# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository. It outlines the project architecture, key components, and development workflow for the "AI-Конспектор" Telegram Web App.

## 1. Project Overview

This project is a **Telegram Web App (TWA)** that allows users to upload large audio files, which are then transcribed by a server-side **Whisper** model and summarized into a structured markdown-конспект by **Google Gemini**.

The project has evolved from a simple Telegram bot into a full-stack application with a distinct **backend (API)** and **frontend (web interface)**. The TWA provides a simple upload interface, overcoming Telegram's 20MB file size limit for bots.

**Core User Flow:**
1.  User opens the Web App inside Telegram.
2.  The interface shows a simple upload button.
3.  User uploads an audio file (up to 300MB).
4.  The backend processes the file in the background.
5.  Upon completion, the user receives the generated summary directly in the Telegram chat.

**Key Business Logic:**
-   Max file size: **300 MB**.
-   User storage quota: **1 GB** per user. All upload attempts must check this quota.

## 2. Architecture

The system is composed of two main, independent components: a Python backend and a modern React frontend.

### 2.1. Backend (FastAPI)

-   **Framework:** Asynchronous web server built with **FastAPI**.
-   **Responsibilities:**
    -   Providing a REST API for the frontend.
    -   **Authentication:** Validating every API request by checking the `initData` string provided by the TWA.
    -   **File Processing:** Handling file uploads, managing user quotas.
    -   **AI Logic:** Running CPU-intensive `faster-whisper` and `Gemini` tasks in a thread pool (`asyncio.to_thread`) to avoid blocking the server.
    -   **Database:** Using **SQLite** with `SQLAlchemy` (async mode) to store metadata about users, files, and their statuses.
    -   **Telegram Bot API:** Sending notifications to users upon task completion.

### 2.2. Frontend (React + TypeScript)

-   **Technology:** **React 18 + TypeScript + Vite** for modern development experience with **Tailwind CSS** for styling.
-   **Responsibilities:**
    -   Simple, responsive UI with drag & drop file upload
    -   **Telegram Integration:** Full TWA integration with theme support and haptic feedback
    -   **Component Architecture:** Modular components (FileUpload)
    -   **State Management:** React hooks for local state management
    -   **API Communication:** Axios-based HTTP client with TypeScript types
    -   **Error Handling:** Comprehensive error boundaries and user feedback

## 3. Directory Structure

The project is organized into `backend` and `react-frontend` directories. A separate `storage` directory holds user data.

```
/
├── backend/
│   ├── .env                   # Environment variables (TELEGRAM_BOT_TOKEN)
│   ├── src/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI app, routers, WebSocket endpoint
│   │   ├── crud.py            # Database interaction functions (Create, Read, Update, Delete)
│   │   ├── models.py          # SQLAlchemy ORM models (User, File)
│   │   ├── schemas.py         # Pydantic schemas for API validation
│   │   ├── processing.py      # Background processing with progress callbacks
│   │   ├── security.py        # Full initData validation with HMAC
│   │   ├── database.py        # AsyncIO SQLAlchemy configuration
│   │   └── connection_manager.py  # WebSocket connection management (Singleton)
│   ├── requirements.txt
│   ├── venv/                  # Python virtual environment
│   └── storage/               # Backend storage for uploaded files
│
├── react-frontend/           # Modern React + TypeScript frontend
│   ├── src/
│   │   ├── api/
│   │   │   └── client.ts      # API client with TypeScript interfaces
│   │   ├── components/
│   │   │   └── FileUpload.tsx # Drag & drop file upload component
│   │   ├── hooks/
│   │   │   └── useTelegramWebApp.ts # Telegram Web App integration
│   │   ├── App.tsx           # Main application component
│   │   ├── main.tsx          # React app entry point
│   │   └── index.css         # Global styles with Tailwind
│   ├── package.json          # Dependencies and build scripts
│   ├── vite.config.ts        # Vite build configuration
│   ├── tailwind.config.js    # Tailwind CSS configuration
│   └── dist/                 # Built production files
│
├── storage/                  # Persistent storage for user files (NOT committed to git)
│   ├── audio_uploads/        # Uploaded audio files (UUID-named)
│   └── summaries/            # Generated markdown summaries
│
├── sertificat/               # SSL certificates for production
│   ├── certificate.crt
│   └── certificate.key
│
├── database.db              # SQLite database file (auto-created)
├── index.html               # Production frontend (built from React)
├── assets/                  # Production JS/CSS bundles
├── CLAUDE.md                # This file
├── NGINX_DEPLOYMENT_REPORT.md # Production deployment documentation
└── nginx_*.txt              # Nginx configuration files
```

## 4. Environment and Running

### 4.1. Backend

1.  **Set up environment:**
    ```bash
    cd backend
    python -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    ```
2.  **Set environment variables:**
    ```bash
    # Create .env file
    echo "TELEGRAM_BOT_TOKEN=your_bot_token_here" > .env
    echo "GEMINI_API_KEY=your_gemini_api_key_here" >> .env
    ```
3.  **Run the development server:**
    ```bash
    # From the 'backend' directory
    uvicorn src.main:app --reload --host 127.0.0.1 --port 8001
    ```

### 4.2. Frontend Development

1.  **Set up React development environment:**
    ```bash
    cd react-frontend
    npm install
    npm run dev  # Starts Vite dev server
    ```

2.  **Build for production:**
    ```bash
    npm run build
    sudo cp -r dist/* /home/<USER>/PROJ/diarizator/
    ```

### 4.3. Production Deployment

The project is deployed on `owa.med-dev-systems.ru` with:
- **Nginx** serving React build files and proxying API requests
- **SSL/HTTPS** with custom certificates
- **FastAPI backend** running on port 8001
- **SQLite database** with auto-creation on startup

**Deployment commands:**
```bash
# Start backend
cd backend && source venv/bin/activate
TELEGRAM_BOT_TOKEN=your_token uvicorn src.main:app --host 127.0.0.1 --port 8001

# Frontend is built React app served by nginx from project root
# Files: index.html, assets/ (JS/CSS bundles)
```

## 5. API Contract (Backend <-> Frontend)

-   **Authentication:** Every request to `/api/*` must include the header `Authorization: Bearer <raw_init_data_string>`.
-   **Base URL:** `https://owa.med-dev-systems.ru/api/v1/`

### 5.1. Implemented Endpoints

-   **`GET /api/v1/users/me`** - Get current user info
    -   **Response:** `200 OK`
    ```json
    {
      "id": "uuid", 
      "telegram_id": 123456789,
      "username": "user123",
      "created_at": "2025-06-22T05:59:54.182Z"
    }
    ```

-   **`GET /api/v1/history`** - Get user's file history (for backend use)
    -   **Response:** `200 OK` with array of file records

-   **`POST /api/v1/upload`** - Upload audio file
    -   **Request:** `multipart/form-data` with file
    -   **Validation:** Max 300MB, user quota 1GB, audio/video only
    -   **Response:** `202 Accepted` + FileSchema or `400/413/429` on error
    -   **Background:** Starts processing with BackgroundTasks

-   **`GET /api/v1/summary/{summary_id}`** - Get processed summary
    -   **Response:** `200 OK` with markdown text in `text/markdown` format

## 6. Current Status & Next Steps

### 6.1. ✅ Completed

**Backend (Fully Functional):**
- ✅ FastAPI application with async SQLAlchemy
- ✅ Complete Telegram initData validation with HMAC
- ✅ **Fixed SQLAlchemy models** with platform-independent GUID type for SQLite compatibility
- ✅ **Improved user creation logic** with race condition handling
- ✅ Full authentication system with `get_current_active_user`
- ✅ Database auto-creation on startup
- ✅ File upload endpoint with validation (size, quota, type)
- ✅ Background processing with BackgroundTasks
- ✅ User quota management (1GB limit)
- ✅ API endpoints: `/users/me`, `/history`, `/upload`, `/summary/{id}`
- ✅ **Professional WebSocket system** for background processing notifications
- ✅ **ConnectionManager (Singleton) for multi-user support**
- ✅ **Progress callback system with decoupled architecture**
- ✅ **Standardized JSON message format**
- ✅ **Debug mode support** for development testing
- ✅ **Production deployment working** on owa.med-dev-systems.ru
- ✅ **Complete /summary/{id} endpoint** returning markdown text

**Frontend (Modern React App):**
- ✅ **React 18 + TypeScript + Vite** modern development stack
- ✅ **Professional Telegram Web App Design** with complete theme integration
- ✅ **Tailwind CSS** with custom Telegram color system and components
- ✅ **Full Telegram Web App integration** with automatic theme adaptation
- ✅ **Modern responsive layout** with clean, minimalist design
- ✅ **Custom component library** (.tg-card, .tg-button, upload orb)
- ✅ **Smooth animations and transitions** (fadeIn, slideUp, bounceIn)
- ✅ **Safe area support** for mobile devices
- ✅ **Drag & drop file upload** with progress indicators and validation
- ✅ **Simple upload-only interface** (no file history complexity)
- ✅ **Comprehensive error handling** and user feedback systems
- ✅ **TypeScript interfaces** for type-safe API communication
- ✅ **Production build optimized** and deployed

**Infrastructure:**
- ✅ **Nginx configuration working** with SSL
- ✅ **Production domain fully functional**: owa.med-dev-systems.ru
- ✅ **API proxying working** correctly through nginx
- ✅ File storage structure (audio_uploads/, summaries/)
- ✅ **Database connectivity resolved** - no more "server unavailable" errors

### 6.2. 🚧 Next Development Tasks

**High Priority:**
1. **Real Whisper Integration** - Replace test processing with actual transcription  
2. **Gemini API Integration** - Implement actual summary generation from transcriptions
3. **Telegram Bot Integration** - Send generated summaries directly to user's chat

**Medium Priority:**
4. **File Type Validation** - More robust audio format detection
5. **Performance Optimization** - Optimize React bundle size and loading  
6. **Remove Debug Code** - Clean up temporary debug authentication for production

**Low Priority:**
7. **Rate Limiting** - API throttling for production
8. **Monitoring** - Logging and metrics collection
9. **Backup Strategy** - Database and file backup system
10. **Favicon** - Add favicon.ico to resolve nginx warnings

## 7. React Frontend Architecture

### 7.1. Component Structure

**Main Components:**
- **`App.tsx`** - Main application with welcome messages and upload interface
- **`FileUpload.tsx`** - Drag & drop upload with progress indicators and validation

**Custom Hooks:**
- **`useTelegramWebApp.ts`** - TWA integration with theme support and haptic feedback

**API Layer:**
- **`api/client.ts`** - Axios-based HTTP client with TypeScript interfaces
- **Type-safe** request/response handling with proper error management

### 7.2. Professional Design System

**Telegram Web App Integration:**
- **Automatic Theme Adaptation:** CSS variables adapt to user's Telegram theme (light/dark)
- **Native Look & Feel:** Uses Telegram's color scheme and design patterns
- **Safe Area Support:** Proper padding for mobile devices with notches
- **Haptic Feedback:** Success/error vibrations for better UX
- **Native Alerts:** Uses Telegram's native alert/confirm dialogs

**Design Components:**
- **`.tg-card`** - Telegram-style cards with proper shadows and borders
- **`.tg-button`** / **`.tg-button-secondary`** - Telegram-style buttons with hover effects
- **`.upload-orb`** - Animated circular upload button with loading states
- **`.loading-spinner`** - Smooth CSS-only loading animation

**Typography & Layout:**
- **System Fonts:** Uses platform-appropriate fonts (-apple-system, Segoe UI, etc.)
- **Responsive Layout:** Header/main/footer structure with proper spacing
- **Modern Grid System:** Flexbox-based layout with max-width containers
- **Consistent Spacing:** 1rem (16px) base unit with Telegram's 12px border radius

**Animations:**
- **`fadeIn`** - Smooth element appearance (0.3s)
- **`slideUp`** - Elements slide from bottom (0.3s)
- **`bounceIn`** - Error states with bounce effect (0.4s)
- **`spin`** - Loading spinners rotation

**Upload Experience:**
- Simple drag & drop upload orb interface
- Upload progress indication during file transfer
- Success/error feedback with haptic responses
- Clean, minimalist design focused on single-file upload