/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-border-style:solid;--tw-leading:initial;--tw-duration:initial}}}.pointer-events-none{pointer-events:none}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.z-10{z-index:10}.z-50{z-index:50}.mx-auto{margin-inline:auto}.flex{display:flex}.hidden{display:none}.max-h-\[80vh\]{max-height:80vh}.min-h-screen{min-height:100vh}.w-full{width:100%}.flex-1{flex:1}.scale-110{--tw-scale-x:110%;--tw-scale-y:110%;--tw-scale-z:110%;scale:var(--tw-scale-x)var(--tw-scale-y)}.cursor-pointer{cursor:pointer}.flex-col{flex-direction:column}.items-center{align-items:center}.items-start{align-items:flex-start}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-y-auto{overflow-y:auto}.rounded-full{border-radius:3.40282e38px}.border{border-style:var(--tw-border-style);border-width:1px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.text-center{text-align:center}.leading-none{--tw-leading:1;line-height:1}.whitespace-pre-wrap{white-space:pre-wrap}.opacity-50{opacity:.5}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}:root{--tg-theme-bg-color:#fff;--tg-theme-text-color:#000;--tg-theme-hint-color:#999;--tg-theme-link-color:#2481cc;--tg-theme-button-color:#2481cc;--tg-theme-button-text-color:#fff;--tg-theme-secondary-bg-color:#f1f1f1;--tg-theme-section-separator-color:#d7d8da;--tg-theme-section-bg-color:#fff;--tg-theme-section-header-text-color:#6d6d71;--tg-theme-subtitle-text-color:#999;--tg-theme-destructive-text-color:#ff3b30;--tg-safe-area-inset-top:0px;--tg-safe-area-inset-bottom:0px;--tg-safe-area-inset-left:0px;--tg-safe-area-inset-right:0px;font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,sans-serif;font-weight:400;line-height:1.5}body{background-color:var(--tg-theme-bg-color);min-height:100vh;color:var(--tg-theme-text-color);-webkit-tap-highlight-color:transparent;margin:0;padding:0;overflow-x:hidden}@layer base{html{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}body{background-color:var(--tg-theme-bg-color);color:var(--tg-theme-text-color)}h1{color:var(--tg-theme-text-color);font-size:1.5rem;font-weight:700;line-height:1.25}h2{color:var(--tg-theme-text-color);font-size:1.25rem;font-weight:600;line-height:1.25}h3{color:var(--tg-theme-text-color);font-size:1.125rem;font-weight:500;line-height:1.25}p{color:var(--tg-theme-text-color);line-height:1.625}a{color:var(--tg-theme-link-color);transition:opacity .2s}a:hover{opacity:.8}input,textarea,select{background-color:var(--tg-theme-bg-color);color:var(--tg-theme-text-color);border-color:var(--tg-theme-section-separator-color)}button{font-weight:500;transition:all .2s}*{scrollbar-width:thin;scrollbar-color:var(--tg-theme-hint-color)transparent}::-webkit-scrollbar{width:4px;height:4px}::-webkit-scrollbar-track{background:0 0}::-webkit-scrollbar-thumb{background-color:var(--tg-theme-hint-color);border-radius:2px}::-webkit-scrollbar-thumb:hover{background-color:var(--tg-theme-text-color)}}@layer components{.tg-card{background-color:var(--tg-theme-secondary-bg-color);border:1px solid var(--tg-theme-section-separator-color);border-radius:12px;padding:1rem;box-shadow:0 1px 3px #0000001f,0 1px 2px #0000003d}.tg-button{background-color:var(--tg-theme-button-color);color:var(--tg-theme-button-text-color);cursor:pointer;border:none;border-radius:12px;padding:.5rem 1rem;font-weight:500;transition:opacity .2s}.tg-button:hover{opacity:.9}.tg-button:active{opacity:.8}.tg-button-secondary{background-color:var(--tg-theme-secondary-bg-color);color:var(--tg-theme-text-color);border:1px solid var(--tg-theme-section-separator-color);cursor:pointer;border-radius:12px;padding:.5rem 1rem;font-weight:500;transition:opacity .2s}.tg-button-secondary:hover{opacity:.9}.tg-button-secondary:active{opacity:.8}.status-processing{color:#1d4ed8;background-color:#eff6ff;border:1px solid #dbeafe}.status-completed{color:#15803d;background-color:#f0fdf4;border:1px solid #dcfce7}.status-error{color:#b91c1c;background-color:#fef2f2;border:1px solid #fee2e2}.status-pending{color:#b45309;background-color:#fffbeb;border:1px solid #fef3c7}.loading-spinner{border:2px solid;border-top-color:#0000;border-radius:50%;width:1rem;height:1rem;animation:1s linear infinite spin;display:inline-block}.upload-area{border:2px dashed var(--tg-theme-section-separator-color);border-radius:12px;transition:colors .2s}.upload-area.drag-over{border-color:var(--tg-theme-link-color);background-color:#eff6ff}.safe-area-top{padding-top:var(--tg-safe-area-inset-top)}.safe-area-bottom{padding-bottom:var(--tg-safe-area-inset-bottom)}.safe-area-left{padding-left:var(--tg-safe-area-inset-left)}.safe-area-right{padding-right:var(--tg-safe-area-inset-right)}}@layer utilities{.text-hint{color:var(--tg-theme-hint-color)}.text-destructive{color:var(--tg-theme-destructive-text-color)}.text-subtitle{color:var(--tg-theme-subtitle-text-color)}.text-section-header{color:var(--tg-theme-section-header-text-color)}.bg-section{background-color:var(--tg-theme-section-bg-color)}.border-separator{border-color:var(--tg-theme-section-separator-color)}.animate-fade-in{animation:.3s ease-out fadeIn}.animate-slide-up{animation:.3s ease-out slideUp}.animate-bounce-in{animation:.4s ease-out bounceIn}}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes slideUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes bounceIn{0%{opacity:0;transform:scale(.3)}50%{opacity:1;transform:scale(1.05)}70%{transform:scale(.9)}to{opacity:1;transform:scale(1)}}@keyframes orbFill{0%{transform:translateY(100%)}to{transform:translateY(0)}}@keyframes shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}.upload-orb{cursor:pointer;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);border-radius:50%;justify-content:center;align-items:center;width:80px;height:80px;transition:all .3s;display:flex;position:relative;overflow:hidden;box-shadow:0 8px 32px #f094334d}.upload-orb:before{content:"";z-index:-1;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888,#a53998,#833ab4,#5b51d8,#405de6) 0 0/200% 200%;border-radius:50%;animation:3s linear infinite shimmer;position:absolute;top:-2px;right:-2px;bottom:-2px;left:-2px}.upload-orb:hover{transform:scale(1.05);box-shadow:0 12px 40px #f0943366}.upload-orb-fill{z-index:1;background:linear-gradient(#405de6,#5b51d8,#833ab4,#a53998,#bc1888,#cc2366);border-radius:50%;width:100%;height:0%;transition:height .8s cubic-bezier(.4,0,.2,1);position:absolute;bottom:0;left:0}.upload-orb.uploading .upload-orb-fill{height:100%;animation:2s ease-in-out infinite alternate orbFill}.upload-orb-content{z-index:2;justify-content:center;align-items:center;display:flex;position:relative}.upload-orb.uploading{background:linear-gradient(45deg,#667eea,#764ba2);animation:2s ease-in-out infinite pulse}@keyframes pulse{0%,to{transform:scale(1)}50%{transform:scale(1.1)}}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-leading{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}
