var uy=Object.defineProperty;var iy=(i,c,r)=>c in i?uy(i,c,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[c]=r;var _f=(i,c,r)=>iy(i,typeof c!="symbol"?c+"":c,r);(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))f(d);new MutationObserver(d=>{for(const h of d)if(h.type==="childList")for(const y of h.addedNodes)y.tagName==="LINK"&&y.rel==="modulepreload"&&f(y)}).observe(document,{childList:!0,subtree:!0});function r(d){const h={};return d.integrity&&(h.integrity=d.integrity),d.referrerPolicy&&(h.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?h.credentials="include":d.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function f(d){if(d.ep)return;d.ep=!0;const h=r(d);fetch(d.href,h)}})();var Df={exports:{}},Mn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yd;function cy(){if(Yd)return Mn;Yd=1;var i=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function r(f,d,h){var y=null;if(h!==void 0&&(y=""+h),d.key!==void 0&&(y=""+d.key),"key"in d){h={};for(var R in d)R!=="key"&&(h[R]=d[R])}else h=d;return d=h.ref,{$$typeof:i,type:f,key:y,ref:d!==void 0?d:null,props:h}}return Mn.Fragment=c,Mn.jsx=r,Mn.jsxs=r,Mn}var Ld;function fy(){return Ld||(Ld=1,Df.exports=cy()),Df.exports}var st=fy(),zf={exports:{}},tt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gd;function sy(){if(Gd)return tt;Gd=1;var i=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),y=Symbol.for("react.context"),R=Symbol.for("react.forward_ref"),_=Symbol.for("react.suspense"),S=Symbol.for("react.memo"),O=Symbol.for("react.lazy"),H=Symbol.iterator;function L(v){return v===null||typeof v!="object"?null:(v=H&&v[H]||v["@@iterator"],typeof v=="function"?v:null)}var Z={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},B=Object.assign,C={};function q(v,x,Y){this.props=v,this.context=x,this.refs=C,this.updater=Y||Z}q.prototype.isReactComponent={},q.prototype.setState=function(v,x){if(typeof v!="object"&&typeof v!="function"&&v!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,v,x,"setState")},q.prototype.forceUpdate=function(v){this.updater.enqueueForceUpdate(this,v,"forceUpdate")};function ut(){}ut.prototype=q.prototype;function F(v,x,Y){this.props=v,this.context=x,this.refs=C,this.updater=Y||Z}var W=F.prototype=new ut;W.constructor=F,B(W,q.prototype),W.isPureReactComponent=!0;var dt=Array.isArray,G={H:null,A:null,T:null,S:null,V:null},rt=Object.prototype.hasOwnProperty;function Tt(v,x,Y,j,V,ct){return Y=ct.ref,{$$typeof:i,type:v,key:x,ref:Y!==void 0?Y:null,props:ct}}function yt(v,x){return Tt(v.type,x,void 0,void 0,void 0,v.props)}function Bt(v){return typeof v=="object"&&v!==null&&v.$$typeof===i}function Zl(v){var x={"=":"=0",":":"=2"};return"$"+v.replace(/[=:]/g,function(Y){return x[Y]})}var qe=/\/+/g;function Zt(v,x){return typeof v=="object"&&v!==null&&v.key!=null?Zl(""+v.key):x.toString(36)}function El(){}function Tl(v){switch(v.status){case"fulfilled":return v.value;case"rejected":throw v.reason;default:switch(typeof v.status=="string"?v.then(El,El):(v.status="pending",v.then(function(x){v.status==="pending"&&(v.status="fulfilled",v.value=x)},function(x){v.status==="pending"&&(v.status="rejected",v.reason=x)})),v.status){case"fulfilled":return v.value;case"rejected":throw v.reason}}throw v}function Vt(v,x,Y,j,V){var ct=typeof v;(ct==="undefined"||ct==="boolean")&&(v=null);var P=!1;if(v===null)P=!0;else switch(ct){case"bigint":case"string":case"number":P=!0;break;case"object":switch(v.$$typeof){case i:case c:P=!0;break;case O:return P=v._init,Vt(P(v._payload),x,Y,j,V)}}if(P)return V=V(v),P=j===""?"."+Zt(v,0):j,dt(V)?(Y="",P!=null&&(Y=P.replace(qe,"$&/")+"/"),Vt(V,x,Y,"",function($e){return $e})):V!=null&&(Bt(V)&&(V=yt(V,Y+(V.key==null||v&&v.key===V.key?"":(""+V.key).replace(qe,"$&/")+"/")+P)),x.push(V)),1;P=0;var ne=j===""?".":j+":";if(dt(v))for(var At=0;At<v.length;At++)j=v[At],ct=ne+Zt(j,At),P+=Vt(j,x,Y,ct,V);else if(At=L(v),typeof At=="function")for(v=At.call(v),At=0;!(j=v.next()).done;)j=j.value,ct=ne+Zt(j,At++),P+=Vt(j,x,Y,ct,V);else if(ct==="object"){if(typeof v.then=="function")return Vt(Tl(v),x,Y,j,V);throw x=String(v),Error("Objects are not valid as a React child (found: "+(x==="[object Object]"?"object with keys {"+Object.keys(v).join(", ")+"}":x)+"). If you meant to render a collection of children, use an array instead.")}return P}function U(v,x,Y){if(v==null)return v;var j=[],V=0;return Vt(v,j,"","",function(ct){return x.call(Y,ct,V++)}),j}function w(v){if(v._status===-1){var x=v._result;x=x(),x.then(function(Y){(v._status===0||v._status===-1)&&(v._status=1,v._result=Y)},function(Y){(v._status===0||v._status===-1)&&(v._status=2,v._result=Y)}),v._status===-1&&(v._status=0,v._result=x)}if(v._status===1)return v._result.default;throw v._result}var k=typeof reportError=="function"?reportError:function(v){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var x=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof v=="object"&&v!==null&&typeof v.message=="string"?String(v.message):String(v),error:v});if(!window.dispatchEvent(x))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",v);return}console.error(v)};function bt(){}return tt.Children={map:U,forEach:function(v,x,Y){U(v,function(){x.apply(this,arguments)},Y)},count:function(v){var x=0;return U(v,function(){x++}),x},toArray:function(v){return U(v,function(x){return x})||[]},only:function(v){if(!Bt(v))throw Error("React.Children.only expected to receive a single React element child.");return v}},tt.Component=q,tt.Fragment=r,tt.Profiler=d,tt.PureComponent=F,tt.StrictMode=f,tt.Suspense=_,tt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=G,tt.__COMPILER_RUNTIME={__proto__:null,c:function(v){return G.H.useMemoCache(v)}},tt.cache=function(v){return function(){return v.apply(null,arguments)}},tt.cloneElement=function(v,x,Y){if(v==null)throw Error("The argument must be a React element, but you passed "+v+".");var j=B({},v.props),V=v.key,ct=void 0;if(x!=null)for(P in x.ref!==void 0&&(ct=void 0),x.key!==void 0&&(V=""+x.key),x)!rt.call(x,P)||P==="key"||P==="__self"||P==="__source"||P==="ref"&&x.ref===void 0||(j[P]=x[P]);var P=arguments.length-2;if(P===1)j.children=Y;else if(1<P){for(var ne=Array(P),At=0;At<P;At++)ne[At]=arguments[At+2];j.children=ne}return Tt(v.type,V,void 0,void 0,ct,j)},tt.createContext=function(v){return v={$$typeof:y,_currentValue:v,_currentValue2:v,_threadCount:0,Provider:null,Consumer:null},v.Provider=v,v.Consumer={$$typeof:h,_context:v},v},tt.createElement=function(v,x,Y){var j,V={},ct=null;if(x!=null)for(j in x.key!==void 0&&(ct=""+x.key),x)rt.call(x,j)&&j!=="key"&&j!=="__self"&&j!=="__source"&&(V[j]=x[j]);var P=arguments.length-2;if(P===1)V.children=Y;else if(1<P){for(var ne=Array(P),At=0;At<P;At++)ne[At]=arguments[At+2];V.children=ne}if(v&&v.defaultProps)for(j in P=v.defaultProps,P)V[j]===void 0&&(V[j]=P[j]);return Tt(v,ct,void 0,void 0,null,V)},tt.createRef=function(){return{current:null}},tt.forwardRef=function(v){return{$$typeof:R,render:v}},tt.isValidElement=Bt,tt.lazy=function(v){return{$$typeof:O,_payload:{_status:-1,_result:v},_init:w}},tt.memo=function(v,x){return{$$typeof:S,type:v,compare:x===void 0?null:x}},tt.startTransition=function(v){var x=G.T,Y={};G.T=Y;try{var j=v(),V=G.S;V!==null&&V(Y,j),typeof j=="object"&&j!==null&&typeof j.then=="function"&&j.then(bt,k)}catch(ct){k(ct)}finally{G.T=x}},tt.unstable_useCacheRefresh=function(){return G.H.useCacheRefresh()},tt.use=function(v){return G.H.use(v)},tt.useActionState=function(v,x,Y){return G.H.useActionState(v,x,Y)},tt.useCallback=function(v,x){return G.H.useCallback(v,x)},tt.useContext=function(v){return G.H.useContext(v)},tt.useDebugValue=function(){},tt.useDeferredValue=function(v,x){return G.H.useDeferredValue(v,x)},tt.useEffect=function(v,x,Y){var j=G.H;if(typeof Y=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return j.useEffect(v,x)},tt.useId=function(){return G.H.useId()},tt.useImperativeHandle=function(v,x,Y){return G.H.useImperativeHandle(v,x,Y)},tt.useInsertionEffect=function(v,x){return G.H.useInsertionEffect(v,x)},tt.useLayoutEffect=function(v,x){return G.H.useLayoutEffect(v,x)},tt.useMemo=function(v,x){return G.H.useMemo(v,x)},tt.useOptimistic=function(v,x){return G.H.useOptimistic(v,x)},tt.useReducer=function(v,x,Y){return G.H.useReducer(v,x,Y)},tt.useRef=function(v){return G.H.useRef(v)},tt.useState=function(v){return G.H.useState(v)},tt.useSyncExternalStore=function(v,x,Y){return G.H.useSyncExternalStore(v,x,Y)},tt.useTransition=function(){return G.H.useTransition()},tt.version="19.1.0",tt}var Xd;function Xf(){return Xd||(Xd=1,zf.exports=sy()),zf.exports}var Qt=Xf(),Uf={exports:{}},Nn={},Mf={exports:{}},Nf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qd;function ry(){return Qd||(Qd=1,function(i){function c(U,w){var k=U.length;U.push(w);t:for(;0<k;){var bt=k-1>>>1,v=U[bt];if(0<d(v,w))U[bt]=w,U[k]=v,k=bt;else break t}}function r(U){return U.length===0?null:U[0]}function f(U){if(U.length===0)return null;var w=U[0],k=U.pop();if(k!==w){U[0]=k;t:for(var bt=0,v=U.length,x=v>>>1;bt<x;){var Y=2*(bt+1)-1,j=U[Y],V=Y+1,ct=U[V];if(0>d(j,k))V<v&&0>d(ct,j)?(U[bt]=ct,U[V]=k,bt=V):(U[bt]=j,U[Y]=k,bt=Y);else if(V<v&&0>d(ct,k))U[bt]=ct,U[V]=k,bt=V;else break t}}return w}function d(U,w){var k=U.sortIndex-w.sortIndex;return k!==0?k:U.id-w.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;i.unstable_now=function(){return h.now()}}else{var y=Date,R=y.now();i.unstable_now=function(){return y.now()-R}}var _=[],S=[],O=1,H=null,L=3,Z=!1,B=!1,C=!1,q=!1,ut=typeof setTimeout=="function"?setTimeout:null,F=typeof clearTimeout=="function"?clearTimeout:null,W=typeof setImmediate<"u"?setImmediate:null;function dt(U){for(var w=r(S);w!==null;){if(w.callback===null)f(S);else if(w.startTime<=U)f(S),w.sortIndex=w.expirationTime,c(_,w);else break;w=r(S)}}function G(U){if(C=!1,dt(U),!B)if(r(_)!==null)B=!0,rt||(rt=!0,Zt());else{var w=r(S);w!==null&&Vt(G,w.startTime-U)}}var rt=!1,Tt=-1,yt=5,Bt=-1;function Zl(){return q?!0:!(i.unstable_now()-Bt<yt)}function qe(){if(q=!1,rt){var U=i.unstable_now();Bt=U;var w=!0;try{t:{B=!1,C&&(C=!1,F(Tt),Tt=-1),Z=!0;var k=L;try{e:{for(dt(U),H=r(_);H!==null&&!(H.expirationTime>U&&Zl());){var bt=H.callback;if(typeof bt=="function"){H.callback=null,L=H.priorityLevel;var v=bt(H.expirationTime<=U);if(U=i.unstable_now(),typeof v=="function"){H.callback=v,dt(U),w=!0;break e}H===r(_)&&f(_),dt(U)}else f(_);H=r(_)}if(H!==null)w=!0;else{var x=r(S);x!==null&&Vt(G,x.startTime-U),w=!1}}break t}finally{H=null,L=k,Z=!1}w=void 0}}finally{w?Zt():rt=!1}}}var Zt;if(typeof W=="function")Zt=function(){W(qe)};else if(typeof MessageChannel<"u"){var El=new MessageChannel,Tl=El.port2;El.port1.onmessage=qe,Zt=function(){Tl.postMessage(null)}}else Zt=function(){ut(qe,0)};function Vt(U,w){Tt=ut(function(){U(i.unstable_now())},w)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(U){U.callback=null},i.unstable_forceFrameRate=function(U){0>U||125<U?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):yt=0<U?Math.floor(1e3/U):5},i.unstable_getCurrentPriorityLevel=function(){return L},i.unstable_next=function(U){switch(L){case 1:case 2:case 3:var w=3;break;default:w=L}var k=L;L=w;try{return U()}finally{L=k}},i.unstable_requestPaint=function(){q=!0},i.unstable_runWithPriority=function(U,w){switch(U){case 1:case 2:case 3:case 4:case 5:break;default:U=3}var k=L;L=U;try{return w()}finally{L=k}},i.unstable_scheduleCallback=function(U,w,k){var bt=i.unstable_now();switch(typeof k=="object"&&k!==null?(k=k.delay,k=typeof k=="number"&&0<k?bt+k:bt):k=bt,U){case 1:var v=-1;break;case 2:v=250;break;case 5:v=1073741823;break;case 4:v=1e4;break;default:v=5e3}return v=k+v,U={id:O++,callback:w,priorityLevel:U,startTime:k,expirationTime:v,sortIndex:-1},k>bt?(U.sortIndex=k,c(S,U),r(_)===null&&U===r(S)&&(C?(F(Tt),Tt=-1):C=!0,Vt(G,k-bt))):(U.sortIndex=v,c(_,U),B||Z||(B=!0,rt||(rt=!0,Zt()))),U},i.unstable_shouldYield=Zl,i.unstable_wrapCallback=function(U){var w=L;return function(){var k=L;L=w;try{return U.apply(this,arguments)}finally{L=k}}}}(Nf)),Nf}var Zd;function oy(){return Zd||(Zd=1,Mf.exports=ry()),Mf.exports}var xf={exports:{}},kt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vd;function dy(){if(Vd)return kt;Vd=1;var i=Xf();function c(_){var S="https://react.dev/errors/"+_;if(1<arguments.length){S+="?args[]="+encodeURIComponent(arguments[1]);for(var O=2;O<arguments.length;O++)S+="&args[]="+encodeURIComponent(arguments[O])}return"Minified React error #"+_+"; visit "+S+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function r(){}var f={d:{f:r,r:function(){throw Error(c(522))},D:r,C:r,L:r,m:r,X:r,S:r,M:r},p:0,findDOMNode:null},d=Symbol.for("react.portal");function h(_,S,O){var H=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:H==null?null:""+H,children:_,containerInfo:S,implementation:O}}var y=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function R(_,S){if(_==="font")return"";if(typeof S=="string")return S==="use-credentials"?S:""}return kt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=f,kt.createPortal=function(_,S){var O=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!S||S.nodeType!==1&&S.nodeType!==9&&S.nodeType!==11)throw Error(c(299));return h(_,S,null,O)},kt.flushSync=function(_){var S=y.T,O=f.p;try{if(y.T=null,f.p=2,_)return _()}finally{y.T=S,f.p=O,f.d.f()}},kt.preconnect=function(_,S){typeof _=="string"&&(S?(S=S.crossOrigin,S=typeof S=="string"?S==="use-credentials"?S:"":void 0):S=null,f.d.C(_,S))},kt.prefetchDNS=function(_){typeof _=="string"&&f.d.D(_)},kt.preinit=function(_,S){if(typeof _=="string"&&S&&typeof S.as=="string"){var O=S.as,H=R(O,S.crossOrigin),L=typeof S.integrity=="string"?S.integrity:void 0,Z=typeof S.fetchPriority=="string"?S.fetchPriority:void 0;O==="style"?f.d.S(_,typeof S.precedence=="string"?S.precedence:void 0,{crossOrigin:H,integrity:L,fetchPriority:Z}):O==="script"&&f.d.X(_,{crossOrigin:H,integrity:L,fetchPriority:Z,nonce:typeof S.nonce=="string"?S.nonce:void 0})}},kt.preinitModule=function(_,S){if(typeof _=="string")if(typeof S=="object"&&S!==null){if(S.as==null||S.as==="script"){var O=R(S.as,S.crossOrigin);f.d.M(_,{crossOrigin:O,integrity:typeof S.integrity=="string"?S.integrity:void 0,nonce:typeof S.nonce=="string"?S.nonce:void 0})}}else S==null&&f.d.M(_)},kt.preload=function(_,S){if(typeof _=="string"&&typeof S=="object"&&S!==null&&typeof S.as=="string"){var O=S.as,H=R(O,S.crossOrigin);f.d.L(_,O,{crossOrigin:H,integrity:typeof S.integrity=="string"?S.integrity:void 0,nonce:typeof S.nonce=="string"?S.nonce:void 0,type:typeof S.type=="string"?S.type:void 0,fetchPriority:typeof S.fetchPriority=="string"?S.fetchPriority:void 0,referrerPolicy:typeof S.referrerPolicy=="string"?S.referrerPolicy:void 0,imageSrcSet:typeof S.imageSrcSet=="string"?S.imageSrcSet:void 0,imageSizes:typeof S.imageSizes=="string"?S.imageSizes:void 0,media:typeof S.media=="string"?S.media:void 0})}},kt.preloadModule=function(_,S){if(typeof _=="string")if(S){var O=R(S.as,S.crossOrigin);f.d.m(_,{as:typeof S.as=="string"&&S.as!=="script"?S.as:void 0,crossOrigin:O,integrity:typeof S.integrity=="string"?S.integrity:void 0})}else f.d.m(_)},kt.requestFormReset=function(_){f.d.r(_)},kt.unstable_batchedUpdates=function(_,S){return _(S)},kt.useFormState=function(_,S,O){return y.H.useFormState(_,S,O)},kt.useFormStatus=function(){return y.H.useHostTransitionStatus()},kt.version="19.1.0",kt}var Kd;function hy(){if(Kd)return xf.exports;Kd=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(c){console.error(c)}}return i(),xf.exports=dy(),xf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jd;function my(){if(Jd)return Nn;Jd=1;var i=oy(),c=Xf(),r=hy();function f(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function h(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function y(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function R(t){if(h(t)!==t)throw Error(f(188))}function _(t){var e=t.alternate;if(!e){if(e=h(t),e===null)throw Error(f(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return R(n),t;if(u===a)return R(n),e;u=u.sibling}throw Error(f(188))}if(l.return!==a.return)l=n,a=u;else{for(var s=!1,o=n.child;o;){if(o===l){s=!0,l=n,a=u;break}if(o===a){s=!0,a=n,l=u;break}o=o.sibling}if(!s){for(o=u.child;o;){if(o===l){s=!0,l=u,a=n;break}if(o===a){s=!0,a=u,l=n;break}o=o.sibling}if(!s)throw Error(f(189))}}if(l.alternate!==a)throw Error(f(190))}if(l.tag!==3)throw Error(f(188));return l.stateNode.current===l?t:e}function S(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=S(t),e!==null)return e;t=t.sibling}return null}var O=Object.assign,H=Symbol.for("react.element"),L=Symbol.for("react.transitional.element"),Z=Symbol.for("react.portal"),B=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),q=Symbol.for("react.profiler"),ut=Symbol.for("react.provider"),F=Symbol.for("react.consumer"),W=Symbol.for("react.context"),dt=Symbol.for("react.forward_ref"),G=Symbol.for("react.suspense"),rt=Symbol.for("react.suspense_list"),Tt=Symbol.for("react.memo"),yt=Symbol.for("react.lazy"),Bt=Symbol.for("react.activity"),Zl=Symbol.for("react.memo_cache_sentinel"),qe=Symbol.iterator;function Zt(t){return t===null||typeof t!="object"?null:(t=qe&&t[qe]||t["@@iterator"],typeof t=="function"?t:null)}var El=Symbol.for("react.client.reference");function Tl(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===El?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case B:return"Fragment";case q:return"Profiler";case C:return"StrictMode";case G:return"Suspense";case rt:return"SuspenseList";case Bt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case Z:return"Portal";case W:return(t.displayName||"Context")+".Provider";case F:return(t._context.displayName||"Context")+".Consumer";case dt:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Tt:return e=t.displayName||null,e!==null?e:Tl(t.type)||"Memo";case yt:e=t._payload,t=t._init;try{return Tl(t(e))}catch{}}return null}var Vt=Array.isArray,U=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,w=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,k={pending:!1,data:null,method:null,action:null},bt=[],v=-1;function x(t){return{current:t}}function Y(t){0>v||(t.current=bt[v],bt[v]=null,v--)}function j(t,e){v++,bt[v]=t.current,t.current=e}var V=x(null),ct=x(null),P=x(null),ne=x(null);function At(t,e){switch(j(P,e),j(ct,t),j(V,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?hd(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=hd(e),t=md(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Y(V),j(V,t)}function $e(){Y(V),Y(ct),Y(P)}function ri(t){t.memoizedState!==null&&j(ne,t);var e=V.current,l=md(e,t.type);e!==l&&(j(ct,t),j(V,l))}function jn(t){ct.current===t&&(Y(V),Y(ct)),ne.current===t&&(Y(ne),Rn._currentValue=k)}var oi=Object.prototype.hasOwnProperty,di=i.unstable_scheduleCallback,hi=i.unstable_cancelCallback,jh=i.unstable_shouldYield,wh=i.unstable_requestPaint,De=i.unstable_now,Yh=i.unstable_getCurrentPriorityLevel,Kf=i.unstable_ImmediatePriority,Jf=i.unstable_UserBlockingPriority,wn=i.unstable_NormalPriority,Lh=i.unstable_LowPriority,kf=i.unstable_IdlePriority,Gh=i.log,Xh=i.unstable_setDisableYieldValue,xa=null,ue=null;function Fe(t){if(typeof Gh=="function"&&Xh(t),ue&&typeof ue.setStrictMode=="function")try{ue.setStrictMode(xa,t)}catch{}}var ie=Math.clz32?Math.clz32:Vh,Qh=Math.log,Zh=Math.LN2;function Vh(t){return t>>>=0,t===0?32:31-(Qh(t)/Zh|0)|0}var Yn=256,Ln=4194304;function Al(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Gn(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,u=t.suspendedLanes,s=t.pingedLanes;t=t.warmLanes;var o=a&134217727;return o!==0?(a=o&~u,a!==0?n=Al(a):(s&=o,s!==0?n=Al(s):l||(l=o&~t,l!==0&&(n=Al(l))))):(o=a&~u,o!==0?n=Al(o):s!==0?n=Al(s):l||(l=a&~t,l!==0&&(n=Al(l)))),n===0?0:e!==0&&e!==n&&(e&u)===0&&(u=n&-n,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:n}function Ha(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Kh(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Wf(){var t=Yn;return Yn<<=1,(Yn&4194048)===0&&(Yn=256),t}function $f(){var t=Ln;return Ln<<=1,(Ln&62914560)===0&&(Ln=4194304),t}function mi(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function qa(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Jh(t,e,l,a,n,u){var s=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var o=t.entanglements,m=t.expirationTimes,E=t.hiddenUpdates;for(l=s&~l;0<l;){var z=31-ie(l),N=1<<z;o[z]=0,m[z]=-1;var T=E[z];if(T!==null)for(E[z]=null,z=0;z<T.length;z++){var A=T[z];A!==null&&(A.lane&=-536870913)}l&=~N}a!==0&&Ff(t,a,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(s&~e))}function Ff(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-ie(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function Pf(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-ie(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function yi(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function vi(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function If(){var t=w.p;return t!==0?t:(t=window.event,t===void 0?32:Hd(t.type))}function kh(t,e){var l=w.p;try{return w.p=t,e()}finally{w.p=l}}var Pe=Math.random().toString(36).slice(2),Kt="__reactFiber$"+Pe,Ft="__reactProps$"+Pe,Vl="__reactContainer$"+Pe,gi="__reactEvents$"+Pe,Wh="__reactListeners$"+Pe,$h="__reactHandles$"+Pe,ts="__reactResources$"+Pe,Ba="__reactMarker$"+Pe;function Si(t){delete t[Kt],delete t[Ft],delete t[gi],delete t[Wh],delete t[$h]}function Kl(t){var e=t[Kt];if(e)return e;for(var l=t.parentNode;l;){if(e=l[Vl]||l[Kt]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=Sd(t);t!==null;){if(l=t[Kt])return l;t=Sd(t)}return e}t=l,l=t.parentNode}return null}function Jl(t){if(t=t[Kt]||t[Vl]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Ca(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(f(33))}function kl(t){var e=t[ts];return e||(e=t[ts]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Ct(t){t[Ba]=!0}var es=new Set,ls={};function Ol(t,e){Wl(t,e),Wl(t+"Capture",e)}function Wl(t,e){for(ls[t]=e,t=0;t<e.length;t++)es.add(e[t])}var Fh=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),as={},ns={};function Ph(t){return oi.call(ns,t)?!0:oi.call(as,t)?!1:Fh.test(t)?ns[t]=!0:(as[t]=!0,!1)}function Xn(t,e,l){if(Ph(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Qn(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function Be(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var bi,us;function $l(t){if(bi===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);bi=e&&e[1]||"",us=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+bi+t+us}var pi=!1;function Ei(t,e){if(!t||pi)return"";pi=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(N,[])}catch(A){var T=A}Reflect.construct(t,[],N)}else{try{N.call()}catch(A){T=A}t.call(N.prototype)}}else{try{throw Error()}catch(A){T=A}(N=t())&&typeof N.catch=="function"&&N.catch(function(){})}}catch(A){if(A&&T&&typeof A.stack=="string")return[A.stack,T.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),s=u[0],o=u[1];if(s&&o){var m=s.split(`
`),E=o.split(`
`);for(n=a=0;a<m.length&&!m[a].includes("DetermineComponentFrameRoot");)a++;for(;n<E.length&&!E[n].includes("DetermineComponentFrameRoot");)n++;if(a===m.length||n===E.length)for(a=m.length-1,n=E.length-1;1<=a&&0<=n&&m[a]!==E[n];)n--;for(;1<=a&&0<=n;a--,n--)if(m[a]!==E[n]){if(a!==1||n!==1)do if(a--,n--,0>n||m[a]!==E[n]){var z=`
`+m[a].replace(" at new "," at ");return t.displayName&&z.includes("<anonymous>")&&(z=z.replace("<anonymous>",t.displayName)),z}while(1<=a&&0<=n);break}}}finally{pi=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?$l(l):""}function Ih(t){switch(t.tag){case 26:case 27:case 5:return $l(t.type);case 16:return $l("Lazy");case 13:return $l("Suspense");case 19:return $l("SuspenseList");case 0:case 15:return Ei(t.type,!1);case 11:return Ei(t.type.render,!1);case 1:return Ei(t.type,!0);case 31:return $l("Activity");default:return""}}function is(t){try{var e="";do e+=Ih(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function me(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function cs(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function tm(t){var e=cs(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(s){a=""+s,u.call(this,s)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(s){a=""+s},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Zn(t){t._valueTracker||(t._valueTracker=tm(t))}function fs(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=cs(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Vn(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var em=/[\n"\\]/g;function ye(t){return t.replace(em,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function Ti(t,e,l,a,n,u,s,o){t.name="",s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"?t.type=s:t.removeAttribute("type"),e!=null?s==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+me(e)):t.value!==""+me(e)&&(t.value=""+me(e)):s!=="submit"&&s!=="reset"||t.removeAttribute("value"),e!=null?Ai(t,s,me(e)):l!=null?Ai(t,s,me(l)):a!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.name=""+me(o):t.removeAttribute("name")}function ss(t,e,l,a,n,u,s,o){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;l=l!=null?""+me(l):"",e=e!=null?""+me(e):l,o||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=o?t.checked:!!a,t.defaultChecked=!!a,s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(t.name=s)}function Ai(t,e,l){e==="number"&&Vn(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function Fl(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+me(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function rs(t,e,l){if(e!=null&&(e=""+me(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+me(l):""}function os(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(f(92));if(Vt(a)){if(1<a.length)throw Error(f(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=me(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function Pl(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var lm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ds(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||lm.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function hs(t,e,l){if(e!=null&&typeof e!="object")throw Error(f(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&ds(t,n,a)}else for(var u in e)e.hasOwnProperty(u)&&ds(t,u,e[u])}function Oi(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var am=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),nm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Kn(t){return nm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Ri=null;function _i(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Il=null,ta=null;function ms(t){var e=Jl(t);if(e&&(t=e.stateNode)){var l=t[Ft]||null;t:switch(t=e.stateNode,e.type){case"input":if(Ti(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+ye(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[Ft]||null;if(!n)throw Error(f(90));Ti(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&fs(a)}break t;case"textarea":rs(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&Fl(t,!!l.multiple,e,!1)}}}var Di=!1;function ys(t,e,l){if(Di)return t(e,l);Di=!0;try{var a=t(e);return a}finally{if(Di=!1,(Il!==null||ta!==null)&&(Nu(),Il&&(e=Il,t=ta,ta=Il=null,ms(e),t)))for(e=0;e<t.length;e++)ms(t[e])}}function ja(t,e){var l=t.stateNode;if(l===null)return null;var a=l[Ft]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(f(231,e,typeof l));return l}var Ce=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),zi=!1;if(Ce)try{var wa={};Object.defineProperty(wa,"passive",{get:function(){zi=!0}}),window.addEventListener("test",wa,wa),window.removeEventListener("test",wa,wa)}catch{zi=!1}var Ie=null,Ui=null,Jn=null;function vs(){if(Jn)return Jn;var t,e=Ui,l=e.length,a,n="value"in Ie?Ie.value:Ie.textContent,u=n.length;for(t=0;t<l&&e[t]===n[t];t++);var s=l-t;for(a=1;a<=s&&e[l-a]===n[u-a];a++);return Jn=n.slice(t,1<a?1-a:void 0)}function kn(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Wn(){return!0}function gs(){return!1}function Pt(t){function e(l,a,n,u,s){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=s,this.currentTarget=null;for(var o in t)t.hasOwnProperty(o)&&(l=t[o],this[o]=l?l(u):u[o]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Wn:gs,this.isPropagationStopped=gs,this}return O(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Wn)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Wn)},persist:function(){},isPersistent:Wn}),e}var Rl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},$n=Pt(Rl),Ya=O({},Rl,{view:0,detail:0}),um=Pt(Ya),Mi,Ni,La,Fn=O({},Ya,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Hi,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==La&&(La&&t.type==="mousemove"?(Mi=t.screenX-La.screenX,Ni=t.screenY-La.screenY):Ni=Mi=0,La=t),Mi)},movementY:function(t){return"movementY"in t?t.movementY:Ni}}),Ss=Pt(Fn),im=O({},Fn,{dataTransfer:0}),cm=Pt(im),fm=O({},Ya,{relatedTarget:0}),xi=Pt(fm),sm=O({},Rl,{animationName:0,elapsedTime:0,pseudoElement:0}),rm=Pt(sm),om=O({},Rl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),dm=Pt(om),hm=O({},Rl,{data:0}),bs=Pt(hm),mm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ym={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},vm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function gm(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=vm[t])?!!e[t]:!1}function Hi(){return gm}var Sm=O({},Ya,{key:function(t){if(t.key){var e=mm[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=kn(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?ym[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Hi,charCode:function(t){return t.type==="keypress"?kn(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?kn(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),bm=Pt(Sm),pm=O({},Fn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),ps=Pt(pm),Em=O({},Ya,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Hi}),Tm=Pt(Em),Am=O({},Rl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Om=Pt(Am),Rm=O({},Fn,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),_m=Pt(Rm),Dm=O({},Rl,{newState:0,oldState:0}),zm=Pt(Dm),Um=[9,13,27,32],qi=Ce&&"CompositionEvent"in window,Ga=null;Ce&&"documentMode"in document&&(Ga=document.documentMode);var Mm=Ce&&"TextEvent"in window&&!Ga,Es=Ce&&(!qi||Ga&&8<Ga&&11>=Ga),Ts=" ",As=!1;function Os(t,e){switch(t){case"keyup":return Um.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Rs(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var ea=!1;function Nm(t,e){switch(t){case"compositionend":return Rs(e);case"keypress":return e.which!==32?null:(As=!0,Ts);case"textInput":return t=e.data,t===Ts&&As?null:t;default:return null}}function xm(t,e){if(ea)return t==="compositionend"||!qi&&Os(t,e)?(t=vs(),Jn=Ui=Ie=null,ea=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Es&&e.locale!=="ko"?null:e.data;default:return null}}var Hm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _s(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Hm[t.type]:e==="textarea"}function Ds(t,e,l,a){Il?ta?ta.push(a):ta=[a]:Il=a,e=ju(e,"onChange"),0<e.length&&(l=new $n("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var Xa=null,Qa=null;function qm(t){fd(t,0)}function Pn(t){var e=Ca(t);if(fs(e))return t}function zs(t,e){if(t==="change")return e}var Us=!1;if(Ce){var Bi;if(Ce){var Ci="oninput"in document;if(!Ci){var Ms=document.createElement("div");Ms.setAttribute("oninput","return;"),Ci=typeof Ms.oninput=="function"}Bi=Ci}else Bi=!1;Us=Bi&&(!document.documentMode||9<document.documentMode)}function Ns(){Xa&&(Xa.detachEvent("onpropertychange",xs),Qa=Xa=null)}function xs(t){if(t.propertyName==="value"&&Pn(Qa)){var e=[];Ds(e,Qa,t,_i(t)),ys(qm,e)}}function Bm(t,e,l){t==="focusin"?(Ns(),Xa=e,Qa=l,Xa.attachEvent("onpropertychange",xs)):t==="focusout"&&Ns()}function Cm(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Pn(Qa)}function jm(t,e){if(t==="click")return Pn(e)}function wm(t,e){if(t==="input"||t==="change")return Pn(e)}function Ym(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ce=typeof Object.is=="function"?Object.is:Ym;function Za(t,e){if(ce(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!oi.call(e,n)||!ce(t[n],e[n]))return!1}return!0}function Hs(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function qs(t,e){var l=Hs(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Hs(l)}}function Bs(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Bs(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Cs(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Vn(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Vn(t.document)}return e}function ji(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Lm=Ce&&"documentMode"in document&&11>=document.documentMode,la=null,wi=null,Va=null,Yi=!1;function js(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Yi||la==null||la!==Vn(a)||(a=la,"selectionStart"in a&&ji(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Va&&Za(Va,a)||(Va=a,a=ju(wi,"onSelect"),0<a.length&&(e=new $n("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=la)))}function _l(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var aa={animationend:_l("Animation","AnimationEnd"),animationiteration:_l("Animation","AnimationIteration"),animationstart:_l("Animation","AnimationStart"),transitionrun:_l("Transition","TransitionRun"),transitionstart:_l("Transition","TransitionStart"),transitioncancel:_l("Transition","TransitionCancel"),transitionend:_l("Transition","TransitionEnd")},Li={},ws={};Ce&&(ws=document.createElement("div").style,"AnimationEvent"in window||(delete aa.animationend.animation,delete aa.animationiteration.animation,delete aa.animationstart.animation),"TransitionEvent"in window||delete aa.transitionend.transition);function Dl(t){if(Li[t])return Li[t];if(!aa[t])return t;var e=aa[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in ws)return Li[t]=e[l];return t}var Ys=Dl("animationend"),Ls=Dl("animationiteration"),Gs=Dl("animationstart"),Gm=Dl("transitionrun"),Xm=Dl("transitionstart"),Qm=Dl("transitioncancel"),Xs=Dl("transitionend"),Qs=new Map,Gi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Gi.push("scrollEnd");function Ae(t,e){Qs.set(t,e),Ol(e,[t])}var Zs=new WeakMap;function ve(t,e){if(typeof t=="object"&&t!==null){var l=Zs.get(t);return l!==void 0?l:(e={value:t,source:e,stack:is(e)},Zs.set(t,e),e)}return{value:t,source:e,stack:is(e)}}var ge=[],na=0,Xi=0;function In(){for(var t=na,e=Xi=na=0;e<t;){var l=ge[e];ge[e++]=null;var a=ge[e];ge[e++]=null;var n=ge[e];ge[e++]=null;var u=ge[e];if(ge[e++]=null,a!==null&&n!==null){var s=a.pending;s===null?n.next=n:(n.next=s.next,s.next=n),a.pending=n}u!==0&&Vs(l,n,u)}}function tu(t,e,l,a){ge[na++]=t,ge[na++]=e,ge[na++]=l,ge[na++]=a,Xi|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Qi(t,e,l,a){return tu(t,e,l,a),eu(t)}function ua(t,e){return tu(t,null,null,e),eu(t)}function Vs(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=t.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&e!==null&&(n=31-ie(l),t=u.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),u):null}function eu(t){if(50<gn)throw gn=0,Wc=null,Error(f(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var ia={};function Zm(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function fe(t,e,l,a){return new Zm(t,e,l,a)}function Zi(t){return t=t.prototype,!(!t||!t.isReactComponent)}function je(t,e){var l=t.alternate;return l===null?(l=fe(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Ks(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function lu(t,e,l,a,n,u){var s=0;if(a=t,typeof t=="function")Zi(t)&&(s=1);else if(typeof t=="string")s=K0(t,l,V.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Bt:return t=fe(31,l,e,n),t.elementType=Bt,t.lanes=u,t;case B:return zl(l.children,n,u,e);case C:s=8,n|=24;break;case q:return t=fe(12,l,e,n|2),t.elementType=q,t.lanes=u,t;case G:return t=fe(13,l,e,n),t.elementType=G,t.lanes=u,t;case rt:return t=fe(19,l,e,n),t.elementType=rt,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case ut:case W:s=10;break t;case F:s=9;break t;case dt:s=11;break t;case Tt:s=14;break t;case yt:s=16,a=null;break t}s=29,l=Error(f(130,t===null?"null":typeof t,"")),a=null}return e=fe(s,l,e,n),e.elementType=t,e.type=a,e.lanes=u,e}function zl(t,e,l,a){return t=fe(7,t,a,e),t.lanes=l,t}function Vi(t,e,l){return t=fe(6,t,null,e),t.lanes=l,t}function Ki(t,e,l){return e=fe(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var ca=[],fa=0,au=null,nu=0,Se=[],be=0,Ul=null,we=1,Ye="";function Ml(t,e){ca[fa++]=nu,ca[fa++]=au,au=t,nu=e}function Js(t,e,l){Se[be++]=we,Se[be++]=Ye,Se[be++]=Ul,Ul=t;var a=we;t=Ye;var n=32-ie(a)-1;a&=~(1<<n),l+=1;var u=32-ie(e)+n;if(30<u){var s=n-n%5;u=(a&(1<<s)-1).toString(32),a>>=s,n-=s,we=1<<32-ie(e)+n|l<<n|a,Ye=u+t}else we=1<<u|l<<n|a,Ye=t}function Ji(t){t.return!==null&&(Ml(t,1),Js(t,1,0))}function ki(t){for(;t===au;)au=ca[--fa],ca[fa]=null,nu=ca[--fa],ca[fa]=null;for(;t===Ul;)Ul=Se[--be],Se[be]=null,Ye=Se[--be],Se[be]=null,we=Se[--be],Se[be]=null}var $t=null,_t=null,ot=!1,Nl=null,ze=!1,Wi=Error(f(519));function xl(t){var e=Error(f(418,""));throw ka(ve(e,t)),Wi}function ks(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[Kt]=t,e[Ft]=a,l){case"dialog":nt("cancel",e),nt("close",e);break;case"iframe":case"object":case"embed":nt("load",e);break;case"video":case"audio":for(l=0;l<bn.length;l++)nt(bn[l],e);break;case"source":nt("error",e);break;case"img":case"image":case"link":nt("error",e),nt("load",e);break;case"details":nt("toggle",e);break;case"input":nt("invalid",e),ss(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Zn(e);break;case"select":nt("invalid",e);break;case"textarea":nt("invalid",e),os(e,a.value,a.defaultValue,a.children),Zn(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||dd(e.textContent,l)?(a.popover!=null&&(nt("beforetoggle",e),nt("toggle",e)),a.onScroll!=null&&nt("scroll",e),a.onScrollEnd!=null&&nt("scrollend",e),a.onClick!=null&&(e.onclick=wu),e=!0):e=!1,e||xl(t)}function Ws(t){for($t=t.return;$t;)switch($t.tag){case 5:case 13:ze=!1;return;case 27:case 3:ze=!0;return;default:$t=$t.return}}function Ka(t){if(t!==$t)return!1;if(!ot)return Ws(t),ot=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||df(t.type,t.memoizedProps)),l=!l),l&&_t&&xl(t),Ws(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(f(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){_t=Re(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}_t=null}}else e===27?(e=_t,yl(t.type)?(t=vf,vf=null,_t=t):_t=e):_t=$t?Re(t.stateNode.nextSibling):null;return!0}function Ja(){_t=$t=null,ot=!1}function $s(){var t=Nl;return t!==null&&(ee===null?ee=t:ee.push.apply(ee,t),Nl=null),t}function ka(t){Nl===null?Nl=[t]:Nl.push(t)}var $i=x(null),Hl=null,Le=null;function tl(t,e,l){j($i,e._currentValue),e._currentValue=l}function Ge(t){t._currentValue=$i.current,Y($i)}function Fi(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function Pi(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var s=n.child;u=u.firstContext;t:for(;u!==null;){var o=u;u=n;for(var m=0;m<e.length;m++)if(o.context===e[m]){u.lanes|=l,o=u.alternate,o!==null&&(o.lanes|=l),Fi(u.return,l,t),a||(s=null);break t}u=o.next}}else if(n.tag===18){if(s=n.return,s===null)throw Error(f(341));s.lanes|=l,u=s.alternate,u!==null&&(u.lanes|=l),Fi(s,l,t),s=null}else s=n.child;if(s!==null)s.return=n;else for(s=n;s!==null;){if(s===t){s=null;break}if(n=s.sibling,n!==null){n.return=s.return,s=n;break}s=s.return}n=s}}function Wa(t,e,l,a){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var s=n.alternate;if(s===null)throw Error(f(387));if(s=s.memoizedProps,s!==null){var o=n.type;ce(n.pendingProps.value,s.value)||(t!==null?t.push(o):t=[o])}}else if(n===ne.current){if(s=n.alternate,s===null)throw Error(f(387));s.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Rn):t=[Rn])}n=n.return}t!==null&&Pi(e,t,l,a),e.flags|=262144}function uu(t){for(t=t.firstContext;t!==null;){if(!ce(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ql(t){Hl=t,Le=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Jt(t){return Fs(Hl,t)}function iu(t,e){return Hl===null&&ql(t),Fs(t,e)}function Fs(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},Le===null){if(t===null)throw Error(f(308));Le=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Le=Le.next=e;return l}var Vm=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},Km=i.unstable_scheduleCallback,Jm=i.unstable_NormalPriority,Ht={$$typeof:W,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ii(){return{controller:new Vm,data:new Map,refCount:0}}function $a(t){t.refCount--,t.refCount===0&&Km(Jm,function(){t.controller.abort()})}var Fa=null,tc=0,sa=0,ra=null;function km(t,e){if(Fa===null){var l=Fa=[];tc=0,sa=lf(),ra={status:"pending",value:void 0,then:function(a){l.push(a)}}}return tc++,e.then(Ps,Ps),e}function Ps(){if(--tc===0&&Fa!==null){ra!==null&&(ra.status="fulfilled");var t=Fa;Fa=null,sa=0,ra=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Wm(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Is=U.S;U.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&km(t,e),Is!==null&&Is(t,e)};var Bl=x(null);function ec(){var t=Bl.current;return t!==null?t:Et.pooledCache}function cu(t,e){e===null?j(Bl,Bl.current):j(Bl,e.pool)}function tr(){var t=ec();return t===null?null:{parent:Ht._currentValue,pool:t}}var Pa=Error(f(460)),er=Error(f(474)),fu=Error(f(542)),lc={then:function(){}};function lr(t){return t=t.status,t==="fulfilled"||t==="rejected"}function su(){}function ar(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(su,su),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ur(t),t;default:if(typeof e.status=="string")e.then(su,su);else{if(t=Et,t!==null&&100<t.shellSuspendCounter)throw Error(f(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,ur(t),t}throw Ia=e,Pa}}var Ia=null;function nr(){if(Ia===null)throw Error(f(459));var t=Ia;return Ia=null,t}function ur(t){if(t===Pa||t===fu)throw Error(f(483))}var el=!1;function ac(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function nc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function ll(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function al(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(ht&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=eu(t),Vs(t,null,l),e}return tu(t,a,e,l),eu(t)}function tn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Pf(t,l)}}function uc(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var s={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=s:u=u.next=s,l=l.next}while(l!==null);u===null?n=u=e:u=u.next=e}else n=u=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var ic=!1;function en(){if(ic){var t=ra;if(t!==null)throw t}}function ln(t,e,l,a){ic=!1;var n=t.updateQueue;el=!1;var u=n.firstBaseUpdate,s=n.lastBaseUpdate,o=n.shared.pending;if(o!==null){n.shared.pending=null;var m=o,E=m.next;m.next=null,s===null?u=E:s.next=E,s=m;var z=t.alternate;z!==null&&(z=z.updateQueue,o=z.lastBaseUpdate,o!==s&&(o===null?z.firstBaseUpdate=E:o.next=E,z.lastBaseUpdate=m))}if(u!==null){var N=n.baseState;s=0,z=E=m=null,o=u;do{var T=o.lane&-536870913,A=T!==o.lane;if(A?(it&T)===T:(a&T)===T){T!==0&&T===sa&&(ic=!0),z!==null&&(z=z.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});t:{var $=t,K=o;T=e;var St=l;switch(K.tag){case 1:if($=K.payload,typeof $=="function"){N=$.call(St,N,T);break t}N=$;break t;case 3:$.flags=$.flags&-65537|128;case 0:if($=K.payload,T=typeof $=="function"?$.call(St,N,T):$,T==null)break t;N=O({},N,T);break t;case 2:el=!0}}T=o.callback,T!==null&&(t.flags|=64,A&&(t.flags|=8192),A=n.callbacks,A===null?n.callbacks=[T]:A.push(T))}else A={lane:T,tag:o.tag,payload:o.payload,callback:o.callback,next:null},z===null?(E=z=A,m=N):z=z.next=A,s|=T;if(o=o.next,o===null){if(o=n.shared.pending,o===null)break;A=o,o=A.next,A.next=null,n.lastBaseUpdate=A,n.shared.pending=null}}while(!0);z===null&&(m=N),n.baseState=m,n.firstBaseUpdate=E,n.lastBaseUpdate=z,u===null&&(n.shared.lanes=0),ol|=s,t.lanes=s,t.memoizedState=N}}function ir(t,e){if(typeof t!="function")throw Error(f(191,t));t.call(e)}function cr(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)ir(l[t],e)}var oa=x(null),ru=x(0);function fr(t,e){t=ke,j(ru,t),j(oa,e),ke=t|e.baseLanes}function cc(){j(ru,ke),j(oa,oa.current)}function fc(){ke=ru.current,Y(oa),Y(ru)}var nl=0,et=null,vt=null,Nt=null,ou=!1,da=!1,Cl=!1,du=0,an=0,ha=null,$m=0;function zt(){throw Error(f(321))}function sc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!ce(t[l],e[l]))return!1;return!0}function rc(t,e,l,a,n,u){return nl=u,et=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,U.H=t===null||t.memoizedState===null?Vr:Kr,Cl=!1,u=l(a,n),Cl=!1,da&&(u=rr(e,l,a,n)),sr(t),u}function sr(t){U.H=Su;var e=vt!==null&&vt.next!==null;if(nl=0,Nt=vt=et=null,ou=!1,an=0,ha=null,e)throw Error(f(300));t===null||jt||(t=t.dependencies,t!==null&&uu(t)&&(jt=!0))}function rr(t,e,l,a){et=t;var n=0;do{if(da&&(ha=null),an=0,da=!1,25<=n)throw Error(f(301));if(n+=1,Nt=vt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}U.H=a0,u=e(l,a)}while(da);return u}function Fm(){var t=U.H,e=t.useState()[0];return e=typeof e.then=="function"?nn(e):e,t=t.useState()[0],(vt!==null?vt.memoizedState:null)!==t&&(et.flags|=1024),e}function oc(){var t=du!==0;return du=0,t}function dc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function hc(t){if(ou){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ou=!1}nl=0,Nt=vt=et=null,da=!1,an=du=0,ha=null}function It(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Nt===null?et.memoizedState=Nt=t:Nt=Nt.next=t,Nt}function xt(){if(vt===null){var t=et.alternate;t=t!==null?t.memoizedState:null}else t=vt.next;var e=Nt===null?et.memoizedState:Nt.next;if(e!==null)Nt=e,vt=t;else{if(t===null)throw et.alternate===null?Error(f(467)):Error(f(310));vt=t,t={memoizedState:vt.memoizedState,baseState:vt.baseState,baseQueue:vt.baseQueue,queue:vt.queue,next:null},Nt===null?et.memoizedState=Nt=t:Nt=Nt.next=t}return Nt}function mc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function nn(t){var e=an;return an+=1,ha===null&&(ha=[]),t=ar(ha,t,e),e=et,(Nt===null?e.memoizedState:Nt.next)===null&&(e=e.alternate,U.H=e===null||e.memoizedState===null?Vr:Kr),t}function hu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return nn(t);if(t.$$typeof===W)return Jt(t)}throw Error(f(438,String(t)))}function yc(t){var e=null,l=et.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=et.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=mc(),et.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=Zl;return e.index++,l}function Xe(t,e){return typeof e=="function"?e(t):e}function mu(t){var e=xt();return vc(e,vt,t)}function vc(t,e,l){var a=t.queue;if(a===null)throw Error(f(311));a.lastRenderedReducer=l;var n=t.baseQueue,u=a.pending;if(u!==null){if(n!==null){var s=n.next;n.next=u.next,u.next=s}e.baseQueue=n=u,a.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var o=s=null,m=null,E=e,z=!1;do{var N=E.lane&-536870913;if(N!==E.lane?(it&N)===N:(nl&N)===N){var T=E.revertLane;if(T===0)m!==null&&(m=m.next={lane:0,revertLane:0,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null}),N===sa&&(z=!0);else if((nl&T)===T){E=E.next,T===sa&&(z=!0);continue}else N={lane:0,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},m===null?(o=m=N,s=u):m=m.next=N,et.lanes|=T,ol|=T;N=E.action,Cl&&l(u,N),u=E.hasEagerState?E.eagerState:l(u,N)}else T={lane:N,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},m===null?(o=m=T,s=u):m=m.next=T,et.lanes|=N,ol|=N;E=E.next}while(E!==null&&E!==e);if(m===null?s=u:m.next=o,!ce(u,t.memoizedState)&&(jt=!0,z&&(l=ra,l!==null)))throw l;t.memoizedState=u,t.baseState=s,t.baseQueue=m,a.lastRenderedState=u}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function gc(t){var e=xt(),l=e.queue;if(l===null)throw Error(f(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,u=e.memoizedState;if(n!==null){l.pending=null;var s=n=n.next;do u=t(u,s.action),s=s.next;while(s!==n);ce(u,e.memoizedState)||(jt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,a]}function or(t,e,l){var a=et,n=xt(),u=ot;if(u){if(l===void 0)throw Error(f(407));l=l()}else l=e();var s=!ce((vt||n).memoizedState,l);s&&(n.memoizedState=l,jt=!0),n=n.queue;var o=mr.bind(null,a,n,t);if(un(2048,8,o,[t]),n.getSnapshot!==e||s||Nt!==null&&Nt.memoizedState.tag&1){if(a.flags|=2048,ma(9,yu(),hr.bind(null,a,n,l,e),null),Et===null)throw Error(f(349));u||(nl&124)!==0||dr(a,e,l)}return l}function dr(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=et.updateQueue,e===null?(e=mc(),et.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function hr(t,e,l,a){e.value=l,e.getSnapshot=a,yr(e)&&vr(t)}function mr(t,e,l){return l(function(){yr(e)&&vr(t)})}function yr(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!ce(t,l)}catch{return!0}}function vr(t){var e=ua(t,2);e!==null&&he(e,t,2)}function Sc(t){var e=It();if(typeof t=="function"){var l=t;if(t=l(),Cl){Fe(!0);try{l()}finally{Fe(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xe,lastRenderedState:t},e}function gr(t,e,l,a){return t.baseState=l,vc(t,vt,typeof a=="function"?a:Xe)}function Pm(t,e,l,a,n){if(gu(t))throw Error(f(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(s){u.listeners.push(s)}};U.T!==null?l(!0):u.isTransition=!1,a(u),l=e.pending,l===null?(u.next=e.pending=u,Sr(e,u)):(u.next=l.next,e.pending=l.next=u)}}function Sr(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var u=U.T,s={};U.T=s;try{var o=l(n,a),m=U.S;m!==null&&m(s,o),br(t,e,o)}catch(E){bc(t,e,E)}finally{U.T=u}}else try{u=l(n,a),br(t,e,u)}catch(E){bc(t,e,E)}}function br(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){pr(t,e,a)},function(a){return bc(t,e,a)}):pr(t,e,l)}function pr(t,e,l){e.status="fulfilled",e.value=l,Er(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,Sr(t,l)))}function bc(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,Er(e),e=e.next;while(e!==a)}t.action=null}function Er(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Tr(t,e){return e}function Ar(t,e){if(ot){var l=Et.formState;if(l!==null){t:{var a=et;if(ot){if(_t){e:{for(var n=_t,u=ze;n.nodeType!==8;){if(!u){n=null;break e}if(n=Re(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){_t=Re(n.nextSibling),a=n.data==="F!";break t}}xl(a)}a=!1}a&&(e=l[0])}}return l=It(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Tr,lastRenderedState:e},l.queue=a,l=Xr.bind(null,et,a),a.dispatch=l,a=Sc(!1),u=Oc.bind(null,et,!1,a.queue),a=It(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=Pm.bind(null,et,n,u,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Or(t){var e=xt();return Rr(e,vt,t)}function Rr(t,e,l){if(e=vc(t,e,Tr)[0],t=mu(Xe)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=nn(e)}catch(s){throw s===Pa?fu:s}else a=e;e=xt();var n=e.queue,u=n.dispatch;return l!==e.memoizedState&&(et.flags|=2048,ma(9,yu(),Im.bind(null,n,l),null)),[a,u,t]}function Im(t,e){t.action=e}function _r(t){var e=xt(),l=vt;if(l!==null)return Rr(e,l,t);xt(),e=e.memoizedState,l=xt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function ma(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=et.updateQueue,e===null&&(e=mc(),et.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function yu(){return{destroy:void 0,resource:void 0}}function Dr(){return xt().memoizedState}function vu(t,e,l,a){var n=It();a=a===void 0?null:a,et.flags|=t,n.memoizedState=ma(1|e,yu(),l,a)}function un(t,e,l,a){var n=xt();a=a===void 0?null:a;var u=n.memoizedState.inst;vt!==null&&a!==null&&sc(a,vt.memoizedState.deps)?n.memoizedState=ma(e,u,l,a):(et.flags|=t,n.memoizedState=ma(1|e,u,l,a))}function zr(t,e){vu(8390656,8,t,e)}function Ur(t,e){un(2048,8,t,e)}function Mr(t,e){return un(4,2,t,e)}function Nr(t,e){return un(4,4,t,e)}function xr(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Hr(t,e,l){l=l!=null?l.concat([t]):null,un(4,4,xr.bind(null,e,t),l)}function pc(){}function qr(t,e){var l=xt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&sc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function Br(t,e){var l=xt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&sc(e,a[1]))return a[0];if(a=t(),Cl){Fe(!0);try{t()}finally{Fe(!1)}}return l.memoizedState=[a,e],a}function Ec(t,e,l){return l===void 0||(nl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Yo(),et.lanes|=t,ol|=t,l)}function Cr(t,e,l,a){return ce(l,e)?l:oa.current!==null?(t=Ec(t,l,a),ce(t,e)||(jt=!0),t):(nl&42)===0?(jt=!0,t.memoizedState=l):(t=Yo(),et.lanes|=t,ol|=t,e)}function jr(t,e,l,a,n){var u=w.p;w.p=u!==0&&8>u?u:8;var s=U.T,o={};U.T=o,Oc(t,!1,e,l);try{var m=n(),E=U.S;if(E!==null&&E(o,m),m!==null&&typeof m=="object"&&typeof m.then=="function"){var z=Wm(m,a);cn(t,e,z,de(t))}else cn(t,e,a,de(t))}catch(N){cn(t,e,{then:function(){},status:"rejected",reason:N},de())}finally{w.p=u,U.T=s}}function t0(){}function Tc(t,e,l,a){if(t.tag!==5)throw Error(f(476));var n=wr(t).queue;jr(t,n,e,k,l===null?t0:function(){return Yr(t),l(a)})}function wr(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:k,baseState:k,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xe,lastRenderedState:k},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xe,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Yr(t){var e=wr(t).next.queue;cn(t,e,{},de())}function Ac(){return Jt(Rn)}function Lr(){return xt().memoizedState}function Gr(){return xt().memoizedState}function e0(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=de();t=ll(l);var a=al(e,t,l);a!==null&&(he(a,e,l),tn(a,e,l)),e={cache:Ii()},t.payload=e;return}e=e.return}}function l0(t,e,l){var a=de();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},gu(t)?Qr(e,l):(l=Qi(t,e,l,a),l!==null&&(he(l,t,a),Zr(l,e,a)))}function Xr(t,e,l){var a=de();cn(t,e,l,a)}function cn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(gu(t))Qr(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var s=e.lastRenderedState,o=u(s,l);if(n.hasEagerState=!0,n.eagerState=o,ce(o,s))return tu(t,e,n,0),Et===null&&In(),!1}catch{}finally{}if(l=Qi(t,e,n,a),l!==null)return he(l,t,a),Zr(l,e,a),!0}return!1}function Oc(t,e,l,a){if(a={lane:2,revertLane:lf(),action:a,hasEagerState:!1,eagerState:null,next:null},gu(t)){if(e)throw Error(f(479))}else e=Qi(t,l,a,2),e!==null&&he(e,t,2)}function gu(t){var e=t.alternate;return t===et||e!==null&&e===et}function Qr(t,e){da=ou=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Zr(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Pf(t,l)}}var Su={readContext:Jt,use:hu,useCallback:zt,useContext:zt,useEffect:zt,useImperativeHandle:zt,useLayoutEffect:zt,useInsertionEffect:zt,useMemo:zt,useReducer:zt,useRef:zt,useState:zt,useDebugValue:zt,useDeferredValue:zt,useTransition:zt,useSyncExternalStore:zt,useId:zt,useHostTransitionStatus:zt,useFormState:zt,useActionState:zt,useOptimistic:zt,useMemoCache:zt,useCacheRefresh:zt},Vr={readContext:Jt,use:hu,useCallback:function(t,e){return It().memoizedState=[t,e===void 0?null:e],t},useContext:Jt,useEffect:zr,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,vu(4194308,4,xr.bind(null,e,t),l)},useLayoutEffect:function(t,e){return vu(4194308,4,t,e)},useInsertionEffect:function(t,e){vu(4,2,t,e)},useMemo:function(t,e){var l=It();e=e===void 0?null:e;var a=t();if(Cl){Fe(!0);try{t()}finally{Fe(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=It();if(l!==void 0){var n=l(e);if(Cl){Fe(!0);try{l(e)}finally{Fe(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=l0.bind(null,et,t),[a.memoizedState,t]},useRef:function(t){var e=It();return t={current:t},e.memoizedState=t},useState:function(t){t=Sc(t);var e=t.queue,l=Xr.bind(null,et,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:pc,useDeferredValue:function(t,e){var l=It();return Ec(l,t,e)},useTransition:function(){var t=Sc(!1);return t=jr.bind(null,et,t.queue,!0,!1),It().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=et,n=It();if(ot){if(l===void 0)throw Error(f(407));l=l()}else{if(l=e(),Et===null)throw Error(f(349));(it&124)!==0||dr(a,e,l)}n.memoizedState=l;var u={value:l,getSnapshot:e};return n.queue=u,zr(mr.bind(null,a,u,t),[t]),a.flags|=2048,ma(9,yu(),hr.bind(null,a,u,l,e),null),l},useId:function(){var t=It(),e=Et.identifierPrefix;if(ot){var l=Ye,a=we;l=(a&~(1<<32-ie(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=du++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=$m++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Ac,useFormState:Ar,useActionState:Ar,useOptimistic:function(t){var e=It();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=Oc.bind(null,et,!0,l),l.dispatch=e,[t,e]},useMemoCache:yc,useCacheRefresh:function(){return It().memoizedState=e0.bind(null,et)}},Kr={readContext:Jt,use:hu,useCallback:qr,useContext:Jt,useEffect:Ur,useImperativeHandle:Hr,useInsertionEffect:Mr,useLayoutEffect:Nr,useMemo:Br,useReducer:mu,useRef:Dr,useState:function(){return mu(Xe)},useDebugValue:pc,useDeferredValue:function(t,e){var l=xt();return Cr(l,vt.memoizedState,t,e)},useTransition:function(){var t=mu(Xe)[0],e=xt().memoizedState;return[typeof t=="boolean"?t:nn(t),e]},useSyncExternalStore:or,useId:Lr,useHostTransitionStatus:Ac,useFormState:Or,useActionState:Or,useOptimistic:function(t,e){var l=xt();return gr(l,vt,t,e)},useMemoCache:yc,useCacheRefresh:Gr},a0={readContext:Jt,use:hu,useCallback:qr,useContext:Jt,useEffect:Ur,useImperativeHandle:Hr,useInsertionEffect:Mr,useLayoutEffect:Nr,useMemo:Br,useReducer:gc,useRef:Dr,useState:function(){return gc(Xe)},useDebugValue:pc,useDeferredValue:function(t,e){var l=xt();return vt===null?Ec(l,t,e):Cr(l,vt.memoizedState,t,e)},useTransition:function(){var t=gc(Xe)[0],e=xt().memoizedState;return[typeof t=="boolean"?t:nn(t),e]},useSyncExternalStore:or,useId:Lr,useHostTransitionStatus:Ac,useFormState:_r,useActionState:_r,useOptimistic:function(t,e){var l=xt();return vt!==null?gr(l,vt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:yc,useCacheRefresh:Gr},ya=null,fn=0;function bu(t){var e=fn;return fn+=1,ya===null&&(ya=[]),ar(ya,t,e)}function sn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function pu(t,e){throw e.$$typeof===H?Error(f(525)):(t=Object.prototype.toString.call(e),Error(f(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Jr(t){var e=t._init;return e(t._payload)}function kr(t){function e(b,g){if(t){var p=b.deletions;p===null?(b.deletions=[g],b.flags|=16):p.push(g)}}function l(b,g){if(!t)return null;for(;g!==null;)e(b,g),g=g.sibling;return null}function a(b){for(var g=new Map;b!==null;)b.key!==null?g.set(b.key,b):g.set(b.index,b),b=b.sibling;return g}function n(b,g){return b=je(b,g),b.index=0,b.sibling=null,b}function u(b,g,p){return b.index=p,t?(p=b.alternate,p!==null?(p=p.index,p<g?(b.flags|=67108866,g):p):(b.flags|=67108866,g)):(b.flags|=1048576,g)}function s(b){return t&&b.alternate===null&&(b.flags|=67108866),b}function o(b,g,p,M){return g===null||g.tag!==6?(g=Vi(p,b.mode,M),g.return=b,g):(g=n(g,p),g.return=b,g)}function m(b,g,p,M){var X=p.type;return X===B?z(b,g,p.props.children,M,p.key):g!==null&&(g.elementType===X||typeof X=="object"&&X!==null&&X.$$typeof===yt&&Jr(X)===g.type)?(g=n(g,p.props),sn(g,p),g.return=b,g):(g=lu(p.type,p.key,p.props,null,b.mode,M),sn(g,p),g.return=b,g)}function E(b,g,p,M){return g===null||g.tag!==4||g.stateNode.containerInfo!==p.containerInfo||g.stateNode.implementation!==p.implementation?(g=Ki(p,b.mode,M),g.return=b,g):(g=n(g,p.children||[]),g.return=b,g)}function z(b,g,p,M,X){return g===null||g.tag!==7?(g=zl(p,b.mode,M,X),g.return=b,g):(g=n(g,p),g.return=b,g)}function N(b,g,p){if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return g=Vi(""+g,b.mode,p),g.return=b,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case L:return p=lu(g.type,g.key,g.props,null,b.mode,p),sn(p,g),p.return=b,p;case Z:return g=Ki(g,b.mode,p),g.return=b,g;case yt:var M=g._init;return g=M(g._payload),N(b,g,p)}if(Vt(g)||Zt(g))return g=zl(g,b.mode,p,null),g.return=b,g;if(typeof g.then=="function")return N(b,bu(g),p);if(g.$$typeof===W)return N(b,iu(b,g),p);pu(b,g)}return null}function T(b,g,p,M){var X=g!==null?g.key:null;if(typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint")return X!==null?null:o(b,g,""+p,M);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case L:return p.key===X?m(b,g,p,M):null;case Z:return p.key===X?E(b,g,p,M):null;case yt:return X=p._init,p=X(p._payload),T(b,g,p,M)}if(Vt(p)||Zt(p))return X!==null?null:z(b,g,p,M,null);if(typeof p.then=="function")return T(b,g,bu(p),M);if(p.$$typeof===W)return T(b,g,iu(b,p),M);pu(b,p)}return null}function A(b,g,p,M,X){if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return b=b.get(p)||null,o(g,b,""+M,X);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case L:return b=b.get(M.key===null?p:M.key)||null,m(g,b,M,X);case Z:return b=b.get(M.key===null?p:M.key)||null,E(g,b,M,X);case yt:var lt=M._init;return M=lt(M._payload),A(b,g,p,M,X)}if(Vt(M)||Zt(M))return b=b.get(p)||null,z(g,b,M,X,null);if(typeof M.then=="function")return A(b,g,p,bu(M),X);if(M.$$typeof===W)return A(b,g,p,iu(g,M),X);pu(g,M)}return null}function $(b,g,p,M){for(var X=null,lt=null,Q=g,J=g=0,Yt=null;Q!==null&&J<p.length;J++){Q.index>J?(Yt=Q,Q=null):Yt=Q.sibling;var ft=T(b,Q,p[J],M);if(ft===null){Q===null&&(Q=Yt);break}t&&Q&&ft.alternate===null&&e(b,Q),g=u(ft,g,J),lt===null?X=ft:lt.sibling=ft,lt=ft,Q=Yt}if(J===p.length)return l(b,Q),ot&&Ml(b,J),X;if(Q===null){for(;J<p.length;J++)Q=N(b,p[J],M),Q!==null&&(g=u(Q,g,J),lt===null?X=Q:lt.sibling=Q,lt=Q);return ot&&Ml(b,J),X}for(Q=a(Q);J<p.length;J++)Yt=A(Q,b,J,p[J],M),Yt!==null&&(t&&Yt.alternate!==null&&Q.delete(Yt.key===null?J:Yt.key),g=u(Yt,g,J),lt===null?X=Yt:lt.sibling=Yt,lt=Yt);return t&&Q.forEach(function(pl){return e(b,pl)}),ot&&Ml(b,J),X}function K(b,g,p,M){if(p==null)throw Error(f(151));for(var X=null,lt=null,Q=g,J=g=0,Yt=null,ft=p.next();Q!==null&&!ft.done;J++,ft=p.next()){Q.index>J?(Yt=Q,Q=null):Yt=Q.sibling;var pl=T(b,Q,ft.value,M);if(pl===null){Q===null&&(Q=Yt);break}t&&Q&&pl.alternate===null&&e(b,Q),g=u(pl,g,J),lt===null?X=pl:lt.sibling=pl,lt=pl,Q=Yt}if(ft.done)return l(b,Q),ot&&Ml(b,J),X;if(Q===null){for(;!ft.done;J++,ft=p.next())ft=N(b,ft.value,M),ft!==null&&(g=u(ft,g,J),lt===null?X=ft:lt.sibling=ft,lt=ft);return ot&&Ml(b,J),X}for(Q=a(Q);!ft.done;J++,ft=p.next())ft=A(Q,b,J,ft.value,M),ft!==null&&(t&&ft.alternate!==null&&Q.delete(ft.key===null?J:ft.key),g=u(ft,g,J),lt===null?X=ft:lt.sibling=ft,lt=ft);return t&&Q.forEach(function(ny){return e(b,ny)}),ot&&Ml(b,J),X}function St(b,g,p,M){if(typeof p=="object"&&p!==null&&p.type===B&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case L:t:{for(var X=p.key;g!==null;){if(g.key===X){if(X=p.type,X===B){if(g.tag===7){l(b,g.sibling),M=n(g,p.props.children),M.return=b,b=M;break t}}else if(g.elementType===X||typeof X=="object"&&X!==null&&X.$$typeof===yt&&Jr(X)===g.type){l(b,g.sibling),M=n(g,p.props),sn(M,p),M.return=b,b=M;break t}l(b,g);break}else e(b,g);g=g.sibling}p.type===B?(M=zl(p.props.children,b.mode,M,p.key),M.return=b,b=M):(M=lu(p.type,p.key,p.props,null,b.mode,M),sn(M,p),M.return=b,b=M)}return s(b);case Z:t:{for(X=p.key;g!==null;){if(g.key===X)if(g.tag===4&&g.stateNode.containerInfo===p.containerInfo&&g.stateNode.implementation===p.implementation){l(b,g.sibling),M=n(g,p.children||[]),M.return=b,b=M;break t}else{l(b,g);break}else e(b,g);g=g.sibling}M=Ki(p,b.mode,M),M.return=b,b=M}return s(b);case yt:return X=p._init,p=X(p._payload),St(b,g,p,M)}if(Vt(p))return $(b,g,p,M);if(Zt(p)){if(X=Zt(p),typeof X!="function")throw Error(f(150));return p=X.call(p),K(b,g,p,M)}if(typeof p.then=="function")return St(b,g,bu(p),M);if(p.$$typeof===W)return St(b,g,iu(b,p),M);pu(b,p)}return typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint"?(p=""+p,g!==null&&g.tag===6?(l(b,g.sibling),M=n(g,p),M.return=b,b=M):(l(b,g),M=Vi(p,b.mode,M),M.return=b,b=M),s(b)):l(b,g)}return function(b,g,p,M){try{fn=0;var X=St(b,g,p,M);return ya=null,X}catch(Q){if(Q===Pa||Q===fu)throw Q;var lt=fe(29,Q,null,b.mode);return lt.lanes=M,lt.return=b,lt}finally{}}}var va=kr(!0),Wr=kr(!1),pe=x(null),Ue=null;function ul(t){var e=t.alternate;j(qt,qt.current&1),j(pe,t),Ue===null&&(e===null||oa.current!==null||e.memoizedState!==null)&&(Ue=t)}function $r(t){if(t.tag===22){if(j(qt,qt.current),j(pe,t),Ue===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ue=t)}}else il()}function il(){j(qt,qt.current),j(pe,pe.current)}function Qe(t){Y(pe),Ue===t&&(Ue=null),Y(qt)}var qt=x(0);function Eu(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||yf(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Rc(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:O({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var _c={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=de(),n=ll(a);n.payload=e,l!=null&&(n.callback=l),e=al(t,n,a),e!==null&&(he(e,t,a),tn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=de(),n=ll(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=al(t,n,a),e!==null&&(he(e,t,a),tn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=de(),a=ll(l);a.tag=2,e!=null&&(a.callback=e),e=al(t,a,l),e!==null&&(he(e,t,l),tn(e,t,l))}};function Fr(t,e,l,a,n,u,s){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,u,s):e.prototype&&e.prototype.isPureReactComponent?!Za(l,a)||!Za(n,u):!0}function Pr(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&_c.enqueueReplaceState(e,e.state,null)}function jl(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=O({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var Tu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Ir(t){Tu(t)}function to(t){console.error(t)}function eo(t){Tu(t)}function Au(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function lo(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Dc(t,e,l){return l=ll(l),l.tag=3,l.payload={element:null},l.callback=function(){Au(t,e)},l}function ao(t){return t=ll(t),t.tag=3,t}function no(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;t.payload=function(){return n(u)},t.callback=function(){lo(e,l,a)}}var s=l.stateNode;s!==null&&typeof s.componentDidCatch=="function"&&(t.callback=function(){lo(e,l,a),typeof n!="function"&&(dl===null?dl=new Set([this]):dl.add(this));var o=a.stack;this.componentDidCatch(a.value,{componentStack:o!==null?o:""})})}function n0(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&Wa(e,l,n,!0),l=pe.current,l!==null){switch(l.tag){case 13:return Ue===null?Fc():l.alternate===null&&Dt===0&&(Dt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===lc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),Ic(t,a,n)),!1;case 22:return l.flags|=65536,a===lc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),Ic(t,a,n)),!1}throw Error(f(435,l.tag))}return Ic(t,a,n),Fc(),!1}if(ot)return e=pe.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Wi&&(t=Error(f(422),{cause:a}),ka(ve(t,l)))):(a!==Wi&&(e=Error(f(423),{cause:a}),ka(ve(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=ve(a,l),n=Dc(t.stateNode,a,n),uc(t,n),Dt!==4&&(Dt=2)),!1;var u=Error(f(520),{cause:a});if(u=ve(u,l),vn===null?vn=[u]:vn.push(u),Dt!==4&&(Dt=2),e===null)return!0;a=ve(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=Dc(l.stateNode,a,t),uc(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(dl===null||!dl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=ao(n),no(n,t,l,a),uc(l,n),!1}l=l.return}while(l!==null);return!1}var uo=Error(f(461)),jt=!1;function Lt(t,e,l,a){e.child=t===null?Wr(e,null,l,a):va(e,t.child,l,a)}function io(t,e,l,a,n){l=l.render;var u=e.ref;if("ref"in a){var s={};for(var o in a)o!=="ref"&&(s[o]=a[o])}else s=a;return ql(e),a=rc(t,e,l,s,u,n),o=oc(),t!==null&&!jt?(dc(t,e,n),Ze(t,e,n)):(ot&&o&&Ji(e),e.flags|=1,Lt(t,e,a,n),e.child)}function co(t,e,l,a,n){if(t===null){var u=l.type;return typeof u=="function"&&!Zi(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,fo(t,e,u,a,n)):(t=lu(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Bc(t,n)){var s=u.memoizedProps;if(l=l.compare,l=l!==null?l:Za,l(s,a)&&t.ref===e.ref)return Ze(t,e,n)}return e.flags|=1,t=je(u,a),t.ref=e.ref,t.return=e,e.child=t}function fo(t,e,l,a,n){if(t!==null){var u=t.memoizedProps;if(Za(u,a)&&t.ref===e.ref)if(jt=!1,e.pendingProps=a=u,Bc(t,n))(t.flags&131072)!==0&&(jt=!0);else return e.lanes=t.lanes,Ze(t,e,n)}return zc(t,e,l,a,n)}function so(t,e,l){var a=e.pendingProps,n=a.children,u=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~a}else e.childLanes=0,e.child=null;return ro(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&cu(e,u!==null?u.cachePool:null),u!==null?fr(e,u):cc(),$r(e);else return e.lanes=e.childLanes=536870912,ro(t,e,u!==null?u.baseLanes|l:l,l)}else u!==null?(cu(e,u.cachePool),fr(e,u),il(),e.memoizedState=null):(t!==null&&cu(e,null),cc(),il());return Lt(t,e,n,l),e.child}function ro(t,e,l,a){var n=ec();return n=n===null?null:{parent:Ht._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&cu(e,null),cc(),$r(e),t!==null&&Wa(t,e,a,!0),null}function Ou(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(f(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function zc(t,e,l,a,n){return ql(e),l=rc(t,e,l,a,void 0,n),a=oc(),t!==null&&!jt?(dc(t,e,n),Ze(t,e,n)):(ot&&a&&Ji(e),e.flags|=1,Lt(t,e,l,n),e.child)}function oo(t,e,l,a,n,u){return ql(e),e.updateQueue=null,l=rr(e,a,l,n),sr(t),a=oc(),t!==null&&!jt?(dc(t,e,u),Ze(t,e,u)):(ot&&a&&Ji(e),e.flags|=1,Lt(t,e,l,u),e.child)}function ho(t,e,l,a,n){if(ql(e),e.stateNode===null){var u=ia,s=l.contextType;typeof s=="object"&&s!==null&&(u=Jt(s)),u=new l(a,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=_c,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=a,u.state=e.memoizedState,u.refs={},ac(e),s=l.contextType,u.context=typeof s=="object"&&s!==null?Jt(s):ia,u.state=e.memoizedState,s=l.getDerivedStateFromProps,typeof s=="function"&&(Rc(e,l,s,a),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(s=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),s!==u.state&&_c.enqueueReplaceState(u,u.state,null),ln(e,a,u,n),en(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){u=e.stateNode;var o=e.memoizedProps,m=jl(l,o);u.props=m;var E=u.context,z=l.contextType;s=ia,typeof z=="object"&&z!==null&&(s=Jt(z));var N=l.getDerivedStateFromProps;z=typeof N=="function"||typeof u.getSnapshotBeforeUpdate=="function",o=e.pendingProps!==o,z||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(o||E!==s)&&Pr(e,u,a,s),el=!1;var T=e.memoizedState;u.state=T,ln(e,a,u,n),en(),E=e.memoizedState,o||T!==E||el?(typeof N=="function"&&(Rc(e,l,N,a),E=e.memoizedState),(m=el||Fr(e,l,m,a,T,E,s))?(z||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=E),u.props=a,u.state=E,u.context=s,a=m):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{u=e.stateNode,nc(t,e),s=e.memoizedProps,z=jl(l,s),u.props=z,N=e.pendingProps,T=u.context,E=l.contextType,m=ia,typeof E=="object"&&E!==null&&(m=Jt(E)),o=l.getDerivedStateFromProps,(E=typeof o=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(s!==N||T!==m)&&Pr(e,u,a,m),el=!1,T=e.memoizedState,u.state=T,ln(e,a,u,n),en();var A=e.memoizedState;s!==N||T!==A||el||t!==null&&t.dependencies!==null&&uu(t.dependencies)?(typeof o=="function"&&(Rc(e,l,o,a),A=e.memoizedState),(z=el||Fr(e,l,z,a,T,A,m)||t!==null&&t.dependencies!==null&&uu(t.dependencies))?(E||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,A,m),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,A,m)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||s===t.memoizedProps&&T===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||s===t.memoizedProps&&T===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=A),u.props=a,u.state=A,u.context=m,a=z):(typeof u.componentDidUpdate!="function"||s===t.memoizedProps&&T===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||s===t.memoizedProps&&T===t.memoizedState||(e.flags|=1024),a=!1)}return u=a,Ou(t,e),a=(e.flags&128)!==0,u||a?(u=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&a?(e.child=va(e,t.child,null,n),e.child=va(e,null,l,n)):Lt(t,e,l,n),e.memoizedState=u.state,t=e.child):t=Ze(t,e,n),t}function mo(t,e,l,a){return Ja(),e.flags|=256,Lt(t,e,l,a),e.child}var Uc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Mc(t){return{baseLanes:t,cachePool:tr()}}function Nc(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=Ee),t}function yo(t,e,l){var a=e.pendingProps,n=!1,u=(e.flags&128)!==0,s;if((s=u)||(s=t!==null&&t.memoizedState===null?!1:(qt.current&2)!==0),s&&(n=!0,e.flags&=-129),s=(e.flags&32)!==0,e.flags&=-33,t===null){if(ot){if(n?ul(e):il(),ot){var o=_t,m;if(m=o){t:{for(m=o,o=ze;m.nodeType!==8;){if(!o){o=null;break t}if(m=Re(m.nextSibling),m===null){o=null;break t}}o=m}o!==null?(e.memoizedState={dehydrated:o,treeContext:Ul!==null?{id:we,overflow:Ye}:null,retryLane:536870912,hydrationErrors:null},m=fe(18,null,null,0),m.stateNode=o,m.return=e,e.child=m,$t=e,_t=null,m=!0):m=!1}m||xl(e)}if(o=e.memoizedState,o!==null&&(o=o.dehydrated,o!==null))return yf(o)?e.lanes=32:e.lanes=536870912,null;Qe(e)}return o=a.children,a=a.fallback,n?(il(),n=e.mode,o=Ru({mode:"hidden",children:o},n),a=zl(a,n,l,null),o.return=e,a.return=e,o.sibling=a,e.child=o,n=e.child,n.memoizedState=Mc(l),n.childLanes=Nc(t,s,l),e.memoizedState=Uc,a):(ul(e),xc(e,o))}if(m=t.memoizedState,m!==null&&(o=m.dehydrated,o!==null)){if(u)e.flags&256?(ul(e),e.flags&=-257,e=Hc(t,e,l)):e.memoizedState!==null?(il(),e.child=t.child,e.flags|=128,e=null):(il(),n=a.fallback,o=e.mode,a=Ru({mode:"visible",children:a.children},o),n=zl(n,o,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,va(e,t.child,null,l),a=e.child,a.memoizedState=Mc(l),a.childLanes=Nc(t,s,l),e.memoizedState=Uc,e=n);else if(ul(e),yf(o)){if(s=o.nextSibling&&o.nextSibling.dataset,s)var E=s.dgst;s=E,a=Error(f(419)),a.stack="",a.digest=s,ka({value:a,source:null,stack:null}),e=Hc(t,e,l)}else if(jt||Wa(t,e,l,!1),s=(l&t.childLanes)!==0,jt||s){if(s=Et,s!==null&&(a=l&-l,a=(a&42)!==0?1:yi(a),a=(a&(s.suspendedLanes|l))!==0?0:a,a!==0&&a!==m.retryLane))throw m.retryLane=a,ua(t,a),he(s,t,a),uo;o.data==="$?"||Fc(),e=Hc(t,e,l)}else o.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=m.treeContext,_t=Re(o.nextSibling),$t=e,ot=!0,Nl=null,ze=!1,t!==null&&(Se[be++]=we,Se[be++]=Ye,Se[be++]=Ul,we=t.id,Ye=t.overflow,Ul=e),e=xc(e,a.children),e.flags|=4096);return e}return n?(il(),n=a.fallback,o=e.mode,m=t.child,E=m.sibling,a=je(m,{mode:"hidden",children:a.children}),a.subtreeFlags=m.subtreeFlags&65011712,E!==null?n=je(E,n):(n=zl(n,o,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,o=t.child.memoizedState,o===null?o=Mc(l):(m=o.cachePool,m!==null?(E=Ht._currentValue,m=m.parent!==E?{parent:E,pool:E}:m):m=tr(),o={baseLanes:o.baseLanes|l,cachePool:m}),n.memoizedState=o,n.childLanes=Nc(t,s,l),e.memoizedState=Uc,a):(ul(e),l=t.child,t=l.sibling,l=je(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(s=e.deletions,s===null?(e.deletions=[t],e.flags|=16):s.push(t)),e.child=l,e.memoizedState=null,l)}function xc(t,e){return e=Ru({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Ru(t,e){return t=fe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Hc(t,e,l){return va(e,t.child,null,l),t=xc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function vo(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Fi(t.return,e,l)}function qc(t,e,l,a,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function go(t,e,l){var a=e.pendingProps,n=a.revealOrder,u=a.tail;if(Lt(t,e,a.children,l),a=qt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&vo(t,l,e);else if(t.tag===19)vo(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(j(qt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&Eu(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),qc(e,!1,n,l,u);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Eu(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}qc(e,!0,l,null,u);break;case"together":qc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function Ze(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),ol|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(Wa(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(f(153));if(e.child!==null){for(t=e.child,l=je(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=je(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Bc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&uu(t)))}function u0(t,e,l){switch(e.tag){case 3:At(e,e.stateNode.containerInfo),tl(e,Ht,t.memoizedState.cache),Ja();break;case 27:case 5:ri(e);break;case 4:At(e,e.stateNode.containerInfo);break;case 10:tl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(ul(e),e.flags|=128,null):(l&e.child.childLanes)!==0?yo(t,e,l):(ul(e),t=Ze(t,e,l),t!==null?t.sibling:null);ul(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(Wa(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return go(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),j(qt,qt.current),a)break;return null;case 22:case 23:return e.lanes=0,so(t,e,l);case 24:tl(e,Ht,t.memoizedState.cache)}return Ze(t,e,l)}function So(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)jt=!0;else{if(!Bc(t,l)&&(e.flags&128)===0)return jt=!1,u0(t,e,l);jt=(t.flags&131072)!==0}else jt=!1,ot&&(e.flags&1048576)!==0&&Js(e,nu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Zi(a)?(t=jl(a,t),e.tag=1,e=ho(null,e,a,t,l)):(e.tag=0,e=zc(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===dt){e.tag=11,e=io(null,e,a,t,l);break t}else if(n===Tt){e.tag=14,e=co(null,e,a,t,l);break t}}throw e=Tl(a)||a,Error(f(306,e,""))}}return e;case 0:return zc(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=jl(a,e.pendingProps),ho(t,e,a,n,l);case 3:t:{if(At(e,e.stateNode.containerInfo),t===null)throw Error(f(387));a=e.pendingProps;var u=e.memoizedState;n=u.element,nc(t,e),ln(e,a,null,l);var s=e.memoizedState;if(a=s.cache,tl(e,Ht,a),a!==u.cache&&Pi(e,[Ht],l,!0),en(),a=s.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:s.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=mo(t,e,a,l);break t}else if(a!==n){n=ve(Error(f(424)),e),ka(n),e=mo(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(_t=Re(t.firstChild),$t=e,ot=!0,Nl=null,ze=!0,l=Wr(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Ja(),a===n){e=Ze(t,e,l);break t}Lt(t,e,a,l)}e=e.child}return e;case 26:return Ou(t,e),t===null?(l=Td(e.type,null,e.pendingProps,null))?e.memoizedState=l:ot||(l=e.type,t=e.pendingProps,a=Yu(P.current).createElement(l),a[Kt]=e,a[Ft]=t,Xt(a,l,t),Ct(a),e.stateNode=a):e.memoizedState=Td(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return ri(e),t===null&&ot&&(a=e.stateNode=bd(e.type,e.pendingProps,P.current),$t=e,ze=!0,n=_t,yl(e.type)?(vf=n,_t=Re(a.firstChild)):_t=n),Lt(t,e,e.pendingProps.children,l),Ou(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&ot&&((n=a=_t)&&(a=H0(a,e.type,e.pendingProps,ze),a!==null?(e.stateNode=a,$t=e,_t=Re(a.firstChild),ze=!1,n=!0):n=!1),n||xl(e)),ri(e),n=e.type,u=e.pendingProps,s=t!==null?t.memoizedProps:null,a=u.children,df(n,u)?a=null:s!==null&&df(n,s)&&(e.flags|=32),e.memoizedState!==null&&(n=rc(t,e,Fm,null,null,l),Rn._currentValue=n),Ou(t,e),Lt(t,e,a,l),e.child;case 6:return t===null&&ot&&((t=l=_t)&&(l=q0(l,e.pendingProps,ze),l!==null?(e.stateNode=l,$t=e,_t=null,t=!0):t=!1),t||xl(e)),null;case 13:return yo(t,e,l);case 4:return At(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=va(e,null,a,l):Lt(t,e,a,l),e.child;case 11:return io(t,e,e.type,e.pendingProps,l);case 7:return Lt(t,e,e.pendingProps,l),e.child;case 8:return Lt(t,e,e.pendingProps.children,l),e.child;case 12:return Lt(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,tl(e,e.type,a.value),Lt(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,ql(e),n=Jt(n),a=a(n),e.flags|=1,Lt(t,e,a,l),e.child;case 14:return co(t,e,e.type,e.pendingProps,l);case 15:return fo(t,e,e.type,e.pendingProps,l);case 19:return go(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=Ru(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=je(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return so(t,e,l);case 24:return ql(e),a=Jt(Ht),t===null?(n=ec(),n===null&&(n=Et,u=Ii(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),e.memoizedState={parent:a,cache:n},ac(e),tl(e,Ht,n)):((t.lanes&l)!==0&&(nc(t,e),ln(e,null,null,l),en()),n=t.memoizedState,u=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),tl(e,Ht,a)):(a=u.cache,tl(e,Ht,a),a!==n.cache&&Pi(e,[Ht],l,!0))),Lt(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(f(156,e.tag))}function Ve(t){t.flags|=4}function bo(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Dd(e)){if(e=pe.current,e!==null&&((it&4194048)===it?Ue!==null:(it&62914560)!==it&&(it&536870912)===0||e!==Ue))throw Ia=lc,er;t.flags|=8192}}function _u(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?$f():536870912,t.lanes|=e,pa|=e)}function rn(t,e){if(!ot)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Rt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function i0(t,e,l){var a=e.pendingProps;switch(ki(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Rt(e),null;case 1:return Rt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),Ge(Ht),$e(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(Ka(e)?Ve(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,$s())),Rt(e),null;case 26:return l=e.memoizedState,t===null?(Ve(e),l!==null?(Rt(e),bo(e,l)):(Rt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(Ve(e),Rt(e),bo(e,l)):(Rt(e),e.flags&=-16777217):(t.memoizedProps!==a&&Ve(e),Rt(e),e.flags&=-16777217),null;case 27:jn(e),l=P.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&Ve(e);else{if(!a){if(e.stateNode===null)throw Error(f(166));return Rt(e),null}t=V.current,Ka(e)?ks(e):(t=bd(n,a,l),e.stateNode=t,Ve(e))}return Rt(e),null;case 5:if(jn(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&Ve(e);else{if(!a){if(e.stateNode===null)throw Error(f(166));return Rt(e),null}if(t=V.current,Ka(e))ks(e);else{switch(n=Yu(P.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[Kt]=e,t[Ft]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(Xt(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Ve(e)}}return Rt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&Ve(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(f(166));if(t=P.current,Ka(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=$t,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[Kt]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||dd(t.nodeValue,l)),t||xl(e)}else t=Yu(t).createTextNode(a),t[Kt]=e,e.stateNode=t}return Rt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=Ka(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(f(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(f(317));n[Kt]=e}else Ja(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Rt(e),n=!1}else n=$s(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(Qe(e),e):(Qe(e),null)}if(Qe(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),_u(e,e.updateQueue),Rt(e),null;case 4:return $e(),t===null&&cf(e.stateNode.containerInfo),Rt(e),null;case 10:return Ge(e.type),Rt(e),null;case 19:if(Y(qt),n=e.memoizedState,n===null)return Rt(e),null;if(a=(e.flags&128)!==0,u=n.rendering,u===null)if(a)rn(n,!1);else{if(Dt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Eu(t),u!==null){for(e.flags|=128,rn(n,!1),t=u.updateQueue,e.updateQueue=t,_u(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Ks(l,t),l=l.sibling;return j(qt,qt.current&1|2),e.child}t=t.sibling}n.tail!==null&&De()>Uu&&(e.flags|=128,a=!0,rn(n,!1),e.lanes=4194304)}else{if(!a)if(t=Eu(u),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,_u(e,t),rn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!ot)return Rt(e),null}else 2*De()-n.renderingStartTime>Uu&&l!==536870912&&(e.flags|=128,a=!0,rn(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=De(),e.sibling=null,t=qt.current,j(qt,a?t&1|2:t&1),e):(Rt(e),null);case 22:case 23:return Qe(e),fc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Rt(e),e.subtreeFlags&6&&(e.flags|=8192)):Rt(e),l=e.updateQueue,l!==null&&_u(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&Y(Bl),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Ge(Ht),Rt(e),null;case 25:return null;case 30:return null}throw Error(f(156,e.tag))}function c0(t,e){switch(ki(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Ge(Ht),$e(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return jn(e),null;case 13:if(Qe(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(f(340));Ja()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Y(qt),null;case 4:return $e(),null;case 10:return Ge(e.type),null;case 22:case 23:return Qe(e),fc(),t!==null&&Y(Bl),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Ge(Ht),null;case 25:return null;default:return null}}function po(t,e){switch(ki(e),e.tag){case 3:Ge(Ht),$e();break;case 26:case 27:case 5:jn(e);break;case 4:$e();break;case 13:Qe(e);break;case 19:Y(qt);break;case 10:Ge(e.type);break;case 22:case 23:Qe(e),fc(),t!==null&&Y(Bl);break;case 24:Ge(Ht)}}function on(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var u=l.create,s=l.inst;a=u(),s.destroy=a}l=l.next}while(l!==n)}}catch(o){pt(e,e.return,o)}}function cl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&t)===t){var s=a.inst,o=s.destroy;if(o!==void 0){s.destroy=void 0,n=e;var m=l,E=o;try{E()}catch(z){pt(n,m,z)}}}a=a.next}while(a!==u)}}catch(z){pt(e,e.return,z)}}function Eo(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{cr(e,l)}catch(a){pt(t,t.return,a)}}}function To(t,e,l){l.props=jl(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){pt(t,e,a)}}function dn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){pt(t,e,n)}}function Me(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){pt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){pt(t,e,n)}else l.current=null}function Ao(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){pt(t,t.return,n)}}function Cc(t,e,l){try{var a=t.stateNode;z0(a,t.type,l,e),a[Ft]=e}catch(n){pt(t,t.return,n)}}function Oo(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&yl(t.type)||t.tag===4}function jc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Oo(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&yl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function wc(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=wu));else if(a!==4&&(a===27&&yl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(wc(t,e,l),t=t.sibling;t!==null;)wc(t,e,l),t=t.sibling}function Du(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&yl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(Du(t,e,l),t=t.sibling;t!==null;)Du(t,e,l),t=t.sibling}function Ro(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Xt(e,a,l),e[Kt]=t,e[Ft]=l}catch(u){pt(t,t.return,u)}}var Ke=!1,Ut=!1,Yc=!1,_o=typeof WeakSet=="function"?WeakSet:Set,wt=null;function f0(t,e){if(t=t.containerInfo,rf=Vu,t=Cs(t),ji(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var s=0,o=-1,m=-1,E=0,z=0,N=t,T=null;e:for(;;){for(var A;N!==l||n!==0&&N.nodeType!==3||(o=s+n),N!==u||a!==0&&N.nodeType!==3||(m=s+a),N.nodeType===3&&(s+=N.nodeValue.length),(A=N.firstChild)!==null;)T=N,N=A;for(;;){if(N===t)break e;if(T===l&&++E===n&&(o=s),T===u&&++z===a&&(m=s),(A=N.nextSibling)!==null)break;N=T,T=N.parentNode}N=A}l=o===-1||m===-1?null:{start:o,end:m}}else l=null}l=l||{start:0,end:0}}else l=null;for(of={focusedElem:t,selectionRange:l},Vu=!1,wt=e;wt!==null;)if(e=wt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,wt=t;else for(;wt!==null;){switch(e=wt,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var $=jl(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate($,u),a.__reactInternalSnapshotBeforeUpdate=t}catch(K){pt(l,l.return,K)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)mf(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":mf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(f(163))}if(t=e.sibling,t!==null){t.return=e.return,wt=t;break}wt=e.return}}function Do(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:fl(t,l),a&4&&on(5,l);break;case 1:if(fl(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(s){pt(l,l.return,s)}else{var n=jl(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(s){pt(l,l.return,s)}}a&64&&Eo(l),a&512&&dn(l,l.return);break;case 3:if(fl(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{cr(t,e)}catch(s){pt(l,l.return,s)}}break;case 27:e===null&&a&4&&Ro(l);case 26:case 5:fl(t,l),e===null&&a&4&&Ao(l),a&512&&dn(l,l.return);break;case 12:fl(t,l);break;case 13:fl(t,l),a&4&&Mo(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=g0.bind(null,l),B0(t,l))));break;case 22:if(a=l.memoizedState!==null||Ke,!a){e=e!==null&&e.memoizedState!==null||Ut,n=Ke;var u=Ut;Ke=a,(Ut=e)&&!u?sl(t,l,(l.subtreeFlags&8772)!==0):fl(t,l),Ke=n,Ut=u}break;case 30:break;default:fl(t,l)}}function zo(t){var e=t.alternate;e!==null&&(t.alternate=null,zo(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Si(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ot=null,te=!1;function Je(t,e,l){for(l=l.child;l!==null;)Uo(t,e,l),l=l.sibling}function Uo(t,e,l){if(ue&&typeof ue.onCommitFiberUnmount=="function")try{ue.onCommitFiberUnmount(xa,l)}catch{}switch(l.tag){case 26:Ut||Me(l,e),Je(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Ut||Me(l,e);var a=Ot,n=te;yl(l.type)&&(Ot=l.stateNode,te=!1),Je(t,e,l),En(l.stateNode),Ot=a,te=n;break;case 5:Ut||Me(l,e);case 6:if(a=Ot,n=te,Ot=null,Je(t,e,l),Ot=a,te=n,Ot!==null)if(te)try{(Ot.nodeType===9?Ot.body:Ot.nodeName==="HTML"?Ot.ownerDocument.body:Ot).removeChild(l.stateNode)}catch(u){pt(l,e,u)}else try{Ot.removeChild(l.stateNode)}catch(u){pt(l,e,u)}break;case 18:Ot!==null&&(te?(t=Ot,gd(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),Un(t)):gd(Ot,l.stateNode));break;case 4:a=Ot,n=te,Ot=l.stateNode.containerInfo,te=!0,Je(t,e,l),Ot=a,te=n;break;case 0:case 11:case 14:case 15:Ut||cl(2,l,e),Ut||cl(4,l,e),Je(t,e,l);break;case 1:Ut||(Me(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&To(l,e,a)),Je(t,e,l);break;case 21:Je(t,e,l);break;case 22:Ut=(a=Ut)||l.memoizedState!==null,Je(t,e,l),Ut=a;break;default:Je(t,e,l)}}function Mo(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Un(t)}catch(l){pt(e,e.return,l)}}function s0(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new _o),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new _o),e;default:throw Error(f(435,t.tag))}}function Lc(t,e){var l=s0(t);e.forEach(function(a){var n=S0.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function se(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=t,s=e,o=s;t:for(;o!==null;){switch(o.tag){case 27:if(yl(o.type)){Ot=o.stateNode,te=!1;break t}break;case 5:Ot=o.stateNode,te=!1;break t;case 3:case 4:Ot=o.stateNode.containerInfo,te=!0;break t}o=o.return}if(Ot===null)throw Error(f(160));Uo(u,s,n),Ot=null,te=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)No(e,t),e=e.sibling}var Oe=null;function No(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:se(e,t),re(t),a&4&&(cl(3,t,t.return),on(3,t),cl(5,t,t.return));break;case 1:se(e,t),re(t),a&512&&(Ut||l===null||Me(l,l.return)),a&64&&Ke&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Oe;if(se(e,t),re(t),a&512&&(Ut||l===null||Me(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Ba]||u[Kt]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),Xt(u,a,l),u[Kt]=t,Ct(u),a=u;break t;case"link":var s=Rd("link","href",n).get(a+(l.href||""));if(s){for(var o=0;o<s.length;o++)if(u=s[o],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){s.splice(o,1);break e}}u=n.createElement(a),Xt(u,a,l),n.head.appendChild(u);break;case"meta":if(s=Rd("meta","content",n).get(a+(l.content||""))){for(o=0;o<s.length;o++)if(u=s[o],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){s.splice(o,1);break e}}u=n.createElement(a),Xt(u,a,l),n.head.appendChild(u);break;default:throw Error(f(468,a))}u[Kt]=t,Ct(u),a=u}t.stateNode=a}else _d(n,t.type,t.stateNode);else t.stateNode=Od(n,a,t.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?_d(n,t.type,t.stateNode):Od(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Cc(t,t.memoizedProps,l.memoizedProps)}break;case 27:se(e,t),re(t),a&512&&(Ut||l===null||Me(l,l.return)),l!==null&&a&4&&Cc(t,t.memoizedProps,l.memoizedProps);break;case 5:if(se(e,t),re(t),a&512&&(Ut||l===null||Me(l,l.return)),t.flags&32){n=t.stateNode;try{Pl(n,"")}catch(A){pt(t,t.return,A)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,Cc(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Yc=!0);break;case 6:if(se(e,t),re(t),a&4){if(t.stateNode===null)throw Error(f(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(A){pt(t,t.return,A)}}break;case 3:if(Xu=null,n=Oe,Oe=Lu(e.containerInfo),se(e,t),Oe=n,re(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Un(e.containerInfo)}catch(A){pt(t,t.return,A)}Yc&&(Yc=!1,xo(t));break;case 4:a=Oe,Oe=Lu(t.stateNode.containerInfo),se(e,t),re(t),Oe=a;break;case 12:se(e,t),re(t);break;case 13:se(e,t),re(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Kc=De()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Lc(t,a)));break;case 22:n=t.memoizedState!==null;var m=l!==null&&l.memoizedState!==null,E=Ke,z=Ut;if(Ke=E||n,Ut=z||m,se(e,t),Ut=z,Ke=E,re(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||m||Ke||Ut||wl(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){m=l=e;try{if(u=m.stateNode,n)s=u.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none";else{o=m.stateNode;var N=m.memoizedProps.style,T=N!=null&&N.hasOwnProperty("display")?N.display:null;o.style.display=T==null||typeof T=="boolean"?"":(""+T).trim()}}catch(A){pt(m,m.return,A)}}}else if(e.tag===6){if(l===null){m=e;try{m.stateNode.nodeValue=n?"":m.memoizedProps}catch(A){pt(m,m.return,A)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Lc(t,l))));break;case 19:se(e,t),re(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Lc(t,a)));break;case 30:break;case 21:break;default:se(e,t),re(t)}}function re(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(Oo(a)){l=a;break}a=a.return}if(l==null)throw Error(f(160));switch(l.tag){case 27:var n=l.stateNode,u=jc(t);Du(t,u,n);break;case 5:var s=l.stateNode;l.flags&32&&(Pl(s,""),l.flags&=-33);var o=jc(t);Du(t,o,s);break;case 3:case 4:var m=l.stateNode.containerInfo,E=jc(t);wc(t,E,m);break;default:throw Error(f(161))}}catch(z){pt(t,t.return,z)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function xo(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;xo(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function fl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Do(t,e.alternate,e),e=e.sibling}function wl(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:cl(4,e,e.return),wl(e);break;case 1:Me(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&To(e,e.return,l),wl(e);break;case 27:En(e.stateNode);case 26:case 5:Me(e,e.return),wl(e);break;case 22:e.memoizedState===null&&wl(e);break;case 30:wl(e);break;default:wl(e)}t=t.sibling}}function sl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,u=e,s=u.flags;switch(u.tag){case 0:case 11:case 15:sl(n,u,l),on(4,u);break;case 1:if(sl(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(E){pt(a,a.return,E)}if(a=u,n=a.updateQueue,n!==null){var o=a.stateNode;try{var m=n.shared.hiddenCallbacks;if(m!==null)for(n.shared.hiddenCallbacks=null,n=0;n<m.length;n++)ir(m[n],o)}catch(E){pt(a,a.return,E)}}l&&s&64&&Eo(u),dn(u,u.return);break;case 27:Ro(u);case 26:case 5:sl(n,u,l),l&&a===null&&s&4&&Ao(u),dn(u,u.return);break;case 12:sl(n,u,l);break;case 13:sl(n,u,l),l&&s&4&&Mo(n,u);break;case 22:u.memoizedState===null&&sl(n,u,l),dn(u,u.return);break;case 30:break;default:sl(n,u,l)}e=e.sibling}}function Gc(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&$a(l))}function Xc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&$a(t))}function Ne(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ho(t,e,l,a),e=e.sibling}function Ho(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:Ne(t,e,l,a),n&2048&&on(9,e);break;case 1:Ne(t,e,l,a);break;case 3:Ne(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&$a(t)));break;case 12:if(n&2048){Ne(t,e,l,a),t=e.stateNode;try{var u=e.memoizedProps,s=u.id,o=u.onPostCommit;typeof o=="function"&&o(s,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(m){pt(e,e.return,m)}}else Ne(t,e,l,a);break;case 13:Ne(t,e,l,a);break;case 23:break;case 22:u=e.stateNode,s=e.alternate,e.memoizedState!==null?u._visibility&2?Ne(t,e,l,a):hn(t,e):u._visibility&2?Ne(t,e,l,a):(u._visibility|=2,ga(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Gc(s,e);break;case 24:Ne(t,e,l,a),n&2048&&Xc(e.alternate,e);break;default:Ne(t,e,l,a)}}function ga(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,s=e,o=l,m=a,E=s.flags;switch(s.tag){case 0:case 11:case 15:ga(u,s,o,m,n),on(8,s);break;case 23:break;case 22:var z=s.stateNode;s.memoizedState!==null?z._visibility&2?ga(u,s,o,m,n):hn(u,s):(z._visibility|=2,ga(u,s,o,m,n)),n&&E&2048&&Gc(s.alternate,s);break;case 24:ga(u,s,o,m,n),n&&E&2048&&Xc(s.alternate,s);break;default:ga(u,s,o,m,n)}e=e.sibling}}function hn(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:hn(l,a),n&2048&&Gc(a.alternate,a);break;case 24:hn(l,a),n&2048&&Xc(a.alternate,a);break;default:hn(l,a)}e=e.sibling}}var mn=8192;function Sa(t){if(t.subtreeFlags&mn)for(t=t.child;t!==null;)qo(t),t=t.sibling}function qo(t){switch(t.tag){case 26:Sa(t),t.flags&mn&&t.memoizedState!==null&&k0(Oe,t.memoizedState,t.memoizedProps);break;case 5:Sa(t);break;case 3:case 4:var e=Oe;Oe=Lu(t.stateNode.containerInfo),Sa(t),Oe=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=mn,mn=16777216,Sa(t),mn=e):Sa(t));break;default:Sa(t)}}function Bo(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function yn(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];wt=a,jo(a,t)}Bo(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Co(t),t=t.sibling}function Co(t){switch(t.tag){case 0:case 11:case 15:yn(t),t.flags&2048&&cl(9,t,t.return);break;case 3:yn(t);break;case 12:yn(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,zu(t)):yn(t);break;default:yn(t)}}function zu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];wt=a,jo(a,t)}Bo(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:cl(8,e,e.return),zu(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,zu(e));break;default:zu(e)}t=t.sibling}}function jo(t,e){for(;wt!==null;){var l=wt;switch(l.tag){case 0:case 11:case 15:cl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:$a(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,wt=a;else t:for(l=t;wt!==null;){a=wt;var n=a.sibling,u=a.return;if(zo(a),a===l){wt=null;break t}if(n!==null){n.return=u,wt=n;break t}wt=u}}}var r0={getCacheForType:function(t){var e=Jt(Ht),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},o0=typeof WeakMap=="function"?WeakMap:Map,ht=0,Et=null,at=null,it=0,mt=0,oe=null,rl=!1,ba=!1,Qc=!1,ke=0,Dt=0,ol=0,Yl=0,Zc=0,Ee=0,pa=0,vn=null,ee=null,Vc=!1,Kc=0,Uu=1/0,Mu=null,dl=null,Gt=0,hl=null,Ea=null,Ta=0,Jc=0,kc=null,wo=null,gn=0,Wc=null;function de(){if((ht&2)!==0&&it!==0)return it&-it;if(U.T!==null){var t=sa;return t!==0?t:lf()}return If()}function Yo(){Ee===0&&(Ee=(it&536870912)===0||ot?Wf():536870912);var t=pe.current;return t!==null&&(t.flags|=32),Ee}function he(t,e,l){(t===Et&&(mt===2||mt===9)||t.cancelPendingCommit!==null)&&(Aa(t,0),ml(t,it,Ee,!1)),qa(t,l),((ht&2)===0||t!==Et)&&(t===Et&&((ht&2)===0&&(Yl|=l),Dt===4&&ml(t,it,Ee,!1)),xe(t))}function Lo(t,e,l){if((ht&6)!==0)throw Error(f(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||Ha(t,e),n=a?m0(t,e):Pc(t,e,!0),u=a;do{if(n===0){ba&&!a&&ml(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!d0(l)){n=Pc(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var s=0;else s=t.pendingLanes&-536870913,s=s!==0?s:s&536870912?536870912:0;if(s!==0){e=s;t:{var o=t;n=vn;var m=o.current.memoizedState.isDehydrated;if(m&&(Aa(o,s).flags|=256),s=Pc(o,s,!1),s!==2){if(Qc&&!m){o.errorRecoveryDisabledLanes|=u,Yl|=u,n=4;break t}u=ee,ee=n,u!==null&&(ee===null?ee=u:ee.push.apply(ee,u))}n=s}if(u=!1,n!==2)continue}}if(n===1){Aa(t,0),ml(t,e,0,!0);break}t:{switch(a=t,u=n,u){case 0:case 1:throw Error(f(345));case 4:if((e&4194048)!==e)break;case 6:ml(a,e,Ee,!rl);break t;case 2:ee=null;break;case 3:case 5:break;default:throw Error(f(329))}if((e&62914560)===e&&(n=Kc+300-De(),10<n)){if(ml(a,e,Ee,!rl),Gn(a,0,!0)!==0)break t;a.timeoutHandle=yd(Go.bind(null,a,l,ee,Mu,Vc,e,Ee,Yl,pa,rl,u,2,-0,0),n);break t}Go(a,l,ee,Mu,Vc,e,Ee,Yl,pa,rl,u,0,-0,0)}}break}while(!0);xe(t)}function Go(t,e,l,a,n,u,s,o,m,E,z,N,T,A){if(t.timeoutHandle=-1,N=e.subtreeFlags,(N&8192||(N&16785408)===16785408)&&(On={stylesheets:null,count:0,unsuspend:J0},qo(e),N=W0(),N!==null)){t.cancelPendingCommit=N(ko.bind(null,t,e,u,l,a,n,s,o,m,z,1,T,A)),ml(t,u,s,!E);return}ko(t,e,u,l,a,n,s,o,m)}function d0(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!ce(u(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function ml(t,e,l,a){e&=~Zc,e&=~Yl,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var u=31-ie(n),s=1<<u;a[u]=-1,n&=~s}l!==0&&Ff(t,l,e)}function Nu(){return(ht&6)===0?(Sn(0),!1):!0}function $c(){if(at!==null){if(mt===0)var t=at.return;else t=at,Le=Hl=null,hc(t),ya=null,fn=0,t=at;for(;t!==null;)po(t.alternate,t),t=t.return;at=null}}function Aa(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,M0(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),$c(),Et=t,at=l=je(t.current,null),it=e,mt=0,oe=null,rl=!1,ba=Ha(t,e),Qc=!1,pa=Ee=Zc=Yl=ol=Dt=0,ee=vn=null,Vc=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-ie(a),u=1<<n;e|=t[n],a&=~u}return ke=e,In(),l}function Xo(t,e){et=null,U.H=Su,e===Pa||e===fu?(e=nr(),mt=3):e===er?(e=nr(),mt=4):mt=e===uo?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,oe=e,at===null&&(Dt=1,Au(t,ve(e,t.current)))}function Qo(){var t=U.H;return U.H=Su,t===null?Su:t}function Zo(){var t=U.A;return U.A=r0,t}function Fc(){Dt=4,rl||(it&4194048)!==it&&pe.current!==null||(ba=!0),(ol&134217727)===0&&(Yl&134217727)===0||Et===null||ml(Et,it,Ee,!1)}function Pc(t,e,l){var a=ht;ht|=2;var n=Qo(),u=Zo();(Et!==t||it!==e)&&(Mu=null,Aa(t,e)),e=!1;var s=Dt;t:do try{if(mt!==0&&at!==null){var o=at,m=oe;switch(mt){case 8:$c(),s=6;break t;case 3:case 2:case 9:case 6:pe.current===null&&(e=!0);var E=mt;if(mt=0,oe=null,Oa(t,o,m,E),l&&ba){s=0;break t}break;default:E=mt,mt=0,oe=null,Oa(t,o,m,E)}}h0(),s=Dt;break}catch(z){Xo(t,z)}while(!0);return e&&t.shellSuspendCounter++,Le=Hl=null,ht=a,U.H=n,U.A=u,at===null&&(Et=null,it=0,In()),s}function h0(){for(;at!==null;)Vo(at)}function m0(t,e){var l=ht;ht|=2;var a=Qo(),n=Zo();Et!==t||it!==e?(Mu=null,Uu=De()+500,Aa(t,e)):ba=Ha(t,e);t:do try{if(mt!==0&&at!==null){e=at;var u=oe;e:switch(mt){case 1:mt=0,oe=null,Oa(t,e,u,1);break;case 2:case 9:if(lr(u)){mt=0,oe=null,Ko(e);break}e=function(){mt!==2&&mt!==9||Et!==t||(mt=7),xe(t)},u.then(e,e);break t;case 3:mt=7;break t;case 4:mt=5;break t;case 7:lr(u)?(mt=0,oe=null,Ko(e)):(mt=0,oe=null,Oa(t,e,u,7));break;case 5:var s=null;switch(at.tag){case 26:s=at.memoizedState;case 5:case 27:var o=at;if(!s||Dd(s)){mt=0,oe=null;var m=o.sibling;if(m!==null)at=m;else{var E=o.return;E!==null?(at=E,xu(E)):at=null}break e}}mt=0,oe=null,Oa(t,e,u,5);break;case 6:mt=0,oe=null,Oa(t,e,u,6);break;case 8:$c(),Dt=6;break t;default:throw Error(f(462))}}y0();break}catch(z){Xo(t,z)}while(!0);return Le=Hl=null,U.H=a,U.A=n,ht=l,at!==null?0:(Et=null,it=0,In(),Dt)}function y0(){for(;at!==null&&!jh();)Vo(at)}function Vo(t){var e=So(t.alternate,t,ke);t.memoizedProps=t.pendingProps,e===null?xu(t):at=e}function Ko(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=oo(l,e,e.pendingProps,e.type,void 0,it);break;case 11:e=oo(l,e,e.pendingProps,e.type.render,e.ref,it);break;case 5:hc(e);default:po(l,e),e=at=Ks(e,ke),e=So(l,e,ke)}t.memoizedProps=t.pendingProps,e===null?xu(t):at=e}function Oa(t,e,l,a){Le=Hl=null,hc(e),ya=null,fn=0;var n=e.return;try{if(n0(t,n,e,l,it)){Dt=1,Au(t,ve(l,t.current)),at=null;return}}catch(u){if(n!==null)throw at=n,u;Dt=1,Au(t,ve(l,t.current)),at=null;return}e.flags&32768?(ot||a===1?t=!0:ba||(it&536870912)!==0?t=!1:(rl=t=!0,(a===2||a===9||a===3||a===6)&&(a=pe.current,a!==null&&a.tag===13&&(a.flags|=16384))),Jo(e,t)):xu(e)}function xu(t){var e=t;do{if((e.flags&32768)!==0){Jo(e,rl);return}t=e.return;var l=i0(e.alternate,e,ke);if(l!==null){at=l;return}if(e=e.sibling,e!==null){at=e;return}at=e=t}while(e!==null);Dt===0&&(Dt=5)}function Jo(t,e){do{var l=c0(t.alternate,t);if(l!==null){l.flags&=32767,at=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){at=t;return}at=t=l}while(t!==null);Dt=6,at=null}function ko(t,e,l,a,n,u,s,o,m){t.cancelPendingCommit=null;do Hu();while(Gt!==0);if((ht&6)!==0)throw Error(f(327));if(e!==null){if(e===t.current)throw Error(f(177));if(u=e.lanes|e.childLanes,u|=Xi,Jh(t,l,u,s,o,m),t===Et&&(at=Et=null,it=0),Ea=e,hl=t,Ta=l,Jc=u,kc=n,wo=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,b0(wn,function(){return Io(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=U.T,U.T=null,n=w.p,w.p=2,s=ht,ht|=4;try{f0(t,e,l)}finally{ht=s,w.p=n,U.T=a}}Gt=1,Wo(),$o(),Fo()}}function Wo(){if(Gt===1){Gt=0;var t=hl,e=Ea,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=U.T,U.T=null;var a=w.p;w.p=2;var n=ht;ht|=4;try{No(e,t);var u=of,s=Cs(t.containerInfo),o=u.focusedElem,m=u.selectionRange;if(s!==o&&o&&o.ownerDocument&&Bs(o.ownerDocument.documentElement,o)){if(m!==null&&ji(o)){var E=m.start,z=m.end;if(z===void 0&&(z=E),"selectionStart"in o)o.selectionStart=E,o.selectionEnd=Math.min(z,o.value.length);else{var N=o.ownerDocument||document,T=N&&N.defaultView||window;if(T.getSelection){var A=T.getSelection(),$=o.textContent.length,K=Math.min(m.start,$),St=m.end===void 0?K:Math.min(m.end,$);!A.extend&&K>St&&(s=St,St=K,K=s);var b=qs(o,K),g=qs(o,St);if(b&&g&&(A.rangeCount!==1||A.anchorNode!==b.node||A.anchorOffset!==b.offset||A.focusNode!==g.node||A.focusOffset!==g.offset)){var p=N.createRange();p.setStart(b.node,b.offset),A.removeAllRanges(),K>St?(A.addRange(p),A.extend(g.node,g.offset)):(p.setEnd(g.node,g.offset),A.addRange(p))}}}}for(N=[],A=o;A=A.parentNode;)A.nodeType===1&&N.push({element:A,left:A.scrollLeft,top:A.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<N.length;o++){var M=N[o];M.element.scrollLeft=M.left,M.element.scrollTop=M.top}}Vu=!!rf,of=rf=null}finally{ht=n,w.p=a,U.T=l}}t.current=e,Gt=2}}function $o(){if(Gt===2){Gt=0;var t=hl,e=Ea,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=U.T,U.T=null;var a=w.p;w.p=2;var n=ht;ht|=4;try{Do(t,e.alternate,e)}finally{ht=n,w.p=a,U.T=l}}Gt=3}}function Fo(){if(Gt===4||Gt===3){Gt=0,wh();var t=hl,e=Ea,l=Ta,a=wo;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Gt=5:(Gt=0,Ea=hl=null,Po(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(dl=null),vi(l),e=e.stateNode,ue&&typeof ue.onCommitFiberRoot=="function")try{ue.onCommitFiberRoot(xa,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=U.T,n=w.p,w.p=2,U.T=null;try{for(var u=t.onRecoverableError,s=0;s<a.length;s++){var o=a[s];u(o.value,{componentStack:o.stack})}}finally{U.T=e,w.p=n}}(Ta&3)!==0&&Hu(),xe(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Wc?gn++:(gn=0,Wc=t):gn=0,Sn(0)}}function Po(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,$a(e)))}function Hu(t){return Wo(),$o(),Fo(),Io()}function Io(){if(Gt!==5)return!1;var t=hl,e=Jc;Jc=0;var l=vi(Ta),a=U.T,n=w.p;try{w.p=32>l?32:l,U.T=null,l=kc,kc=null;var u=hl,s=Ta;if(Gt=0,Ea=hl=null,Ta=0,(ht&6)!==0)throw Error(f(331));var o=ht;if(ht|=4,Co(u.current),Ho(u,u.current,s,l),ht=o,Sn(0,!1),ue&&typeof ue.onPostCommitFiberRoot=="function")try{ue.onPostCommitFiberRoot(xa,u)}catch{}return!0}finally{w.p=n,U.T=a,Po(t,e)}}function td(t,e,l){e=ve(l,e),e=Dc(t.stateNode,e,2),t=al(t,e,2),t!==null&&(qa(t,2),xe(t))}function pt(t,e,l){if(t.tag===3)td(t,t,l);else for(;e!==null;){if(e.tag===3){td(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(dl===null||!dl.has(a))){t=ve(l,t),l=ao(2),a=al(e,l,2),a!==null&&(no(l,a,e,t),qa(a,2),xe(a));break}}e=e.return}}function Ic(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new o0;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Qc=!0,n.add(l),t=v0.bind(null,t,e,l),e.then(t,t))}function v0(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Et===t&&(it&l)===l&&(Dt===4||Dt===3&&(it&62914560)===it&&300>De()-Kc?(ht&2)===0&&Aa(t,0):Zc|=l,pa===it&&(pa=0)),xe(t)}function ed(t,e){e===0&&(e=$f()),t=ua(t,e),t!==null&&(qa(t,e),xe(t))}function g0(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),ed(t,l)}function S0(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(f(314))}a!==null&&a.delete(e),ed(t,l)}function b0(t,e){return di(t,e)}var qu=null,Ra=null,tf=!1,Bu=!1,ef=!1,Ll=0;function xe(t){t!==Ra&&t.next===null&&(Ra===null?qu=Ra=t:Ra=Ra.next=t),Bu=!0,tf||(tf=!0,E0())}function Sn(t,e){if(!ef&&Bu){ef=!0;do for(var l=!1,a=qu;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var s=a.suspendedLanes,o=a.pingedLanes;u=(1<<31-ie(42|t)+1)-1,u&=n&~(s&~o),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,ud(a,u))}else u=it,u=Gn(a,a===Et?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||Ha(a,u)||(l=!0,ud(a,u));a=a.next}while(l);ef=!1}}function p0(){ld()}function ld(){Bu=tf=!1;var t=0;Ll!==0&&(U0()&&(t=Ll),Ll=0);for(var e=De(),l=null,a=qu;a!==null;){var n=a.next,u=ad(a,e);u===0?(a.next=null,l===null?qu=n:l.next=n,n===null&&(Ra=l)):(l=a,(t!==0||(u&3)!==0)&&(Bu=!0)),a=n}Sn(t)}function ad(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var s=31-ie(u),o=1<<s,m=n[s];m===-1?((o&l)===0||(o&a)!==0)&&(n[s]=Kh(o,e)):m<=e&&(t.expiredLanes|=o),u&=~o}if(e=Et,l=it,l=Gn(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(mt===2||mt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&hi(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||Ha(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&hi(a),vi(l)){case 2:case 8:l=Jf;break;case 32:l=wn;break;case 268435456:l=kf;break;default:l=wn}return a=nd.bind(null,t),l=di(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&hi(a),t.callbackPriority=2,t.callbackNode=null,2}function nd(t,e){if(Gt!==0&&Gt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(Hu()&&t.callbackNode!==l)return null;var a=it;return a=Gn(t,t===Et?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Lo(t,a,e),ad(t,De()),t.callbackNode!=null&&t.callbackNode===l?nd.bind(null,t):null)}function ud(t,e){if(Hu())return null;Lo(t,e,!0)}function E0(){N0(function(){(ht&6)!==0?di(Kf,p0):ld()})}function lf(){return Ll===0&&(Ll=Wf()),Ll}function id(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Kn(""+t)}function cd(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function T0(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var u=id((n[Ft]||null).action),s=a.submitter;s&&(e=(e=s[Ft]||null)?id(e.formAction):s.getAttribute("formAction"),e!==null&&(u=e,s=null));var o=new $n("action","action",null,a,n);t.push({event:o,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Ll!==0){var m=s?cd(n,s):new FormData(n);Tc(l,{pending:!0,data:m,method:n.method,action:u},null,m)}}else typeof u=="function"&&(o.preventDefault(),m=s?cd(n,s):new FormData(n),Tc(l,{pending:!0,data:m,method:n.method,action:u},u,m))},currentTarget:n}]})}}for(var af=0;af<Gi.length;af++){var nf=Gi[af],A0=nf.toLowerCase(),O0=nf[0].toUpperCase()+nf.slice(1);Ae(A0,"on"+O0)}Ae(Ys,"onAnimationEnd"),Ae(Ls,"onAnimationIteration"),Ae(Gs,"onAnimationStart"),Ae("dblclick","onDoubleClick"),Ae("focusin","onFocus"),Ae("focusout","onBlur"),Ae(Gm,"onTransitionRun"),Ae(Xm,"onTransitionStart"),Ae(Qm,"onTransitionCancel"),Ae(Xs,"onTransitionEnd"),Wl("onMouseEnter",["mouseout","mouseover"]),Wl("onMouseLeave",["mouseout","mouseover"]),Wl("onPointerEnter",["pointerout","pointerover"]),Wl("onPointerLeave",["pointerout","pointerover"]),Ol("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ol("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ol("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ol("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ol("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ol("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var bn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),R0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(bn));function fd(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var u=void 0;if(e)for(var s=a.length-1;0<=s;s--){var o=a[s],m=o.instance,E=o.currentTarget;if(o=o.listener,m!==u&&n.isPropagationStopped())break t;u=o,n.currentTarget=E;try{u(n)}catch(z){Tu(z)}n.currentTarget=null,u=m}else for(s=0;s<a.length;s++){if(o=a[s],m=o.instance,E=o.currentTarget,o=o.listener,m!==u&&n.isPropagationStopped())break t;u=o,n.currentTarget=E;try{u(n)}catch(z){Tu(z)}n.currentTarget=null,u=m}}}}function nt(t,e){var l=e[gi];l===void 0&&(l=e[gi]=new Set);var a=t+"__bubble";l.has(a)||(sd(e,t,2,!1),l.add(a))}function uf(t,e,l){var a=0;e&&(a|=4),sd(l,t,a,e)}var Cu="_reactListening"+Math.random().toString(36).slice(2);function cf(t){if(!t[Cu]){t[Cu]=!0,es.forEach(function(l){l!=="selectionchange"&&(R0.has(l)||uf(l,!1,t),uf(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Cu]||(e[Cu]=!0,uf("selectionchange",!1,e))}}function sd(t,e,l,a){switch(Hd(e)){case 2:var n=P0;break;case 8:n=I0;break;default:n=Ef}l=n.bind(null,e,l,t),n=void 0,!zi||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function ff(t,e,l,a,n){var u=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var s=a.tag;if(s===3||s===4){var o=a.stateNode.containerInfo;if(o===n)break;if(s===4)for(s=a.return;s!==null;){var m=s.tag;if((m===3||m===4)&&s.stateNode.containerInfo===n)return;s=s.return}for(;o!==null;){if(s=Kl(o),s===null)return;if(m=s.tag,m===5||m===6||m===26||m===27){a=u=s;continue t}o=o.parentNode}}a=a.return}ys(function(){var E=u,z=_i(l),N=[];t:{var T=Qs.get(t);if(T!==void 0){var A=$n,$=t;switch(t){case"keypress":if(kn(l)===0)break t;case"keydown":case"keyup":A=bm;break;case"focusin":$="focus",A=xi;break;case"focusout":$="blur",A=xi;break;case"beforeblur":case"afterblur":A=xi;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":A=Ss;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":A=cm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":A=Tm;break;case Ys:case Ls:case Gs:A=rm;break;case Xs:A=Om;break;case"scroll":case"scrollend":A=um;break;case"wheel":A=_m;break;case"copy":case"cut":case"paste":A=dm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":A=ps;break;case"toggle":case"beforetoggle":A=zm}var K=(e&4)!==0,St=!K&&(t==="scroll"||t==="scrollend"),b=K?T!==null?T+"Capture":null:T;K=[];for(var g=E,p;g!==null;){var M=g;if(p=M.stateNode,M=M.tag,M!==5&&M!==26&&M!==27||p===null||b===null||(M=ja(g,b),M!=null&&K.push(pn(g,M,p))),St)break;g=g.return}0<K.length&&(T=new A(T,$,null,l,z),N.push({event:T,listeners:K}))}}if((e&7)===0){t:{if(T=t==="mouseover"||t==="pointerover",A=t==="mouseout"||t==="pointerout",T&&l!==Ri&&($=l.relatedTarget||l.fromElement)&&(Kl($)||$[Vl]))break t;if((A||T)&&(T=z.window===z?z:(T=z.ownerDocument)?T.defaultView||T.parentWindow:window,A?($=l.relatedTarget||l.toElement,A=E,$=$?Kl($):null,$!==null&&(St=h($),K=$.tag,$!==St||K!==5&&K!==27&&K!==6)&&($=null)):(A=null,$=E),A!==$)){if(K=Ss,M="onMouseLeave",b="onMouseEnter",g="mouse",(t==="pointerout"||t==="pointerover")&&(K=ps,M="onPointerLeave",b="onPointerEnter",g="pointer"),St=A==null?T:Ca(A),p=$==null?T:Ca($),T=new K(M,g+"leave",A,l,z),T.target=St,T.relatedTarget=p,M=null,Kl(z)===E&&(K=new K(b,g+"enter",$,l,z),K.target=p,K.relatedTarget=St,M=K),St=M,A&&$)e:{for(K=A,b=$,g=0,p=K;p;p=_a(p))g++;for(p=0,M=b;M;M=_a(M))p++;for(;0<g-p;)K=_a(K),g--;for(;0<p-g;)b=_a(b),p--;for(;g--;){if(K===b||b!==null&&K===b.alternate)break e;K=_a(K),b=_a(b)}K=null}else K=null;A!==null&&rd(N,T,A,K,!1),$!==null&&St!==null&&rd(N,St,$,K,!0)}}t:{if(T=E?Ca(E):window,A=T.nodeName&&T.nodeName.toLowerCase(),A==="select"||A==="input"&&T.type==="file")var X=zs;else if(_s(T))if(Us)X=wm;else{X=Cm;var lt=Bm}else A=T.nodeName,!A||A.toLowerCase()!=="input"||T.type!=="checkbox"&&T.type!=="radio"?E&&Oi(E.elementType)&&(X=zs):X=jm;if(X&&(X=X(t,E))){Ds(N,X,l,z);break t}lt&&lt(t,T,E),t==="focusout"&&E&&T.type==="number"&&E.memoizedProps.value!=null&&Ai(T,"number",T.value)}switch(lt=E?Ca(E):window,t){case"focusin":(_s(lt)||lt.contentEditable==="true")&&(la=lt,wi=E,Va=null);break;case"focusout":Va=wi=la=null;break;case"mousedown":Yi=!0;break;case"contextmenu":case"mouseup":case"dragend":Yi=!1,js(N,l,z);break;case"selectionchange":if(Lm)break;case"keydown":case"keyup":js(N,l,z)}var Q;if(qi)t:{switch(t){case"compositionstart":var J="onCompositionStart";break t;case"compositionend":J="onCompositionEnd";break t;case"compositionupdate":J="onCompositionUpdate";break t}J=void 0}else ea?Os(t,l)&&(J="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(J="onCompositionStart");J&&(Es&&l.locale!=="ko"&&(ea||J!=="onCompositionStart"?J==="onCompositionEnd"&&ea&&(Q=vs()):(Ie=z,Ui="value"in Ie?Ie.value:Ie.textContent,ea=!0)),lt=ju(E,J),0<lt.length&&(J=new bs(J,t,null,l,z),N.push({event:J,listeners:lt}),Q?J.data=Q:(Q=Rs(l),Q!==null&&(J.data=Q)))),(Q=Mm?Nm(t,l):xm(t,l))&&(J=ju(E,"onBeforeInput"),0<J.length&&(lt=new bs("onBeforeInput","beforeinput",null,l,z),N.push({event:lt,listeners:J}),lt.data=Q)),T0(N,t,E,l,z)}fd(N,e)})}function pn(t,e,l){return{instance:t,listener:e,currentTarget:l}}function ju(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=ja(t,l),n!=null&&a.unshift(pn(t,n,u)),n=ja(t,e),n!=null&&a.push(pn(t,n,u))),t.tag===3)return a;t=t.return}return[]}function _a(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function rd(t,e,l,a,n){for(var u=e._reactName,s=[];l!==null&&l!==a;){var o=l,m=o.alternate,E=o.stateNode;if(o=o.tag,m!==null&&m===a)break;o!==5&&o!==26&&o!==27||E===null||(m=E,n?(E=ja(l,u),E!=null&&s.unshift(pn(l,E,m))):n||(E=ja(l,u),E!=null&&s.push(pn(l,E,m)))),l=l.return}s.length!==0&&t.push({event:e,listeners:s})}var _0=/\r\n?/g,D0=/\u0000|\uFFFD/g;function od(t){return(typeof t=="string"?t:""+t).replace(_0,`
`).replace(D0,"")}function dd(t,e){return e=od(e),od(t)===e}function wu(){}function gt(t,e,l,a,n,u){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||Pl(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&Pl(t,""+a);break;case"className":Qn(t,"class",a);break;case"tabIndex":Qn(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Qn(t,l,a);break;case"style":hs(t,a,u);break;case"data":if(e!=="object"){Qn(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Kn(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&gt(t,e,"name",n.name,n,null),gt(t,e,"formEncType",n.formEncType,n,null),gt(t,e,"formMethod",n.formMethod,n,null),gt(t,e,"formTarget",n.formTarget,n,null)):(gt(t,e,"encType",n.encType,n,null),gt(t,e,"method",n.method,n,null),gt(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Kn(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=wu);break;case"onScroll":a!=null&&nt("scroll",t);break;case"onScrollEnd":a!=null&&nt("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(f(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(f(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=Kn(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":nt("beforetoggle",t),nt("toggle",t),Xn(t,"popover",a);break;case"xlinkActuate":Be(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Be(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Be(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Be(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Be(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Be(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Be(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Be(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Be(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Xn(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=am.get(l)||l,Xn(t,l,a))}}function sf(t,e,l,a,n,u){switch(l){case"style":hs(t,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(f(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(f(60));t.innerHTML=l}}break;case"children":typeof a=="string"?Pl(t,a):(typeof a=="number"||typeof a=="bigint")&&Pl(t,""+a);break;case"onScroll":a!=null&&nt("scroll",t);break;case"onScrollEnd":a!=null&&nt("scrollend",t);break;case"onClick":a!=null&&(t.onclick=wu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ls.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),u=t[Ft]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Xn(t,l,a)}}}function Xt(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":nt("error",t),nt("load",t);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var s=l[u];if(s!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(f(137,e));default:gt(t,e,u,s,l,null)}}n&&gt(t,e,"srcSet",l.srcSet,l,null),a&&gt(t,e,"src",l.src,l,null);return;case"input":nt("invalid",t);var o=u=s=n=null,m=null,E=null;for(a in l)if(l.hasOwnProperty(a)){var z=l[a];if(z!=null)switch(a){case"name":n=z;break;case"type":s=z;break;case"checked":m=z;break;case"defaultChecked":E=z;break;case"value":u=z;break;case"defaultValue":o=z;break;case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(f(137,e));break;default:gt(t,e,a,z,l,null)}}ss(t,u,o,m,E,s,n,!1),Zn(t);return;case"select":nt("invalid",t),a=s=u=null;for(n in l)if(l.hasOwnProperty(n)&&(o=l[n],o!=null))switch(n){case"value":u=o;break;case"defaultValue":s=o;break;case"multiple":a=o;default:gt(t,e,n,o,l,null)}e=u,l=s,t.multiple=!!a,e!=null?Fl(t,!!a,e,!1):l!=null&&Fl(t,!!a,l,!0);return;case"textarea":nt("invalid",t),u=n=a=null;for(s in l)if(l.hasOwnProperty(s)&&(o=l[s],o!=null))switch(s){case"value":a=o;break;case"defaultValue":n=o;break;case"children":u=o;break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(f(91));break;default:gt(t,e,s,o,l,null)}os(t,a,n,u),Zn(t);return;case"option":for(m in l)if(l.hasOwnProperty(m)&&(a=l[m],a!=null))switch(m){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:gt(t,e,m,a,l,null)}return;case"dialog":nt("beforetoggle",t),nt("toggle",t),nt("cancel",t),nt("close",t);break;case"iframe":case"object":nt("load",t);break;case"video":case"audio":for(a=0;a<bn.length;a++)nt(bn[a],t);break;case"image":nt("error",t),nt("load",t);break;case"details":nt("toggle",t);break;case"embed":case"source":case"link":nt("error",t),nt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(E in l)if(l.hasOwnProperty(E)&&(a=l[E],a!=null))switch(E){case"children":case"dangerouslySetInnerHTML":throw Error(f(137,e));default:gt(t,e,E,a,l,null)}return;default:if(Oi(e)){for(z in l)l.hasOwnProperty(z)&&(a=l[z],a!==void 0&&sf(t,e,z,a,l,void 0));return}}for(o in l)l.hasOwnProperty(o)&&(a=l[o],a!=null&&gt(t,e,o,a,l,null))}function z0(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,s=null,o=null,m=null,E=null,z=null;for(A in l){var N=l[A];if(l.hasOwnProperty(A)&&N!=null)switch(A){case"checked":break;case"value":break;case"defaultValue":m=N;default:a.hasOwnProperty(A)||gt(t,e,A,null,a,N)}}for(var T in a){var A=a[T];if(N=l[T],a.hasOwnProperty(T)&&(A!=null||N!=null))switch(T){case"type":u=A;break;case"name":n=A;break;case"checked":E=A;break;case"defaultChecked":z=A;break;case"value":s=A;break;case"defaultValue":o=A;break;case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(f(137,e));break;default:A!==N&&gt(t,e,T,A,a,N)}}Ti(t,s,o,m,E,z,u,n);return;case"select":A=s=o=T=null;for(u in l)if(m=l[u],l.hasOwnProperty(u)&&m!=null)switch(u){case"value":break;case"multiple":A=m;default:a.hasOwnProperty(u)||gt(t,e,u,null,a,m)}for(n in a)if(u=a[n],m=l[n],a.hasOwnProperty(n)&&(u!=null||m!=null))switch(n){case"value":T=u;break;case"defaultValue":o=u;break;case"multiple":s=u;default:u!==m&&gt(t,e,n,u,a,m)}e=o,l=s,a=A,T!=null?Fl(t,!!l,T,!1):!!a!=!!l&&(e!=null?Fl(t,!!l,e,!0):Fl(t,!!l,l?[]:"",!1));return;case"textarea":A=T=null;for(o in l)if(n=l[o],l.hasOwnProperty(o)&&n!=null&&!a.hasOwnProperty(o))switch(o){case"value":break;case"children":break;default:gt(t,e,o,null,a,n)}for(s in a)if(n=a[s],u=l[s],a.hasOwnProperty(s)&&(n!=null||u!=null))switch(s){case"value":T=n;break;case"defaultValue":A=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(f(91));break;default:n!==u&&gt(t,e,s,n,a,u)}rs(t,T,A);return;case"option":for(var $ in l)if(T=l[$],l.hasOwnProperty($)&&T!=null&&!a.hasOwnProperty($))switch($){case"selected":t.selected=!1;break;default:gt(t,e,$,null,a,T)}for(m in a)if(T=a[m],A=l[m],a.hasOwnProperty(m)&&T!==A&&(T!=null||A!=null))switch(m){case"selected":t.selected=T&&typeof T!="function"&&typeof T!="symbol";break;default:gt(t,e,m,T,a,A)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var K in l)T=l[K],l.hasOwnProperty(K)&&T!=null&&!a.hasOwnProperty(K)&&gt(t,e,K,null,a,T);for(E in a)if(T=a[E],A=l[E],a.hasOwnProperty(E)&&T!==A&&(T!=null||A!=null))switch(E){case"children":case"dangerouslySetInnerHTML":if(T!=null)throw Error(f(137,e));break;default:gt(t,e,E,T,a,A)}return;default:if(Oi(e)){for(var St in l)T=l[St],l.hasOwnProperty(St)&&T!==void 0&&!a.hasOwnProperty(St)&&sf(t,e,St,void 0,a,T);for(z in a)T=a[z],A=l[z],!a.hasOwnProperty(z)||T===A||T===void 0&&A===void 0||sf(t,e,z,T,a,A);return}}for(var b in l)T=l[b],l.hasOwnProperty(b)&&T!=null&&!a.hasOwnProperty(b)&&gt(t,e,b,null,a,T);for(N in a)T=a[N],A=l[N],!a.hasOwnProperty(N)||T===A||T==null&&A==null||gt(t,e,N,T,a,A)}var rf=null,of=null;function Yu(t){return t.nodeType===9?t:t.ownerDocument}function hd(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function md(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function df(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var hf=null;function U0(){var t=window.event;return t&&t.type==="popstate"?t===hf?!1:(hf=t,!0):(hf=null,!1)}var yd=typeof setTimeout=="function"?setTimeout:void 0,M0=typeof clearTimeout=="function"?clearTimeout:void 0,vd=typeof Promise=="function"?Promise:void 0,N0=typeof queueMicrotask=="function"?queueMicrotask:typeof vd<"u"?function(t){return vd.resolve(null).then(t).catch(x0)}:yd;function x0(t){setTimeout(function(){throw t})}function yl(t){return t==="head"}function gd(t,e){var l=e,a=0,n=0;do{var u=l.nextSibling;if(t.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var s=t.ownerDocument;if(l&1&&En(s.documentElement),l&2&&En(s.body),l&4)for(l=s.head,En(l),s=l.firstChild;s;){var o=s.nextSibling,m=s.nodeName;s[Ba]||m==="SCRIPT"||m==="STYLE"||m==="LINK"&&s.rel.toLowerCase()==="stylesheet"||l.removeChild(s),s=o}}if(n===0){t.removeChild(u),Un(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);Un(e)}function mf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":mf(l),Si(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function H0(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[Ba])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Re(t.nextSibling),t===null)break}return null}function q0(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Re(t.nextSibling),t===null))return null;return t}function yf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function B0(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Re(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var vf=null;function Sd(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function bd(t,e,l){switch(e=Yu(l),t){case"html":if(t=e.documentElement,!t)throw Error(f(452));return t;case"head":if(t=e.head,!t)throw Error(f(453));return t;case"body":if(t=e.body,!t)throw Error(f(454));return t;default:throw Error(f(451))}}function En(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Si(t)}var Te=new Map,pd=new Set;function Lu(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var We=w.d;w.d={f:C0,r:j0,D:w0,C:Y0,L:L0,m:G0,X:Q0,S:X0,M:Z0};function C0(){var t=We.f(),e=Nu();return t||e}function j0(t){var e=Jl(t);e!==null&&e.tag===5&&e.type==="form"?Yr(e):We.r(t)}var Da=typeof document>"u"?null:document;function Ed(t,e,l){var a=Da;if(a&&typeof e=="string"&&e){var n=ye(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),pd.has(n)||(pd.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),Xt(e,"link",t),Ct(e),a.head.appendChild(e)))}}function w0(t){We.D(t),Ed("dns-prefetch",t,null)}function Y0(t,e){We.C(t,e),Ed("preconnect",t,e)}function L0(t,e,l){We.L(t,e,l);var a=Da;if(a&&t&&e){var n='link[rel="preload"][as="'+ye(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+ye(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+ye(l.imageSizes)+'"]')):n+='[href="'+ye(t)+'"]';var u=n;switch(e){case"style":u=za(t);break;case"script":u=Ua(t)}Te.has(u)||(t=O({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),Te.set(u,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(Tn(u))||e==="script"&&a.querySelector(An(u))||(e=a.createElement("link"),Xt(e,"link",t),Ct(e),a.head.appendChild(e)))}}function G0(t,e){We.m(t,e);var l=Da;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+ye(a)+'"][href="'+ye(t)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Ua(t)}if(!Te.has(u)&&(t=O({rel:"modulepreload",href:t},e),Te.set(u,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(An(u)))return}a=l.createElement("link"),Xt(a,"link",t),Ct(a),l.head.appendChild(a)}}}function X0(t,e,l){We.S(t,e,l);var a=Da;if(a&&t){var n=kl(a).hoistableStyles,u=za(t);e=e||"default";var s=n.get(u);if(!s){var o={loading:0,preload:null};if(s=a.querySelector(Tn(u)))o.loading=5;else{t=O({rel:"stylesheet",href:t,"data-precedence":e},l),(l=Te.get(u))&&gf(t,l);var m=s=a.createElement("link");Ct(m),Xt(m,"link",t),m._p=new Promise(function(E,z){m.onload=E,m.onerror=z}),m.addEventListener("load",function(){o.loading|=1}),m.addEventListener("error",function(){o.loading|=2}),o.loading|=4,Gu(s,e,a)}s={type:"stylesheet",instance:s,count:1,state:o},n.set(u,s)}}}function Q0(t,e){We.X(t,e);var l=Da;if(l&&t){var a=kl(l).hoistableScripts,n=Ua(t),u=a.get(n);u||(u=l.querySelector(An(n)),u||(t=O({src:t,async:!0},e),(e=Te.get(n))&&Sf(t,e),u=l.createElement("script"),Ct(u),Xt(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Z0(t,e){We.M(t,e);var l=Da;if(l&&t){var a=kl(l).hoistableScripts,n=Ua(t),u=a.get(n);u||(u=l.querySelector(An(n)),u||(t=O({src:t,async:!0,type:"module"},e),(e=Te.get(n))&&Sf(t,e),u=l.createElement("script"),Ct(u),Xt(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function Td(t,e,l,a){var n=(n=P.current)?Lu(n):null;if(!n)throw Error(f(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=za(l.href),l=kl(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=za(l.href);var u=kl(n).hoistableStyles,s=u.get(t);if(s||(n=n.ownerDocument||n,s={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,s),(u=n.querySelector(Tn(t)))&&!u._p&&(s.instance=u,s.state.loading=5),Te.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Te.set(t,l),u||V0(n,t,l,s.state))),e&&a===null)throw Error(f(528,""));return s}if(e&&a!==null)throw Error(f(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ua(l),l=kl(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(f(444,t))}}function za(t){return'href="'+ye(t)+'"'}function Tn(t){return'link[rel="stylesheet"]['+t+"]"}function Ad(t){return O({},t,{"data-precedence":t.precedence,precedence:null})}function V0(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),Xt(e,"link",l),Ct(e),t.head.appendChild(e))}function Ua(t){return'[src="'+ye(t)+'"]'}function An(t){return"script[async]"+t}function Od(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+ye(l.href)+'"]');if(a)return e.instance=a,Ct(a),a;var n=O({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Ct(a),Xt(a,"style",n),Gu(a,l.precedence,t),e.instance=a;case"stylesheet":n=za(l.href);var u=t.querySelector(Tn(n));if(u)return e.state.loading|=4,e.instance=u,Ct(u),u;a=Ad(l),(n=Te.get(n))&&gf(a,n),u=(t.ownerDocument||t).createElement("link"),Ct(u);var s=u;return s._p=new Promise(function(o,m){s.onload=o,s.onerror=m}),Xt(u,"link",a),e.state.loading|=4,Gu(u,l.precedence,t),e.instance=u;case"script":return u=Ua(l.src),(n=t.querySelector(An(u)))?(e.instance=n,Ct(n),n):(a=l,(n=Te.get(u))&&(a=O({},l),Sf(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Ct(n),Xt(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(f(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Gu(a,l.precedence,t));return e.instance}function Gu(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,s=0;s<a.length;s++){var o=a[s];if(o.dataset.precedence===e)u=o;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function gf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Sf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Xu=null;function Rd(t,e,l){if(Xu===null){var a=new Map,n=Xu=new Map;n.set(l,a)}else n=Xu,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var u=l[n];if(!(u[Ba]||u[Kt]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var s=u.getAttribute(e)||"";s=t+s;var o=a.get(s);o?o.push(u):a.set(s,[u])}}return a}function _d(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function K0(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Dd(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var On=null;function J0(){}function k0(t,e,l){if(On===null)throw Error(f(475));var a=On;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=za(l.href),u=t.querySelector(Tn(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Qu.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=u,Ct(u);return}u=t.ownerDocument||t,l=Ad(l),(n=Te.get(n))&&gf(l,n),u=u.createElement("link"),Ct(u);var s=u;s._p=new Promise(function(o,m){s.onload=o,s.onerror=m}),Xt(u,"link",l),e.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Qu.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function W0(){if(On===null)throw Error(f(475));var t=On;return t.stylesheets&&t.count===0&&bf(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&bf(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Qu(){if(this.count--,this.count===0){if(this.stylesheets)bf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Zu=null;function bf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Zu=new Map,e.forEach($0,t),Zu=null,Qu.call(t))}function $0(t,e){if(!(e.state.loading&4)){var l=Zu.get(t);if(l)var a=l.get(null);else{l=new Map,Zu.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var s=n[u];(s.nodeName==="LINK"||s.getAttribute("media")!=="not all")&&(l.set(s.dataset.precedence,s),a=s)}a&&l.set(null,a)}n=e.instance,s=n.getAttribute("data-precedence"),u=l.get(s)||a,u===a&&l.set(null,n),l.set(s,n),this.count++,a=Qu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Rn={$$typeof:W,Provider:null,Consumer:null,_currentValue:k,_currentValue2:k,_threadCount:0};function F0(t,e,l,a,n,u,s,o){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=mi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=mi(0),this.hiddenUpdates=mi(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=s,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function zd(t,e,l,a,n,u,s,o,m,E,z,N){return t=new F0(t,e,l,s,o,m,E,N),e=1,u===!0&&(e|=24),u=fe(3,null,null,e),t.current=u,u.stateNode=t,e=Ii(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:e},ac(u),t}function Ud(t){return t?(t=ia,t):ia}function Md(t,e,l,a,n,u){n=Ud(n),a.context===null?a.context=n:a.pendingContext=n,a=ll(e),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=al(t,a,e),l!==null&&(he(l,t,e),tn(l,t,e))}function Nd(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function pf(t,e){Nd(t,e),(t=t.alternate)&&Nd(t,e)}function xd(t){if(t.tag===13){var e=ua(t,67108864);e!==null&&he(e,t,67108864),pf(t,67108864)}}var Vu=!0;function P0(t,e,l,a){var n=U.T;U.T=null;var u=w.p;try{w.p=2,Ef(t,e,l,a)}finally{w.p=u,U.T=n}}function I0(t,e,l,a){var n=U.T;U.T=null;var u=w.p;try{w.p=8,Ef(t,e,l,a)}finally{w.p=u,U.T=n}}function Ef(t,e,l,a){if(Vu){var n=Tf(a);if(n===null)ff(t,e,a,Ku,l),qd(t,a);else if(ey(n,t,e,l,a))a.stopPropagation();else if(qd(t,a),e&4&&-1<ty.indexOf(t)){for(;n!==null;){var u=Jl(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var s=Al(u.pendingLanes);if(s!==0){var o=u;for(o.pendingLanes|=2,o.entangledLanes|=2;s;){var m=1<<31-ie(s);o.entanglements[1]|=m,s&=~m}xe(u),(ht&6)===0&&(Uu=De()+500,Sn(0))}}break;case 13:o=ua(u,2),o!==null&&he(o,u,2),Nu(),pf(u,2)}if(u=Tf(a),u===null&&ff(t,e,a,Ku,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else ff(t,e,a,null,l)}}function Tf(t){return t=_i(t),Af(t)}var Ku=null;function Af(t){if(Ku=null,t=Kl(t),t!==null){var e=h(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=y(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Ku=t,null}function Hd(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Yh()){case Kf:return 2;case Jf:return 8;case wn:case Lh:return 32;case kf:return 268435456;default:return 32}default:return 32}}var Of=!1,vl=null,gl=null,Sl=null,_n=new Map,Dn=new Map,bl=[],ty="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function qd(t,e){switch(t){case"focusin":case"focusout":vl=null;break;case"dragenter":case"dragleave":gl=null;break;case"mouseover":case"mouseout":Sl=null;break;case"pointerover":case"pointerout":_n.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Dn.delete(e.pointerId)}}function zn(t,e,l,a,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},e!==null&&(e=Jl(e),e!==null&&xd(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function ey(t,e,l,a,n){switch(e){case"focusin":return vl=zn(vl,t,e,l,a,n),!0;case"dragenter":return gl=zn(gl,t,e,l,a,n),!0;case"mouseover":return Sl=zn(Sl,t,e,l,a,n),!0;case"pointerover":var u=n.pointerId;return _n.set(u,zn(_n.get(u)||null,t,e,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Dn.set(u,zn(Dn.get(u)||null,t,e,l,a,n)),!0}return!1}function Bd(t){var e=Kl(t.target);if(e!==null){var l=h(e);if(l!==null){if(e=l.tag,e===13){if(e=y(l),e!==null){t.blockedOn=e,kh(t.priority,function(){if(l.tag===13){var a=de();a=yi(a);var n=ua(l,a);n!==null&&he(n,l,a),pf(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Ju(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=Tf(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);Ri=a,l.target.dispatchEvent(a),Ri=null}else return e=Jl(l),e!==null&&xd(e),t.blockedOn=l,!1;e.shift()}return!0}function Cd(t,e,l){Ju(t)&&l.delete(e)}function ly(){Of=!1,vl!==null&&Ju(vl)&&(vl=null),gl!==null&&Ju(gl)&&(gl=null),Sl!==null&&Ju(Sl)&&(Sl=null),_n.forEach(Cd),Dn.forEach(Cd)}function ku(t,e){t.blockedOn===e&&(t.blockedOn=null,Of||(Of=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,ly)))}var Wu=null;function jd(t){Wu!==t&&(Wu=t,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Wu===t&&(Wu=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(Af(a||l)===null)continue;break}var u=Jl(l);u!==null&&(t.splice(e,3),e-=3,Tc(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Un(t){function e(m){return ku(m,t)}vl!==null&&ku(vl,t),gl!==null&&ku(gl,t),Sl!==null&&ku(Sl,t),_n.forEach(e),Dn.forEach(e);for(var l=0;l<bl.length;l++){var a=bl[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<bl.length&&(l=bl[0],l.blockedOn===null);)Bd(l),l.blockedOn===null&&bl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],s=n[Ft]||null;if(typeof u=="function")s||jd(l);else if(s){var o=null;if(u&&u.hasAttribute("formAction")){if(n=u,s=u[Ft]||null)o=s.formAction;else if(Af(n)!==null)continue}else o=s.action;typeof o=="function"?l[a+1]=o:(l.splice(a,3),a-=3),jd(l)}}}function Rf(t){this._internalRoot=t}$u.prototype.render=Rf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(f(409));var l=e.current,a=de();Md(l,a,t,e,null,null)},$u.prototype.unmount=Rf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Md(t.current,2,null,t,null,null),Nu(),e[Vl]=null}};function $u(t){this._internalRoot=t}$u.prototype.unstable_scheduleHydration=function(t){if(t){var e=If();t={blockedOn:null,target:t,priority:e};for(var l=0;l<bl.length&&e!==0&&e<bl[l].priority;l++);bl.splice(l,0,t),l===0&&Bd(t)}};var wd=c.version;if(wd!=="19.1.0")throw Error(f(527,wd,"19.1.0"));w.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(f(188)):(t=Object.keys(t).join(","),Error(f(268,t)));return t=_(e),t=t!==null?S(t):null,t=t===null?null:t.stateNode,t};var ay={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:U,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Fu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Fu.isDisabled&&Fu.supportsFiber)try{xa=Fu.inject(ay),ue=Fu}catch{}}return Nn.createRoot=function(t,e){if(!d(t))throw Error(f(299));var l=!1,a="",n=Ir,u=to,s=eo,o=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(s=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(o=e.unstable_transitionCallbacks)),e=zd(t,1,!1,null,null,l,a,n,u,s,o,null),t[Vl]=e.current,cf(t),new Rf(e)},Nn.hydrateRoot=function(t,e,l){if(!d(t))throw Error(f(299));var a=!1,n="",u=Ir,s=to,o=eo,m=null,E=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(s=l.onCaughtError),l.onRecoverableError!==void 0&&(o=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(m=l.unstable_transitionCallbacks),l.formState!==void 0&&(E=l.formState)),e=zd(t,1,!0,e,l??null,a,n,u,s,o,m,E),e.context=Ud(null),l=e.current,a=de(),a=yi(a),n=ll(a),n.callback=null,al(l,n,a),l=a,e.current.lanes=l,qa(e,l),xe(e),t[Vl]=e.current,cf(t),new $u(e)},Nn.version="19.1.0",Nn}var kd;function yy(){if(kd)return Uf.exports;kd=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(c){console.error(c)}}return i(),Uf.exports=my(),Uf.exports}var vy=yy();/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gy=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Sy=i=>i.replace(/^([A-Z])|[\s-_]+(\w)/g,(c,r,f)=>f?f.toUpperCase():r.toLowerCase()),Wd=i=>{const c=Sy(i);return c.charAt(0).toUpperCase()+c.slice(1)},sh=(...i)=>i.filter((c,r,f)=>!!c&&c.trim()!==""&&f.indexOf(c)===r).join(" ").trim(),by=i=>{for(const c in i)if(c.startsWith("aria-")||c==="role"||c==="title")return!0};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var py={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ey=Qt.forwardRef(({color:i="currentColor",size:c=24,strokeWidth:r=2,absoluteStrokeWidth:f,className:d="",children:h,iconNode:y,...R},_)=>Qt.createElement("svg",{ref:_,...py,width:c,height:c,stroke:i,strokeWidth:f?Number(r)*24/Number(c):r,className:sh("lucide",d),...!h&&!by(R)&&{"aria-hidden":"true"},...R},[...y.map(([S,O])=>Qt.createElement(S,O)),...Array.isArray(h)?h:[h]]));/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rh=(i,c)=>{const r=Qt.forwardRef(({className:f,...d},h)=>Qt.createElement(Ey,{ref:h,iconNode:c,className:sh(`lucide-${gy(Wd(i))}`,`lucide-${i}`,f),...d}));return r.displayName=Wd(i),r};/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ty=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],Ay=rh("circle-alert",Ty);/**
 * @license lucide-react v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oy=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],Ry=rh("upload",Oy);function oh(i,c){return function(){return i.apply(c,arguments)}}const{toString:_y}=Object.prototype,{getPrototypeOf:Qf}=Object,{iterator:ai,toStringTag:dh}=Symbol,ni=(i=>c=>{const r=_y.call(c);return i[r]||(i[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),_e=i=>(i=i.toLowerCase(),c=>ni(c)===i),ui=i=>c=>typeof c===i,{isArray:Ma}=Array,qn=ui("undefined");function Dy(i){return i!==null&&!qn(i)&&i.constructor!==null&&!qn(i.constructor)&&le(i.constructor.isBuffer)&&i.constructor.isBuffer(i)}const hh=_e("ArrayBuffer");function zy(i){let c;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?c=ArrayBuffer.isView(i):c=i&&i.buffer&&hh(i.buffer),c}const Uy=ui("string"),le=ui("function"),mh=ui("number"),ii=i=>i!==null&&typeof i=="object",My=i=>i===!0||i===!1,Pu=i=>{if(ni(i)!=="object")return!1;const c=Qf(i);return(c===null||c===Object.prototype||Object.getPrototypeOf(c)===null)&&!(dh in i)&&!(ai in i)},Ny=_e("Date"),xy=_e("File"),Hy=_e("Blob"),qy=_e("FileList"),By=i=>ii(i)&&le(i.pipe),Cy=i=>{let c;return i&&(typeof FormData=="function"&&i instanceof FormData||le(i.append)&&((c=ni(i))==="formdata"||c==="object"&&le(i.toString)&&i.toString()==="[object FormData]"))},jy=_e("URLSearchParams"),[wy,Yy,Ly,Gy]=["ReadableStream","Request","Response","Headers"].map(_e),Xy=i=>i.trim?i.trim():i.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Bn(i,c,{allOwnKeys:r=!1}={}){if(i===null||typeof i>"u")return;let f,d;if(typeof i!="object"&&(i=[i]),Ma(i))for(f=0,d=i.length;f<d;f++)c.call(null,i[f],f,i);else{const h=r?Object.getOwnPropertyNames(i):Object.keys(i),y=h.length;let R;for(f=0;f<y;f++)R=h[f],c.call(null,i[R],R,i)}}function yh(i,c){c=c.toLowerCase();const r=Object.keys(i);let f=r.length,d;for(;f-- >0;)if(d=r[f],c===d.toLowerCase())return d;return null}const Gl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,vh=i=>!qn(i)&&i!==Gl;function Cf(){const{caseless:i}=vh(this)&&this||{},c={},r=(f,d)=>{const h=i&&yh(c,d)||d;Pu(c[h])&&Pu(f)?c[h]=Cf(c[h],f):Pu(f)?c[h]=Cf({},f):Ma(f)?c[h]=f.slice():c[h]=f};for(let f=0,d=arguments.length;f<d;f++)arguments[f]&&Bn(arguments[f],r);return c}const Qy=(i,c,r,{allOwnKeys:f}={})=>(Bn(c,(d,h)=>{r&&le(d)?i[h]=oh(d,r):i[h]=d},{allOwnKeys:f}),i),Zy=i=>(i.charCodeAt(0)===65279&&(i=i.slice(1)),i),Vy=(i,c,r,f)=>{i.prototype=Object.create(c.prototype,f),i.prototype.constructor=i,Object.defineProperty(i,"super",{value:c.prototype}),r&&Object.assign(i.prototype,r)},Ky=(i,c,r,f)=>{let d,h,y;const R={};if(c=c||{},i==null)return c;do{for(d=Object.getOwnPropertyNames(i),h=d.length;h-- >0;)y=d[h],(!f||f(y,i,c))&&!R[y]&&(c[y]=i[y],R[y]=!0);i=r!==!1&&Qf(i)}while(i&&(!r||r(i,c))&&i!==Object.prototype);return c},Jy=(i,c,r)=>{i=String(i),(r===void 0||r>i.length)&&(r=i.length),r-=c.length;const f=i.indexOf(c,r);return f!==-1&&f===r},ky=i=>{if(!i)return null;if(Ma(i))return i;let c=i.length;if(!mh(c))return null;const r=new Array(c);for(;c-- >0;)r[c]=i[c];return r},Wy=(i=>c=>i&&c instanceof i)(typeof Uint8Array<"u"&&Qf(Uint8Array)),$y=(i,c)=>{const f=(i&&i[ai]).call(i);let d;for(;(d=f.next())&&!d.done;){const h=d.value;c.call(i,h[0],h[1])}},Fy=(i,c)=>{let r;const f=[];for(;(r=i.exec(c))!==null;)f.push(r);return f},Py=_e("HTMLFormElement"),Iy=i=>i.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,f,d){return f.toUpperCase()+d}),$d=(({hasOwnProperty:i})=>(c,r)=>i.call(c,r))(Object.prototype),t1=_e("RegExp"),gh=(i,c)=>{const r=Object.getOwnPropertyDescriptors(i),f={};Bn(r,(d,h)=>{let y;(y=c(d,h,i))!==!1&&(f[h]=y||d)}),Object.defineProperties(i,f)},e1=i=>{gh(i,(c,r)=>{if(le(i)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const f=i[r];if(le(f)){if(c.enumerable=!1,"writable"in c){c.writable=!1;return}c.set||(c.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},l1=(i,c)=>{const r={},f=d=>{d.forEach(h=>{r[h]=!0})};return Ma(i)?f(i):f(String(i).split(c)),r},a1=()=>{},n1=(i,c)=>i!=null&&Number.isFinite(i=+i)?i:c;function u1(i){return!!(i&&le(i.append)&&i[dh]==="FormData"&&i[ai])}const i1=i=>{const c=new Array(10),r=(f,d)=>{if(ii(f)){if(c.indexOf(f)>=0)return;if(!("toJSON"in f)){c[d]=f;const h=Ma(f)?[]:{};return Bn(f,(y,R)=>{const _=r(y,d+1);!qn(_)&&(h[R]=_)}),c[d]=void 0,h}}return f};return r(i,0)},c1=_e("AsyncFunction"),f1=i=>i&&(ii(i)||le(i))&&le(i.then)&&le(i.catch),Sh=((i,c)=>i?setImmediate:c?((r,f)=>(Gl.addEventListener("message",({source:d,data:h})=>{d===Gl&&h===r&&f.length&&f.shift()()},!1),d=>{f.push(d),Gl.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",le(Gl.postMessage)),s1=typeof queueMicrotask<"u"?queueMicrotask.bind(Gl):typeof process<"u"&&process.nextTick||Sh,r1=i=>i!=null&&le(i[ai]),D={isArray:Ma,isArrayBuffer:hh,isBuffer:Dy,isFormData:Cy,isArrayBufferView:zy,isString:Uy,isNumber:mh,isBoolean:My,isObject:ii,isPlainObject:Pu,isReadableStream:wy,isRequest:Yy,isResponse:Ly,isHeaders:Gy,isUndefined:qn,isDate:Ny,isFile:xy,isBlob:Hy,isRegExp:t1,isFunction:le,isStream:By,isURLSearchParams:jy,isTypedArray:Wy,isFileList:qy,forEach:Bn,merge:Cf,extend:Qy,trim:Xy,stripBOM:Zy,inherits:Vy,toFlatObject:Ky,kindOf:ni,kindOfTest:_e,endsWith:Jy,toArray:ky,forEachEntry:$y,matchAll:Fy,isHTMLForm:Py,hasOwnProperty:$d,hasOwnProp:$d,reduceDescriptors:gh,freezeMethods:e1,toObjectSet:l1,toCamelCase:Iy,noop:a1,toFiniteNumber:n1,findKey:yh,global:Gl,isContextDefined:vh,isSpecCompliantForm:u1,toJSONObject:i1,isAsyncFn:c1,isThenable:f1,setImmediate:Sh,asap:s1,isIterable:r1};function I(i,c,r,f,d){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=i,this.name="AxiosError",c&&(this.code=c),r&&(this.config=r),f&&(this.request=f),d&&(this.response=d,this.status=d.status?d.status:null)}D.inherits(I,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:D.toJSONObject(this.config),code:this.code,status:this.status}}});const bh=I.prototype,ph={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(i=>{ph[i]={value:i}});Object.defineProperties(I,ph);Object.defineProperty(bh,"isAxiosError",{value:!0});I.from=(i,c,r,f,d,h)=>{const y=Object.create(bh);return D.toFlatObject(i,y,function(_){return _!==Error.prototype},R=>R!=="isAxiosError"),I.call(y,i.message,c,r,f,d),y.cause=i,y.name=i.name,h&&Object.assign(y,h),y};const o1=null;function jf(i){return D.isPlainObject(i)||D.isArray(i)}function Eh(i){return D.endsWith(i,"[]")?i.slice(0,-2):i}function Fd(i,c,r){return i?i.concat(c).map(function(d,h){return d=Eh(d),!r&&h?"["+d+"]":d}).join(r?".":""):c}function d1(i){return D.isArray(i)&&!i.some(jf)}const h1=D.toFlatObject(D,{},null,function(c){return/^is[A-Z]/.test(c)});function ci(i,c,r){if(!D.isObject(i))throw new TypeError("target must be an object");c=c||new FormData,r=D.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(C,q){return!D.isUndefined(q[C])});const f=r.metaTokens,d=r.visitor||O,h=r.dots,y=r.indexes,_=(r.Blob||typeof Blob<"u"&&Blob)&&D.isSpecCompliantForm(c);if(!D.isFunction(d))throw new TypeError("visitor must be a function");function S(B){if(B===null)return"";if(D.isDate(B))return B.toISOString();if(D.isBoolean(B))return B.toString();if(!_&&D.isBlob(B))throw new I("Blob is not supported. Use a Buffer instead.");return D.isArrayBuffer(B)||D.isTypedArray(B)?_&&typeof Blob=="function"?new Blob([B]):Buffer.from(B):B}function O(B,C,q){let ut=B;if(B&&!q&&typeof B=="object"){if(D.endsWith(C,"{}"))C=f?C:C.slice(0,-2),B=JSON.stringify(B);else if(D.isArray(B)&&d1(B)||(D.isFileList(B)||D.endsWith(C,"[]"))&&(ut=D.toArray(B)))return C=Eh(C),ut.forEach(function(W,dt){!(D.isUndefined(W)||W===null)&&c.append(y===!0?Fd([C],dt,h):y===null?C:C+"[]",S(W))}),!1}return jf(B)?!0:(c.append(Fd(q,C,h),S(B)),!1)}const H=[],L=Object.assign(h1,{defaultVisitor:O,convertValue:S,isVisitable:jf});function Z(B,C){if(!D.isUndefined(B)){if(H.indexOf(B)!==-1)throw Error("Circular reference detected in "+C.join("."));H.push(B),D.forEach(B,function(ut,F){(!(D.isUndefined(ut)||ut===null)&&d.call(c,ut,D.isString(F)?F.trim():F,C,L))===!0&&Z(ut,C?C.concat(F):[F])}),H.pop()}}if(!D.isObject(i))throw new TypeError("data must be an object");return Z(i),c}function Pd(i){const c={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(i).replace(/[!'()~]|%20|%00/g,function(f){return c[f]})}function Zf(i,c){this._pairs=[],i&&ci(i,this,c)}const Th=Zf.prototype;Th.append=function(c,r){this._pairs.push([c,r])};Th.toString=function(c){const r=c?function(f){return c.call(this,f,Pd)}:Pd;return this._pairs.map(function(d){return r(d[0])+"="+r(d[1])},"").join("&")};function m1(i){return encodeURIComponent(i).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ah(i,c,r){if(!c)return i;const f=r&&r.encode||m1;D.isFunction(r)&&(r={serialize:r});const d=r&&r.serialize;let h;if(d?h=d(c,r):h=D.isURLSearchParams(c)?c.toString():new Zf(c,r).toString(f),h){const y=i.indexOf("#");y!==-1&&(i=i.slice(0,y)),i+=(i.indexOf("?")===-1?"?":"&")+h}return i}class Id{constructor(){this.handlers=[]}use(c,r,f){return this.handlers.push({fulfilled:c,rejected:r,synchronous:f?f.synchronous:!1,runWhen:f?f.runWhen:null}),this.handlers.length-1}eject(c){this.handlers[c]&&(this.handlers[c]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(c){D.forEach(this.handlers,function(f){f!==null&&c(f)})}}const Oh={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},y1=typeof URLSearchParams<"u"?URLSearchParams:Zf,v1=typeof FormData<"u"?FormData:null,g1=typeof Blob<"u"?Blob:null,S1={isBrowser:!0,classes:{URLSearchParams:y1,FormData:v1,Blob:g1},protocols:["http","https","file","blob","url","data"]},Vf=typeof window<"u"&&typeof document<"u",wf=typeof navigator=="object"&&navigator||void 0,b1=Vf&&(!wf||["ReactNative","NativeScript","NS"].indexOf(wf.product)<0),p1=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",E1=Vf&&window.location.href||"http://localhost",T1=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Vf,hasStandardBrowserEnv:b1,hasStandardBrowserWebWorkerEnv:p1,navigator:wf,origin:E1},Symbol.toStringTag,{value:"Module"})),Wt={...T1,...S1};function A1(i,c){return ci(i,new Wt.classes.URLSearchParams,Object.assign({visitor:function(r,f,d,h){return Wt.isNode&&D.isBuffer(r)?(this.append(f,r.toString("base64")),!1):h.defaultVisitor.apply(this,arguments)}},c))}function O1(i){return D.matchAll(/\w+|\[(\w*)]/g,i).map(c=>c[0]==="[]"?"":c[1]||c[0])}function R1(i){const c={},r=Object.keys(i);let f;const d=r.length;let h;for(f=0;f<d;f++)h=r[f],c[h]=i[h];return c}function Rh(i){function c(r,f,d,h){let y=r[h++];if(y==="__proto__")return!0;const R=Number.isFinite(+y),_=h>=r.length;return y=!y&&D.isArray(d)?d.length:y,_?(D.hasOwnProp(d,y)?d[y]=[d[y],f]:d[y]=f,!R):((!d[y]||!D.isObject(d[y]))&&(d[y]=[]),c(r,f,d[y],h)&&D.isArray(d[y])&&(d[y]=R1(d[y])),!R)}if(D.isFormData(i)&&D.isFunction(i.entries)){const r={};return D.forEachEntry(i,(f,d)=>{c(O1(f),d,r,0)}),r}return null}function _1(i,c,r){if(D.isString(i))try{return(c||JSON.parse)(i),D.trim(i)}catch(f){if(f.name!=="SyntaxError")throw f}return(r||JSON.stringify)(i)}const Cn={transitional:Oh,adapter:["xhr","http","fetch"],transformRequest:[function(c,r){const f=r.getContentType()||"",d=f.indexOf("application/json")>-1,h=D.isObject(c);if(h&&D.isHTMLForm(c)&&(c=new FormData(c)),D.isFormData(c))return d?JSON.stringify(Rh(c)):c;if(D.isArrayBuffer(c)||D.isBuffer(c)||D.isStream(c)||D.isFile(c)||D.isBlob(c)||D.isReadableStream(c))return c;if(D.isArrayBufferView(c))return c.buffer;if(D.isURLSearchParams(c))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),c.toString();let R;if(h){if(f.indexOf("application/x-www-form-urlencoded")>-1)return A1(c,this.formSerializer).toString();if((R=D.isFileList(c))||f.indexOf("multipart/form-data")>-1){const _=this.env&&this.env.FormData;return ci(R?{"files[]":c}:c,_&&new _,this.formSerializer)}}return h||d?(r.setContentType("application/json",!1),_1(c)):c}],transformResponse:[function(c){const r=this.transitional||Cn.transitional,f=r&&r.forcedJSONParsing,d=this.responseType==="json";if(D.isResponse(c)||D.isReadableStream(c))return c;if(c&&D.isString(c)&&(f&&!this.responseType||d)){const y=!(r&&r.silentJSONParsing)&&d;try{return JSON.parse(c)}catch(R){if(y)throw R.name==="SyntaxError"?I.from(R,I.ERR_BAD_RESPONSE,this,null,this.response):R}}return c}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Wt.classes.FormData,Blob:Wt.classes.Blob},validateStatus:function(c){return c>=200&&c<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};D.forEach(["delete","get","head","post","put","patch"],i=>{Cn.headers[i]={}});const D1=D.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),z1=i=>{const c={};let r,f,d;return i&&i.split(`
`).forEach(function(y){d=y.indexOf(":"),r=y.substring(0,d).trim().toLowerCase(),f=y.substring(d+1).trim(),!(!r||c[r]&&D1[r])&&(r==="set-cookie"?c[r]?c[r].push(f):c[r]=[f]:c[r]=c[r]?c[r]+", "+f:f)}),c},th=Symbol("internals");function xn(i){return i&&String(i).trim().toLowerCase()}function Iu(i){return i===!1||i==null?i:D.isArray(i)?i.map(Iu):String(i)}function U1(i){const c=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let f;for(;f=r.exec(i);)c[f[1]]=f[2];return c}const M1=i=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(i.trim());function Hf(i,c,r,f,d){if(D.isFunction(f))return f.call(this,c,r);if(d&&(c=r),!!D.isString(c)){if(D.isString(f))return c.indexOf(f)!==-1;if(D.isRegExp(f))return f.test(c)}}function N1(i){return i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(c,r,f)=>r.toUpperCase()+f)}function x1(i,c){const r=D.toCamelCase(" "+c);["get","set","has"].forEach(f=>{Object.defineProperty(i,f+r,{value:function(d,h,y){return this[f].call(this,c,d,h,y)},configurable:!0})})}let ae=class{constructor(c){c&&this.set(c)}set(c,r,f){const d=this;function h(R,_,S){const O=xn(_);if(!O)throw new Error("header name must be a non-empty string");const H=D.findKey(d,O);(!H||d[H]===void 0||S===!0||S===void 0&&d[H]!==!1)&&(d[H||_]=Iu(R))}const y=(R,_)=>D.forEach(R,(S,O)=>h(S,O,_));if(D.isPlainObject(c)||c instanceof this.constructor)y(c,r);else if(D.isString(c)&&(c=c.trim())&&!M1(c))y(z1(c),r);else if(D.isObject(c)&&D.isIterable(c)){let R={},_,S;for(const O of c){if(!D.isArray(O))throw TypeError("Object iterator must return a key-value pair");R[S=O[0]]=(_=R[S])?D.isArray(_)?[..._,O[1]]:[_,O[1]]:O[1]}y(R,r)}else c!=null&&h(r,c,f);return this}get(c,r){if(c=xn(c),c){const f=D.findKey(this,c);if(f){const d=this[f];if(!r)return d;if(r===!0)return U1(d);if(D.isFunction(r))return r.call(this,d,f);if(D.isRegExp(r))return r.exec(d);throw new TypeError("parser must be boolean|regexp|function")}}}has(c,r){if(c=xn(c),c){const f=D.findKey(this,c);return!!(f&&this[f]!==void 0&&(!r||Hf(this,this[f],f,r)))}return!1}delete(c,r){const f=this;let d=!1;function h(y){if(y=xn(y),y){const R=D.findKey(f,y);R&&(!r||Hf(f,f[R],R,r))&&(delete f[R],d=!0)}}return D.isArray(c)?c.forEach(h):h(c),d}clear(c){const r=Object.keys(this);let f=r.length,d=!1;for(;f--;){const h=r[f];(!c||Hf(this,this[h],h,c,!0))&&(delete this[h],d=!0)}return d}normalize(c){const r=this,f={};return D.forEach(this,(d,h)=>{const y=D.findKey(f,h);if(y){r[y]=Iu(d),delete r[h];return}const R=c?N1(h):String(h).trim();R!==h&&delete r[h],r[R]=Iu(d),f[R]=!0}),this}concat(...c){return this.constructor.concat(this,...c)}toJSON(c){const r=Object.create(null);return D.forEach(this,(f,d)=>{f!=null&&f!==!1&&(r[d]=c&&D.isArray(f)?f.join(", "):f)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([c,r])=>c+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(c){return c instanceof this?c:new this(c)}static concat(c,...r){const f=new this(c);return r.forEach(d=>f.set(d)),f}static accessor(c){const f=(this[th]=this[th]={accessors:{}}).accessors,d=this.prototype;function h(y){const R=xn(y);f[R]||(x1(d,y),f[R]=!0)}return D.isArray(c)?c.forEach(h):h(c),this}};ae.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);D.reduceDescriptors(ae.prototype,({value:i},c)=>{let r=c[0].toUpperCase()+c.slice(1);return{get:()=>i,set(f){this[r]=f}}});D.freezeMethods(ae);function qf(i,c){const r=this||Cn,f=c||r,d=ae.from(f.headers);let h=f.data;return D.forEach(i,function(R){h=R.call(r,h,d.normalize(),c?c.status:void 0)}),d.normalize(),h}function _h(i){return!!(i&&i.__CANCEL__)}function Na(i,c,r){I.call(this,i??"canceled",I.ERR_CANCELED,c,r),this.name="CanceledError"}D.inherits(Na,I,{__CANCEL__:!0});function Dh(i,c,r){const f=r.config.validateStatus;!r.status||!f||f(r.status)?i(r):c(new I("Request failed with status code "+r.status,[I.ERR_BAD_REQUEST,I.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function H1(i){const c=/^([-+\w]{1,25})(:?\/\/|:)/.exec(i);return c&&c[1]||""}function q1(i,c){i=i||10;const r=new Array(i),f=new Array(i);let d=0,h=0,y;return c=c!==void 0?c:1e3,function(_){const S=Date.now(),O=f[h];y||(y=S),r[d]=_,f[d]=S;let H=h,L=0;for(;H!==d;)L+=r[H++],H=H%i;if(d=(d+1)%i,d===h&&(h=(h+1)%i),S-y<c)return;const Z=O&&S-O;return Z?Math.round(L*1e3/Z):void 0}}function B1(i,c){let r=0,f=1e3/c,d,h;const y=(S,O=Date.now())=>{r=O,d=null,h&&(clearTimeout(h),h=null),i.apply(null,S)};return[(...S)=>{const O=Date.now(),H=O-r;H>=f?y(S,O):(d=S,h||(h=setTimeout(()=>{h=null,y(d)},f-H)))},()=>d&&y(d)]}const ei=(i,c,r=3)=>{let f=0;const d=q1(50,250);return B1(h=>{const y=h.loaded,R=h.lengthComputable?h.total:void 0,_=y-f,S=d(_),O=y<=R;f=y;const H={loaded:y,total:R,progress:R?y/R:void 0,bytes:_,rate:S||void 0,estimated:S&&R&&O?(R-y)/S:void 0,event:h,lengthComputable:R!=null,[c?"download":"upload"]:!0};i(H)},r)},eh=(i,c)=>{const r=i!=null;return[f=>c[0]({lengthComputable:r,total:i,loaded:f}),c[1]]},lh=i=>(...c)=>D.asap(()=>i(...c)),C1=Wt.hasStandardBrowserEnv?((i,c)=>r=>(r=new URL(r,Wt.origin),i.protocol===r.protocol&&i.host===r.host&&(c||i.port===r.port)))(new URL(Wt.origin),Wt.navigator&&/(msie|trident)/i.test(Wt.navigator.userAgent)):()=>!0,j1=Wt.hasStandardBrowserEnv?{write(i,c,r,f,d,h){const y=[i+"="+encodeURIComponent(c)];D.isNumber(r)&&y.push("expires="+new Date(r).toGMTString()),D.isString(f)&&y.push("path="+f),D.isString(d)&&y.push("domain="+d),h===!0&&y.push("secure"),document.cookie=y.join("; ")},read(i){const c=document.cookie.match(new RegExp("(^|;\\s*)("+i+")=([^;]*)"));return c?decodeURIComponent(c[3]):null},remove(i){this.write(i,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function w1(i){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(i)}function Y1(i,c){return c?i.replace(/\/?\/$/,"")+"/"+c.replace(/^\/+/,""):i}function zh(i,c,r){let f=!w1(c);return i&&(f||r==!1)?Y1(i,c):c}const ah=i=>i instanceof ae?{...i}:i;function Ql(i,c){c=c||{};const r={};function f(S,O,H,L){return D.isPlainObject(S)&&D.isPlainObject(O)?D.merge.call({caseless:L},S,O):D.isPlainObject(O)?D.merge({},O):D.isArray(O)?O.slice():O}function d(S,O,H,L){if(D.isUndefined(O)){if(!D.isUndefined(S))return f(void 0,S,H,L)}else return f(S,O,H,L)}function h(S,O){if(!D.isUndefined(O))return f(void 0,O)}function y(S,O){if(D.isUndefined(O)){if(!D.isUndefined(S))return f(void 0,S)}else return f(void 0,O)}function R(S,O,H){if(H in c)return f(S,O);if(H in i)return f(void 0,S)}const _={url:h,method:h,data:h,baseURL:y,transformRequest:y,transformResponse:y,paramsSerializer:y,timeout:y,timeoutMessage:y,withCredentials:y,withXSRFToken:y,adapter:y,responseType:y,xsrfCookieName:y,xsrfHeaderName:y,onUploadProgress:y,onDownloadProgress:y,decompress:y,maxContentLength:y,maxBodyLength:y,beforeRedirect:y,transport:y,httpAgent:y,httpsAgent:y,cancelToken:y,socketPath:y,responseEncoding:y,validateStatus:R,headers:(S,O,H)=>d(ah(S),ah(O),H,!0)};return D.forEach(Object.keys(Object.assign({},i,c)),function(O){const H=_[O]||d,L=H(i[O],c[O],O);D.isUndefined(L)&&H!==R||(r[O]=L)}),r}const Uh=i=>{const c=Ql({},i);let{data:r,withXSRFToken:f,xsrfHeaderName:d,xsrfCookieName:h,headers:y,auth:R}=c;c.headers=y=ae.from(y),c.url=Ah(zh(c.baseURL,c.url,c.allowAbsoluteUrls),i.params,i.paramsSerializer),R&&y.set("Authorization","Basic "+btoa((R.username||"")+":"+(R.password?unescape(encodeURIComponent(R.password)):"")));let _;if(D.isFormData(r)){if(Wt.hasStandardBrowserEnv||Wt.hasStandardBrowserWebWorkerEnv)y.setContentType(void 0);else if((_=y.getContentType())!==!1){const[S,...O]=_?_.split(";").map(H=>H.trim()).filter(Boolean):[];y.setContentType([S||"multipart/form-data",...O].join("; "))}}if(Wt.hasStandardBrowserEnv&&(f&&D.isFunction(f)&&(f=f(c)),f||f!==!1&&C1(c.url))){const S=d&&h&&j1.read(h);S&&y.set(d,S)}return c},L1=typeof XMLHttpRequest<"u",G1=L1&&function(i){return new Promise(function(r,f){const d=Uh(i);let h=d.data;const y=ae.from(d.headers).normalize();let{responseType:R,onUploadProgress:_,onDownloadProgress:S}=d,O,H,L,Z,B;function C(){Z&&Z(),B&&B(),d.cancelToken&&d.cancelToken.unsubscribe(O),d.signal&&d.signal.removeEventListener("abort",O)}let q=new XMLHttpRequest;q.open(d.method.toUpperCase(),d.url,!0),q.timeout=d.timeout;function ut(){if(!q)return;const W=ae.from("getAllResponseHeaders"in q&&q.getAllResponseHeaders()),G={data:!R||R==="text"||R==="json"?q.responseText:q.response,status:q.status,statusText:q.statusText,headers:W,config:i,request:q};Dh(function(Tt){r(Tt),C()},function(Tt){f(Tt),C()},G),q=null}"onloadend"in q?q.onloadend=ut:q.onreadystatechange=function(){!q||q.readyState!==4||q.status===0&&!(q.responseURL&&q.responseURL.indexOf("file:")===0)||setTimeout(ut)},q.onabort=function(){q&&(f(new I("Request aborted",I.ECONNABORTED,i,q)),q=null)},q.onerror=function(){f(new I("Network Error",I.ERR_NETWORK,i,q)),q=null},q.ontimeout=function(){let dt=d.timeout?"timeout of "+d.timeout+"ms exceeded":"timeout exceeded";const G=d.transitional||Oh;d.timeoutErrorMessage&&(dt=d.timeoutErrorMessage),f(new I(dt,G.clarifyTimeoutError?I.ETIMEDOUT:I.ECONNABORTED,i,q)),q=null},h===void 0&&y.setContentType(null),"setRequestHeader"in q&&D.forEach(y.toJSON(),function(dt,G){q.setRequestHeader(G,dt)}),D.isUndefined(d.withCredentials)||(q.withCredentials=!!d.withCredentials),R&&R!=="json"&&(q.responseType=d.responseType),S&&([L,B]=ei(S,!0),q.addEventListener("progress",L)),_&&q.upload&&([H,Z]=ei(_),q.upload.addEventListener("progress",H),q.upload.addEventListener("loadend",Z)),(d.cancelToken||d.signal)&&(O=W=>{q&&(f(!W||W.type?new Na(null,i,q):W),q.abort(),q=null)},d.cancelToken&&d.cancelToken.subscribe(O),d.signal&&(d.signal.aborted?O():d.signal.addEventListener("abort",O)));const F=H1(d.url);if(F&&Wt.protocols.indexOf(F)===-1){f(new I("Unsupported protocol "+F+":",I.ERR_BAD_REQUEST,i));return}q.send(h||null)})},X1=(i,c)=>{const{length:r}=i=i?i.filter(Boolean):[];if(c||r){let f=new AbortController,d;const h=function(S){if(!d){d=!0,R();const O=S instanceof Error?S:this.reason;f.abort(O instanceof I?O:new Na(O instanceof Error?O.message:O))}};let y=c&&setTimeout(()=>{y=null,h(new I(`timeout ${c} of ms exceeded`,I.ETIMEDOUT))},c);const R=()=>{i&&(y&&clearTimeout(y),y=null,i.forEach(S=>{S.unsubscribe?S.unsubscribe(h):S.removeEventListener("abort",h)}),i=null)};i.forEach(S=>S.addEventListener("abort",h));const{signal:_}=f;return _.unsubscribe=()=>D.asap(R),_}},Q1=function*(i,c){let r=i.byteLength;if(r<c){yield i;return}let f=0,d;for(;f<r;)d=f+c,yield i.slice(f,d),f=d},Z1=async function*(i,c){for await(const r of V1(i))yield*Q1(r,c)},V1=async function*(i){if(i[Symbol.asyncIterator]){yield*i;return}const c=i.getReader();try{for(;;){const{done:r,value:f}=await c.read();if(r)break;yield f}}finally{await c.cancel()}},nh=(i,c,r,f)=>{const d=Z1(i,c);let h=0,y,R=_=>{y||(y=!0,f&&f(_))};return new ReadableStream({async pull(_){try{const{done:S,value:O}=await d.next();if(S){R(),_.close();return}let H=O.byteLength;if(r){let L=h+=H;r(L)}_.enqueue(new Uint8Array(O))}catch(S){throw R(S),S}},cancel(_){return R(_),d.return()}},{highWaterMark:2})},fi=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Mh=fi&&typeof ReadableStream=="function",K1=fi&&(typeof TextEncoder=="function"?(i=>c=>i.encode(c))(new TextEncoder):async i=>new Uint8Array(await new Response(i).arrayBuffer())),Nh=(i,...c)=>{try{return!!i(...c)}catch{return!1}},J1=Mh&&Nh(()=>{let i=!1;const c=new Request(Wt.origin,{body:new ReadableStream,method:"POST",get duplex(){return i=!0,"half"}}).headers.has("Content-Type");return i&&!c}),uh=64*1024,Yf=Mh&&Nh(()=>D.isReadableStream(new Response("").body)),li={stream:Yf&&(i=>i.body)};fi&&(i=>{["text","arrayBuffer","blob","formData","stream"].forEach(c=>{!li[c]&&(li[c]=D.isFunction(i[c])?r=>r[c]():(r,f)=>{throw new I(`Response type '${c}' is not supported`,I.ERR_NOT_SUPPORT,f)})})})(new Response);const k1=async i=>{if(i==null)return 0;if(D.isBlob(i))return i.size;if(D.isSpecCompliantForm(i))return(await new Request(Wt.origin,{method:"POST",body:i}).arrayBuffer()).byteLength;if(D.isArrayBufferView(i)||D.isArrayBuffer(i))return i.byteLength;if(D.isURLSearchParams(i)&&(i=i+""),D.isString(i))return(await K1(i)).byteLength},W1=async(i,c)=>{const r=D.toFiniteNumber(i.getContentLength());return r??k1(c)},$1=fi&&(async i=>{let{url:c,method:r,data:f,signal:d,cancelToken:h,timeout:y,onDownloadProgress:R,onUploadProgress:_,responseType:S,headers:O,withCredentials:H="same-origin",fetchOptions:L}=Uh(i);S=S?(S+"").toLowerCase():"text";let Z=X1([d,h&&h.toAbortSignal()],y),B;const C=Z&&Z.unsubscribe&&(()=>{Z.unsubscribe()});let q;try{if(_&&J1&&r!=="get"&&r!=="head"&&(q=await W1(O,f))!==0){let G=new Request(c,{method:"POST",body:f,duplex:"half"}),rt;if(D.isFormData(f)&&(rt=G.headers.get("content-type"))&&O.setContentType(rt),G.body){const[Tt,yt]=eh(q,ei(lh(_)));f=nh(G.body,uh,Tt,yt)}}D.isString(H)||(H=H?"include":"omit");const ut="credentials"in Request.prototype;B=new Request(c,{...L,signal:Z,method:r.toUpperCase(),headers:O.normalize().toJSON(),body:f,duplex:"half",credentials:ut?H:void 0});let F=await fetch(B,L);const W=Yf&&(S==="stream"||S==="response");if(Yf&&(R||W&&C)){const G={};["status","statusText","headers"].forEach(Bt=>{G[Bt]=F[Bt]});const rt=D.toFiniteNumber(F.headers.get("content-length")),[Tt,yt]=R&&eh(rt,ei(lh(R),!0))||[];F=new Response(nh(F.body,uh,Tt,()=>{yt&&yt(),C&&C()}),G)}S=S||"text";let dt=await li[D.findKey(li,S)||"text"](F,i);return!W&&C&&C(),await new Promise((G,rt)=>{Dh(G,rt,{data:dt,headers:ae.from(F.headers),status:F.status,statusText:F.statusText,config:i,request:B})})}catch(ut){throw C&&C(),ut&&ut.name==="TypeError"&&/Load failed|fetch/i.test(ut.message)?Object.assign(new I("Network Error",I.ERR_NETWORK,i,B),{cause:ut.cause||ut}):I.from(ut,ut&&ut.code,i,B)}}),Lf={http:o1,xhr:G1,fetch:$1};D.forEach(Lf,(i,c)=>{if(i){try{Object.defineProperty(i,"name",{value:c})}catch{}Object.defineProperty(i,"adapterName",{value:c})}});const ih=i=>`- ${i}`,F1=i=>D.isFunction(i)||i===null||i===!1,xh={getAdapter:i=>{i=D.isArray(i)?i:[i];const{length:c}=i;let r,f;const d={};for(let h=0;h<c;h++){r=i[h];let y;if(f=r,!F1(r)&&(f=Lf[(y=String(r)).toLowerCase()],f===void 0))throw new I(`Unknown adapter '${y}'`);if(f)break;d[y||"#"+h]=f}if(!f){const h=Object.entries(d).map(([R,_])=>`adapter ${R} `+(_===!1?"is not supported by the environment":"is not available in the build"));let y=c?h.length>1?`since :
`+h.map(ih).join(`
`):" "+ih(h[0]):"as no adapter specified";throw new I("There is no suitable adapter to dispatch the request "+y,"ERR_NOT_SUPPORT")}return f},adapters:Lf};function Bf(i){if(i.cancelToken&&i.cancelToken.throwIfRequested(),i.signal&&i.signal.aborted)throw new Na(null,i)}function ch(i){return Bf(i),i.headers=ae.from(i.headers),i.data=qf.call(i,i.transformRequest),["post","put","patch"].indexOf(i.method)!==-1&&i.headers.setContentType("application/x-www-form-urlencoded",!1),xh.getAdapter(i.adapter||Cn.adapter)(i).then(function(f){return Bf(i),f.data=qf.call(i,i.transformResponse,f),f.headers=ae.from(f.headers),f},function(f){return _h(f)||(Bf(i),f&&f.response&&(f.response.data=qf.call(i,i.transformResponse,f.response),f.response.headers=ae.from(f.response.headers))),Promise.reject(f)})}const Hh="1.10.0",si={};["object","boolean","number","function","string","symbol"].forEach((i,c)=>{si[i]=function(f){return typeof f===i||"a"+(c<1?"n ":" ")+i}});const fh={};si.transitional=function(c,r,f){function d(h,y){return"[Axios v"+Hh+"] Transitional option '"+h+"'"+y+(f?". "+f:"")}return(h,y,R)=>{if(c===!1)throw new I(d(y," has been removed"+(r?" in "+r:"")),I.ERR_DEPRECATED);return r&&!fh[y]&&(fh[y]=!0,console.warn(d(y," has been deprecated since v"+r+" and will be removed in the near future"))),c?c(h,y,R):!0}};si.spelling=function(c){return(r,f)=>(console.warn(`${f} is likely a misspelling of ${c}`),!0)};function P1(i,c,r){if(typeof i!="object")throw new I("options must be an object",I.ERR_BAD_OPTION_VALUE);const f=Object.keys(i);let d=f.length;for(;d-- >0;){const h=f[d],y=c[h];if(y){const R=i[h],_=R===void 0||y(R,h,i);if(_!==!0)throw new I("option "+h+" must be "+_,I.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new I("Unknown option "+h,I.ERR_BAD_OPTION)}}const ti={assertOptions:P1,validators:si},He=ti.validators;let Xl=class{constructor(c){this.defaults=c||{},this.interceptors={request:new Id,response:new Id}}async request(c,r){try{return await this._request(c,r)}catch(f){if(f instanceof Error){let d={};Error.captureStackTrace?Error.captureStackTrace(d):d=new Error;const h=d.stack?d.stack.replace(/^.+\n/,""):"";try{f.stack?h&&!String(f.stack).endsWith(h.replace(/^.+\n.+\n/,""))&&(f.stack+=`
`+h):f.stack=h}catch{}}throw f}}_request(c,r){typeof c=="string"?(r=r||{},r.url=c):r=c||{},r=Ql(this.defaults,r);const{transitional:f,paramsSerializer:d,headers:h}=r;f!==void 0&&ti.assertOptions(f,{silentJSONParsing:He.transitional(He.boolean),forcedJSONParsing:He.transitional(He.boolean),clarifyTimeoutError:He.transitional(He.boolean)},!1),d!=null&&(D.isFunction(d)?r.paramsSerializer={serialize:d}:ti.assertOptions(d,{encode:He.function,serialize:He.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),ti.assertOptions(r,{baseUrl:He.spelling("baseURL"),withXsrfToken:He.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let y=h&&D.merge(h.common,h[r.method]);h&&D.forEach(["delete","get","head","post","put","patch","common"],B=>{delete h[B]}),r.headers=ae.concat(y,h);const R=[];let _=!0;this.interceptors.request.forEach(function(C){typeof C.runWhen=="function"&&C.runWhen(r)===!1||(_=_&&C.synchronous,R.unshift(C.fulfilled,C.rejected))});const S=[];this.interceptors.response.forEach(function(C){S.push(C.fulfilled,C.rejected)});let O,H=0,L;if(!_){const B=[ch.bind(this),void 0];for(B.unshift.apply(B,R),B.push.apply(B,S),L=B.length,O=Promise.resolve(r);H<L;)O=O.then(B[H++],B[H++]);return O}L=R.length;let Z=r;for(H=0;H<L;){const B=R[H++],C=R[H++];try{Z=B(Z)}catch(q){C.call(this,q);break}}try{O=ch.call(this,Z)}catch(B){return Promise.reject(B)}for(H=0,L=S.length;H<L;)O=O.then(S[H++],S[H++]);return O}getUri(c){c=Ql(this.defaults,c);const r=zh(c.baseURL,c.url,c.allowAbsoluteUrls);return Ah(r,c.params,c.paramsSerializer)}};D.forEach(["delete","get","head","options"],function(c){Xl.prototype[c]=function(r,f){return this.request(Ql(f||{},{method:c,url:r,data:(f||{}).data}))}});D.forEach(["post","put","patch"],function(c){function r(f){return function(h,y,R){return this.request(Ql(R||{},{method:c,headers:f?{"Content-Type":"multipart/form-data"}:{},url:h,data:y}))}}Xl.prototype[c]=r(),Xl.prototype[c+"Form"]=r(!0)});let I1=class qh{constructor(c){if(typeof c!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(h){r=h});const f=this;this.promise.then(d=>{if(!f._listeners)return;let h=f._listeners.length;for(;h-- >0;)f._listeners[h](d);f._listeners=null}),this.promise.then=d=>{let h;const y=new Promise(R=>{f.subscribe(R),h=R}).then(d);return y.cancel=function(){f.unsubscribe(h)},y},c(function(h,y,R){f.reason||(f.reason=new Na(h,y,R),r(f.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(c){if(this.reason){c(this.reason);return}this._listeners?this._listeners.push(c):this._listeners=[c]}unsubscribe(c){if(!this._listeners)return;const r=this._listeners.indexOf(c);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const c=new AbortController,r=f=>{c.abort(f)};return this.subscribe(r),c.signal.unsubscribe=()=>this.unsubscribe(r),c.signal}static source(){let c;return{token:new qh(function(d){c=d}),cancel:c}}};function tv(i){return function(r){return i.apply(null,r)}}function ev(i){return D.isObject(i)&&i.isAxiosError===!0}const Gf={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Gf).forEach(([i,c])=>{Gf[c]=i});function Bh(i){const c=new Xl(i),r=oh(Xl.prototype.request,c);return D.extend(r,Xl.prototype,c,{allOwnKeys:!0}),D.extend(r,c,null,{allOwnKeys:!0}),r.create=function(d){return Bh(Ql(i,d))},r}const Mt=Bh(Cn);Mt.Axios=Xl;Mt.CanceledError=Na;Mt.CancelToken=I1;Mt.isCancel=_h;Mt.VERSION=Hh;Mt.toFormData=ci;Mt.AxiosError=I;Mt.Cancel=Mt.CanceledError;Mt.all=function(c){return Promise.all(c)};Mt.spread=tv;Mt.isAxiosError=ev;Mt.mergeConfig=Ql;Mt.AxiosHeaders=ae;Mt.formToJSON=i=>Rh(D.isHTMLForm(i)?new FormData(i):i);Mt.getAdapter=xh.getAdapter;Mt.HttpStatusCode=Gf;Mt.default=Mt;const{Axios:fv,AxiosError:sv,CanceledError:rv,isCancel:ov,CancelToken:dv,VERSION:hv,all:mv,Cancel:yv,isAxiosError:vv,spread:gv,toFormData:Sv,AxiosHeaders:bv,HttpStatusCode:pv,formToJSON:Ev,getAdapter:Tv,mergeConfig:Av}=Mt;class lv{constructor(){_f(this,"client");_f(this,"initData",null);this.client=Mt.create({baseURL:"/api/v1",timeout:3e4}),this.client.interceptors.request.use(c=>(this.initData&&(c.headers.Authorization=`Bearer ${this.initData}`),c)),this.client.interceptors.response.use(c=>c,c=>{var r;throw console.error("API Error:",((r=c.response)==null?void 0:r.data)||c.message),c})}setInitData(c){this.initData=c}async getUserInfo(){return(await this.client.get("/users/me")).data}async getUserStatus(){return(await this.client.get("/user-status")).data}async getHistory(){return(await this.client.get("/history")).data}async uploadFile(c){const r=new FormData;return r.append("file",c),(await this.client.post("/upload",r,{headers:{"Content-Type":"multipart/form-data"}})).data}async getSummary(c){return(await this.client.get(`/summary/${c}`)).data}}const Hn=new lv;function Ch(){const[i,c]=Qt.useState({isReady:!1,initData:null,user:null,error:null});return Qt.useEffect(()=>{var y,R;const h=(y=window.Telegram)==null?void 0:y.WebApp;if(!h){c(_=>({..._,error:"Приложение должно быть запущено в Telegram"}));return}try{if(h.ready(),h.expand(),!h.initData){c(_=>({..._,error:"Отсутствуют данные авторизации Telegram"}));return}if(c({isReady:!0,initData:h.initData,user:((R=h.initDataUnsafe)==null?void 0:R.user)||null,error:null}),h.themeParams){const _=document.documentElement,S=document.body;if(Object.entries(h.themeParams).forEach(([H,L])=>{L&&_.style.setProperty(`--tg-theme-${H.replace(/_/g,"-")}`,L)}),h.themeParams.bg_color&&(S.style.backgroundColor=h.themeParams.bg_color),h.themeParams.text_color&&(S.style.color=h.themeParams.text_color),h.themeParams.bg_color){const H=h.themeParams.bg_color,L=parseInt(H.slice(1),16),Z=L>>16&255,B=L>>8&255,C=L&255;(Z*299+B*587+C*114)/1e3<128?S.classList.add("dark-theme"):S.classList.remove("dark-theme")}const O=document.querySelector('meta[name="viewport"]');O&&O.setAttribute("content","width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover")}h.expand(),h.MainButton&&h.MainButton.hide(),h.setHeaderColor&&h.themeParams.bg_color&&h.setHeaderColor(h.themeParams.bg_color)}catch(_){c(S=>({...S,error:`Ошибка инициализации: ${_ instanceof Error?_.message:"Неизвестная ошибка"}`}))}},[]),{...i,showAlert:h=>{var y,R;(R=(y=window.Telegram)==null?void 0:y.WebApp)!=null&&R.showAlert?window.Telegram.WebApp.showAlert(h):alert(h)},showConfirm:h=>new Promise(y=>{var R,_;(_=(R=window.Telegram)==null?void 0:R.WebApp)!=null&&_.showConfirm?window.Telegram.WebApp.showConfirm(h,y):y(confirm(h))}),hapticFeedback:{impact:(h="medium")=>{var y,R,_;(_=(R=(y=window.Telegram)==null?void 0:y.WebApp)==null?void 0:R.HapticFeedback)==null||_.impactOccurred(h)},notification:h=>{var y,R,_;(_=(R=(y=window.Telegram)==null?void 0:y.WebApp)==null?void 0:R.HapticFeedback)==null||_.notificationOccurred(h)}}}}const av=({onUploadSuccess:i})=>{const[c,r]=Qt.useState(!1),[f,d]=Qt.useState(!1),h=Qt.useRef(null),{showAlert:y,hapticFeedback:R}=Ch(),_=C=>{const ut=["audio/","video/"];return C.size>314572800?"Размер файла не должен превышать 300 МБ":ut.some(F=>C.type.startsWith(F))?null:"Поддерживаются только аудио и видео файлы"},S=async C=>{var ut,F,W,dt;const q=_(C);if(q){y(q),R.notification("error");return}d(!0),R.impact("light");try{await Hn.uploadFile(C),y("Файл успешно загружен и поставлен в обработку!"),R.notification("success"),i()}catch(G){console.error("Upload error:",G);let rt="Не удалось загрузить файл";((ut=G.response)==null?void 0:ut.status)===413?rt="Файл слишком большой (максимум 300 МБ)":((F=G.response)==null?void 0:F.status)===429?rt="Превышена квота хранилища (1 ГБ на пользователя)":((W=G.response)==null?void 0:W.status)===400?rt="Неподдерживаемый формат файла":((dt=G.response)==null?void 0:dt.status)===401&&(rt="Ошибка авторизации. Перезапустите приложение."),y(rt),R.notification("error")}finally{d(!1)}},O=C=>{C.preventDefault(),r(!0)},H=C=>{C.preventDefault(),r(!1)},L=C=>{C.preventDefault(),r(!1);const q=Array.from(C.dataTransfer.files);q.length>0&&S(q[0])},Z=C=>{const q=Array.from(C.target.files||[]);q.length>0&&S(q[0]),C.target.value=""},B=()=>{var C;(C=h.current)==null||C.click()};return st.jsxs("div",{className:"flex items-center justify-center min-h-screen",children:[st.jsx("input",{ref:h,type:"file",accept:"audio/*,video/*",onChange:Z,className:"hidden"}),st.jsx("div",{className:`
          ${c?"scale-110":""} 
          ${f?"pointer-events-none":"cursor-pointer"}
          transition-transform duration-200
        `,onDragOver:O,onDragLeave:H,onDrop:L,onClick:B,children:st.jsxs("div",{className:`upload-orb ${f?"uploading":""}`,children:[st.jsx("div",{className:"upload-orb-fill"}),st.jsx("div",{className:"upload-orb-content",children:!f&&st.jsx(Ry,{className:"h-8 w-8 text-white"})})]})})]})};function nv(){const{isReady:i,initData:c,error:r}=Ch(),[f,d]=Qt.useState(null),[h,y]=Qt.useState(!1),[R,_]=Qt.useState(0),[S,O]=Qt.useState(!0),[H,L]=Qt.useState([]);Qt.useEffect(()=>{c&&(Hn.setInitData(c),B())},[c]);const Z=F=>{L(W=>[...W.slice(-4),F])},B=async()=>{var F,W,dt;try{Z("🔍 Loading user status...");let G;try{G=await Hn.getUserStatus()}catch(Tt){if(((F=Tt.response)==null?void 0:F.status)===404){Z("⚠️ /user-status not found, using fallback");try{Z("📞 Calling /users/me...");const yt=await Hn.getUserInfo();Z(`✅ Got user: ${yt.username}`),Z("📞 Calling /history...");const Bt=await Hn.getHistory();Z(`✅ Got ${Bt.length} files`),G={is_first_time:Bt.length===0,has_files:Bt.length>0,files_count:Bt.length,user:{id:yt.id,telegram_id:yt.telegram_id,username:yt.username,first_name:yt.username}}}catch(yt){throw Z(`❌ Fallback error: ${((W=yt.response)==null?void 0:W.status)||"unknown"}`),Z(`📝 Error details: ${yt.message}`),yt}}else throw Tt}Z(`📊 User: ${((dt=G.user)==null?void 0:dt.username)||"Unknown"}`),Z(`📁 Files: ${G.files_count}, First: ${G.is_first_time}`),d(G);const rt=G.is_first_time&&!G.has_files;Z(`🤔 Should show welcome: ${rt}`),rt?(Z("👋 Showing welcome!"),y(!0),C()):Z("⏭️ Skipping welcome")}catch(G){Z(`❌ Error: ${G.message||"Unknown"}`)}finally{O(!1)}},C=()=>{console.log("🎬 Starting welcome sequence..."),_(1),console.log("📝 Step 1: Welcome message"),setTimeout(()=>{console.log("📝 Step 2: Instructions message"),_(2),setTimeout(()=>{console.log("✅ Welcome sequence complete"),y(!1)},7e3)},7e3)},q=()=>{y(!1),console.log("File uploaded successfully")};if(r)return st.jsx("div",{className:"min-h-screen bg-tg-bg flex items-center justify-center p-4",children:st.jsxs("div",{className:"tg-card max-w-md w-full text-center animate-bounce-in",children:[st.jsx(Ay,{className:"h-16 w-16 text-error-500 mx-auto mb-4"}),st.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Ошибка инициализации"}),st.jsx("p",{className:"text-hint mb-4",children:r}),st.jsx("p",{className:"text-sm text-subtitle",children:"Откройте приложение в Telegram"})]})});if(!i||!c||S)return st.jsx("div",{className:"min-h-screen bg-tg-bg flex items-center justify-center",children:st.jsxs("div",{className:"flex flex-col items-center animate-fade-in",children:[st.jsx("div",{className:"loading-spinner w-12 h-12 text-tg-link mb-4"}),st.jsx("p",{className:"text-hint",children:"Инициализация..."})]})});const ut=()=>{var W,dt;if(console.log("🎨 Rendering welcome message:",{showWelcome:h,welcomeStep:R}),!h)return null;const F=((W=f==null?void 0:f.user)==null?void 0:W.first_name)||((dt=f==null?void 0:f.user)==null?void 0:dt.username)||"Друг";return console.log("👤 Username for welcome:",F),st.jsx("div",{className:"absolute inset-0 bg-tg-bg flex items-center justify-center z-10",children:st.jsxs("div",{className:"text-center px-8 animate-fade-in",children:[R===1&&st.jsxs("div",{className:"animate-fade-in",children:[st.jsxs("h1",{className:"text-2xl font-bold text-tg-text mb-2",children:["Привет, ",F,"! 👋"]}),st.jsx("p",{className:"text-xl text-tg-text",children:"Я - Конспектор."})]}),R===2&&st.jsx("div",{className:"animate-fade-in",children:st.jsx("p",{className:"text-lg text-tg-text leading-relaxed",children:"Пришли мне аудио, я его прослушаю и отправлю тебе конспект этой аудиозаписи! 🎧📝"})})]})})};return st.jsxs("div",{className:"min-h-screen bg-tg-bg relative",children:[H.length>0&&st.jsxs("div",{className:"fixed top-4 left-4 right-4 bg-black bg-opacity-80 text-white text-xs p-3 rounded-lg z-50 font-mono",children:[st.jsx("div",{className:"font-bold mb-2",children:"Debug Info:"}),H.map((F,W)=>st.jsx("div",{className:"mb-1",children:F},W)),st.jsx("button",{onClick:()=>L([]),className:"mt-2 px-2 py-1 bg-red-600 text-white rounded text-xs",children:"Clear"})]}),ut(),st.jsx(av,{onUploadSuccess:q})]})}vy.createRoot(document.getElementById("root")).render(st.jsx(Qt.StrictMode,{children:st.jsx(nv,{})}));
