#!/usr/bin/env python3
"""
Тестовый скрипт для проверки Whisper API
"""
import asyncio
import aiohttp
import aiofiles
import os
import sys
from pathlib import Path

# Добавляем путь к модулям backend
sys.path.append(str(Path(__file__).parent / "src"))

from processing import transcribe_audio

async def test_direct_whisper(audio_file_path: str):
    """Тестирование прямого вызова Whisper"""
    print(f"🎵 Тестируем прямой вызов Whisper с файлом: {audio_file_path}")
    
    if not os.path.exists(audio_file_path):
        print(f"❌ Файл не найден: {audio_file_path}")
        return
    
    try:
        # Прямой вызов функции транскрипции
        transcription = await transcribe_audio(audio_file_path)
        print(f"✅ Транскрипция успешно выполнена!")
        print(f"📝 Длина результата: {len(transcription)} символов")
        print(f"📄 Первые 200 символов:")
        print("-" * 50)
        print(transcription[:200] + "..." if len(transcription) > 200 else transcription)
        print("-" * 50)
        
        return transcription
        
    except Exception as e:
        print(f"❌ Ошибка при транскрипции: {e}")
        return None

async def test_api_endpoint(audio_file_path: str, server_url: str = "http://localhost:8000"):
    """Тестирование API endpoint"""
    print(f"🌐 Тестируем API endpoint: {server_url}/api/v1/transcribe")
    
    if not os.path.exists(audio_file_path):
        print(f"❌ Файл не найден: {audio_file_path}")
        return
    
    try:
        async with aiohttp.ClientSession() as session:
            # Читаем файл
            async with aiofiles.open(audio_file_path, 'rb') as f:
                file_content = await f.read()
            
            # Подготавливаем данные для отправки
            data = aiohttp.FormData()
            data.add_field('file', 
                          file_content, 
                          filename=os.path.basename(audio_file_path),
                          content_type='audio/m4a')
            
            # Отправляем запрос (без авторизации для тестирования)
            headers = {
                'Authorization': 'Bearer debug_mode_fake_init_data'
            }
            
            async with session.post(f"{server_url}/api/v1/transcribe", 
                                  data=data, 
                                  headers=headers) as response:
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ API запрос успешен!")
                    print(f"📄 Результат: {result}")
                    return result
                else:
                    error_text = await response.text()
                    print(f"❌ API запрос неудачен. Статус: {response.status}")
                    print(f"📄 Ошибка: {error_text}")
                    return None
                    
    except Exception as e:
        print(f"❌ Ошибка при обращении к API: {e}")
        return None

async def main():
    """Основная функция тестирования"""
    audio_file_path = "/home/<USER>/PROJ/diarizator/333.m4a"
    
    print("🚀 Запуск тестирования Whisper API")
    print("=" * 60)
    
    # Проверяем существование файла
    if not os.path.exists(audio_file_path):
        print(f"❌ Аудиофайл не найден: {audio_file_path}")
        return
    
    file_size = os.path.getsize(audio_file_path)
    print(f"📁 Файл: {audio_file_path}")
    print(f"📊 Размер: {file_size / (1024*1024):.2f} МБ")
    print()
    
    # Тест 1: Прямой вызов Whisper
    print("🧪 ТЕСТ 1: Прямой вызов функции транскрипции")
    print("-" * 40)
    transcription = await test_direct_whisper(audio_file_path)
    print()
    
    # Тест 2: API endpoint (если сервер запущен)
    print("🧪 ТЕСТ 2: Тестирование API endpoint")
    print("-" * 40)
    print("⚠️  Для этого теста нужно запустить сервер в отдельном терминале:")
    print("   cd backend && source venv/bin/activate && python -m uvicorn src.main:app --reload")
    print()
    
    # Проверяем доступность сервера
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8000/health") as response:
                if response.status == 200:
                    print("✅ Сервер доступен, тестируем API...")
                    await test_api_endpoint(audio_file_path)
                else:
                    print("❌ Сервер недоступен")
    except Exception:
        print("❌ Сервер не запущен или недоступен")
    
    print()
    print("🏁 Тестирование завершено!")

if __name__ == "__main__":
    asyncio.run(main())
