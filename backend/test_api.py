#!/usr/bin/env python3
"""
Тестирование Whisper API
"""
import asyncio
import aiohttp
import aiofiles
import os
import json

async def test_health():
    """Проверка здоровья сервера"""
    print("🏥 Проверяем здоровье сервера...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:8001/health") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Сервер работает: {result}")
                    return True
                else:
                    print(f"❌ Сервер недоступен: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Ошибка подключения: {e}")
            return False

async def test_debug_endpoint():
    """Проверка отладочного endpoint"""
    print("🔧 Проверяем отладочный endpoint...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:8001/api/v1/debug/test") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Debug endpoint работает: {result}")
                    return True
                else:
                    print(f"❌ Debug endpoint недоступен: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Ошибка debug endpoint: {e}")
            return False

async def test_transcribe_endpoint(audio_file_path: str):
    """Тестирование endpoint транскрипции"""
    print(f"🎵 Тестируем транскрипцию файла: {audio_file_path}")
    
    if not os.path.exists(audio_file_path):
        print(f"❌ Файл не найден: {audio_file_path}")
        return False
    
    file_size = os.path.getsize(audio_file_path)
    print(f"📊 Размер файла: {file_size / (1024*1024):.2f} МБ")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Читаем файл
            async with aiofiles.open(audio_file_path, 'rb') as f:
                file_content = await f.read()
            
            # Подготавливаем данные для отправки
            data = aiohttp.FormData()
            data.add_field('file', 
                          file_content, 
                          filename=os.path.basename(audio_file_path),
                          content_type='audio/m4a')
            
            # Отправляем запрос с фейковой авторизацией для тестирования
            headers = {
                'Authorization': 'Bearer debug_mode_fake_init_data'
            }
            
            print("📤 Отправляем файл на сервер...")
            async with session.post("http://localhost:8001/api/v1/transcribe", 
                                  data=data, 
                                  headers=headers,
                                  timeout=aiohttp.ClientTimeout(total=300)) as response:
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Транскрипция успешна!")
                    print(f"📄 Результат: {result}")
                    
                    # Проверяем созданный файл транскрипции
                    transcription_file = result.get('transcription_filename')
                    if transcription_file:
                        transcription_path = f"storage/transcriptions/{transcription_file}"
                        if os.path.exists(transcription_path):
                            with open(transcription_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            print(f"📝 Содержимое транскрипции ({len(content)} символов):")
                            print("-" * 50)
                            print(content[:500] + "..." if len(content) > 500 else content)
                            print("-" * 50)
                        else:
                            print(f"⚠️ Файл транскрипции не найден: {transcription_path}")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Ошибка транскрипции. Статус: {response.status}")
                    print(f"📄 Детали ошибки: {error_text}")
                    return False
                    
        except asyncio.TimeoutError:
            print("❌ Таймаут запроса (превышено 5 минут)")
            return False
        except Exception as e:
            print(f"❌ Ошибка при транскрипции: {e}")
            return False

async def test_upload_endpoint(audio_file_path: str):
    """Тестирование полного endpoint загрузки с обработкой"""
    print(f"📤 Тестируем полную загрузку и обработку: {audio_file_path}")
    
    if not os.path.exists(audio_file_path):
        print(f"❌ Файл не найден: {audio_file_path}")
        return False
    
    async with aiohttp.ClientSession() as session:
        try:
            # Читаем файл
            async with aiofiles.open(audio_file_path, 'rb') as f:
                file_content = await f.read()
            
            # Подготавливаем данные для отправки
            data = aiohttp.FormData()
            data.add_field('file', 
                          file_content, 
                          filename=os.path.basename(audio_file_path),
                          content_type='audio/m4a')
            
            # Отправляем запрос с фейковой авторизацией для тестирования
            headers = {
                'Authorization': 'Bearer debug_mode_fake_init_data'
            }
            
            print("📤 Отправляем файл на полную обработку...")
            async with session.post("http://localhost:8001/api/v1/upload", 
                                  data=data, 
                                  headers=headers,
                                  timeout=aiohttp.ClientTimeout(total=600)) as response:
                
                if response.status == 202:  # Accepted
                    result = await response.json()
                    print(f"✅ Файл принят в обработку!")
                    print(f"📄 Результат: {result}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Ошибка загрузки. Статус: {response.status}")
                    print(f"📄 Детали ошибки: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Ошибка при загрузке: {e}")
            return False

async def main():
    """Основная функция тестирования"""
    audio_file_path = "/home/<USER>/PROJ/diarizator/333.m4a"
    
    print("🚀 Запуск полного тестирования Whisper API")
    print("=" * 60)
    
    # Тест 1: Проверка здоровья сервера
    if not await test_health():
        print("❌ Сервер недоступен, завершаем тестирование")
        return
    
    print()
    
    # Тест 2: Проверка отладочного endpoint
    if not await test_debug_endpoint():
        print("⚠️ Debug endpoint недоступен, но продолжаем")
    
    print()
    
    # Тест 3: Тестирование транскрипции
    print("🧪 ТЕСТ: Endpoint только транскрипции")
    print("-" * 40)
    await test_transcribe_endpoint(audio_file_path)
    
    print()
    
    # Тест 4: Тестирование полной загрузки
    print("🧪 ТЕСТ: Полная загрузка и обработка")
    print("-" * 40)
    await test_upload_endpoint(audio_file_path)
    
    print()
    print("🏁 Тестирование завершено!")

if __name__ == "__main__":
    asyncio.run(main())
