#!/usr/bin/env python3
"""
Простой тест Whisper
"""
import asyncio
import os
from faster_whisper import WhisperModel

async def main():
    audio_file = "../333.m4a"
    
    print(f"🎵 Тестируем Whisper с файлом: {audio_file}")
    
    if not os.path.exists(audio_file):
        print(f"❌ Файл не найден: {audio_file}")
        return
    
    file_size = os.path.getsize(audio_file)
    print(f"📊 Размер файла: {file_size / (1024*1024):.2f} МБ")
    
    try:
        print("🤖 Запускаем транскрипцию...")
        transcription = await transcribe_audio(audio_file)
        
        print(f"✅ Готово! Длина: {len(transcription)} символов")
        print("📝 Результат:")
        print("-" * 50)
        print(transcription[:500] + "..." if len(transcription) > 500 else transcription)
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")

if __name__ == "__main__":
    asyncio.run(main())
