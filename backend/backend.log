INFO:     Will watch for changes in these directories: ['/home/<USER>/PROJ/diarizator/backend']
INFO:     Uvicorn running on http://127.0.0.1:8001 (Press CTRL+C to quit)
INFO:     Started reloader process [270136] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 237
    if progress_callback:
IndentationError: unexpected indent
WARNING:  WatchFiles detected changes in 'src/processing.py'. Reloading...
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 247
    try:
IndentationError: unexpected indent
WARNING:  WatchFiles detected changes in 'src/processing.py'. Reloading...
Process SpawnProcess-3:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 8, in <module>
    import google.generativeai as genai
ModuleNotFoundError: No module named 'google'
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/urllib3/util/url.py', 'venv/lib/python3.13/site-packages/urllib3/filepost.py', 'venv/lib/python3.13/site-packages/pyparsing/__init__.py', 'venv/lib/python3.13/site-packages/urllib3/contrib/emscripten/request.py', 'venv/lib/python3.13/site-packages/tqdm/cli.py', 'venv/lib/python3.13/site-packages/urllib3/contrib/socks.py', 'venv/lib/python3.13/site-packages/tqdm/_tqdm_notebook.py', 'venv/lib/python3.13/site-packages/tqdm/notebook.py', 'venv/lib/python3.13/site-packages/tqdm/_tqdm_gui.py', 'venv/lib/python3.13/site-packages/tqdm/__main__.py', 'venv/lib/python3.13/site-packages/tqdm/contrib/itertools.py', 'venv/lib/python3.13/site-packages/pyparsing/testing.py', 'venv/lib/python3.13/site-packages/tqdm/contrib/logging.py', 'venv/lib/python3.13/site-packages/urllib3/util/util.py', 'venv/lib/python3.13/site-packages/urllib3/exceptions.py', 'venv/lib/python3.13/site-packages/urllib3/util/response.py', 'venv/lib/python3.13/site-packages/urllib3/_request_methods.py', 'venv/lib/python3.13/site-packages/tqdm/std.py', 'venv/lib/python3.13/site-packages/urllib3/util/request.py', 'venv/lib/python3.13/site-packages/tqdm/asyncio.py', 'venv/lib/python3.13/site-packages/urllib3/response.py', 'venv/lib/python3.13/site-packages/tqdm/contrib/__init__.py', 'venv/lib/python3.13/site-packages/tqdm/version.py', 'venv/lib/python3.13/site-packages/tqdm/contrib/concurrent.py', 'venv/lib/python3.13/site-packages/urllib3/__init__.py', 'venv/lib/python3.13/site-packages/tqdm/contrib/slack.py', 'venv/lib/python3.13/site-packages/urllib3/_base_connection.py', 'venv/lib/python3.13/site-packages/urllib3/contrib/emscripten/__init__.py', 'venv/lib/python3.13/site-packages/tqdm/gui.py', 'venv/lib/python3.13/site-packages/pyparsing/actions.py', 'venv/lib/python3.13/site-packages/urllib3/http2/probe.py', 'venv/lib/python3.13/site-packages/urllib3/connectionpool.py', 'venv/lib/python3.13/site-packages/urllib3/fields.py', 'venv/lib/python3.13/site-packages/tqdm/dask.py', 'venv/lib/python3.13/site-packages/tqdm/utils.py', 'venv/lib/python3.13/site-packages/pyparsing/helpers.py', 'venv/lib/python3.13/site-packages/pyparsing/util.py', 'venv/lib/python3.13/site-packages/uritemplate/template.py', 'venv/lib/python3.13/site-packages/urllib3/http2/connection.py', 'venv/lib/python3.13/site-packages/urllib3/contrib/emscripten/response.py', 'venv/lib/python3.13/site-packages/tqdm/contrib/discord.py', 'venv/lib/python3.13/site-packages/uritemplate/api.py', 'venv/lib/python3.13/site-packages/pyparsing/common.py', 'venv/lib/python3.13/site-packages/tqdm/contrib/bells.py', 'venv/lib/python3.13/site-packages/tqdm/_main.py', 'venv/lib/python3.13/site-packages/tqdm/_monitor.py', 'venv/lib/python3.13/site-packages/tqdm/keras.py', 'venv/lib/python3.13/site-packages/urllib3/util/ssl_match_hostname.py', 'venv/lib/python3.13/site-packages/urllib3/connection.py', 'venv/lib/python3.13/site-packages/tqdm/_dist_ver.py', 'venv/lib/python3.13/site-packages/urllib3/util/retry.py', 'venv/lib/python3.13/site-packages/pyparsing/exceptions.py', 'venv/lib/python3.13/site-packages/pyparsing/results.py', 'venv/lib/python3.13/site-packages/urllib3/util/wait.py', 'venv/lib/python3.13/site-packages/tqdm/_tqdm.py', 'venv/lib/python3.13/site-packages/tqdm/tk.py', 'venv/lib/python3.13/site-packages/tqdm/contrib/telegram.py', 'venv/lib/python3.13/site-packages/urllib3/util/ssltransport.py', 'venv/lib/python3.13/site-packages/urllib3/contrib/pyopenssl.py', 'venv/lib/python3.13/site-packages/urllib3/util/timeout.py', 'venv/lib/python3.13/site-packages/tqdm/contrib/utils_worker.py', 'venv/lib/python3.13/site-packages/tqdm/_utils.py', 'venv/lib/python3.13/site-packages/urllib3/_collections.py', 'venv/lib/python3.13/site-packages/urllib3/util/ssl_.py', 'venv/lib/python3.13/site-packages/urllib3/contrib/emscripten/connection.py', 'venv/lib/python3.13/site-packages/pyparsing/unicode.py', 'venv/lib/python3.13/site-packages/uritemplate/orderedset.py', 'venv/lib/python3.13/site-packages/pyparsing/diagram/__init__.py', 'venv/lib/python3.13/site-packages/urllib3/poolmanager.py', 'venv/lib/python3.13/site-packages/tqdm/__init__.py', 'venv/lib/python3.13/site-packages/urllib3/_version.py', 'venv/lib/python3.13/site-packages/tqdm/_tqdm_pandas.py', 'venv/lib/python3.13/site-packages/pyparsing/tools/cvt_pyparsing_pep8_names.py', 'venv/lib/python3.13/site-packages/tqdm/autonotebook.py', 'venv/lib/python3.13/site-packages/urllib3/http2/__init__.py', 'venv/lib/python3.13/site-packages/pyparsing/core.py', 'venv/lib/python3.13/site-packages/tqdm/rich.py', 'venv/lib/python3.13/site-packages/tqdm/auto.py', 'venv/lib/python3.13/site-packages/urllib3/contrib/emscripten/fetch.py', 'venv/lib/python3.13/site-packages/uritemplate/variable.py'. Reloading...
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/pyasn1/codec/streaming.py', 'venv/lib/python3.13/site-packages/google/protobuf/message.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/__init__.py', 'venv/lib/python3.13/site-packages/google/protobuf/proto.py', 'venv/lib/python3.13/site-packages/google/protobuf/text_encoding.py', 'venv/lib/python3.13/site-packages/google/protobuf/pyext/cpp_message.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/_parameterized.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/ber/encoder.py', 'venv/lib/python3.13/site-packages/pyasn1/type/namedval.py', 'venv/lib/python3.13/site-packages/pyasn1/type/univ.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/python_message.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/der/decoder.py', 'venv/lib/python3.13/site-packages/pyasn1/type/tag.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/extension_dict.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/enum_type_wrapper.py', 'venv/lib/python3.13/site-packages/google/protobuf/descriptor_database.py', 'venv/lib/python3.13/site-packages/google/protobuf/field_mask_pb2.py', 'venv/lib/python3.13/site-packages/google/protobuf/timestamp_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1/type/opentype.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/builder.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/cer/encoder.py', 'venv/lib/python3.13/site-packages/pyasn1/error.py', 'venv/lib/python3.13/site-packages/google/protobuf/api_pb2.py', 'venv/lib/python3.13/site-packages/google/protobuf/message_factory.py', 'venv/lib/python3.13/site-packages/google/protobuf/symbol_database.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/native/__init__.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/decoder.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/ber/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1/type/base.py', 'venv/lib/python3.13/site-packages/google/protobuf/struct_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1/type/error.py', 'venv/lib/python3.13/site-packages/pyasn1/type/namedtype.py', 'venv/lib/python3.13/site-packages/pyasn1/debug.py', 'venv/lib/python3.13/site-packages/pyasn1/type/useful.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/cer/decoder.py', 'venv/lib/python3.13/site-packages/pyasn1/compat/__init__.py', 'venv/lib/python3.13/site-packages/google/protobuf/text_format.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/testing_refleaks.py', 'venv/lib/python3.13/site-packages/google/protobuf/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/ber/eoo.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/native/decoder.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/field_mask.py', 'venv/lib/python3.13/site-packages/pyasn1/type/char.py', 'venv/lib/python3.13/site-packages/google/protobuf/descriptor_pool.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/ber/decoder.py', 'venv/lib/python3.13/site-packages/google/protobuf/descriptor_pb2.py', 'venv/lib/python3.13/site-packages/google/protobuf/reflection.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/api_implementation.py', 'venv/lib/python3.13/site-packages/google/protobuf/proto_builder.py', 'venv/lib/python3.13/site-packages/google/protobuf/descriptor.py', 'venv/lib/python3.13/site-packages/google/protobuf/any_pb2.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/encoder.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/message_listener.py', 'venv/lib/python3.13/site-packages/google/protobuf/duration.py', 'venv/lib/python3.13/site-packages/google/protobuf/timestamp.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/type_checkers.py', 'venv/lib/python3.13/site-packages/google/protobuf/unknown_fields.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/native/encoder.py', 'venv/lib/python3.13/site-packages/google/protobuf/source_context_pb2.py', 'venv/lib/python3.13/site-packages/google/protobuf/duration_pb2.py', 'venv/lib/python3.13/site-packages/google/protobuf/service_reflection.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/wire_format.py', 'venv/lib/python3.13/site-packages/pyasn1/type/tagmap.py', 'venv/lib/python3.13/site-packages/google/protobuf/json_format.py', 'venv/lib/python3.13/site-packages/google/protobuf/empty_pb2.py', 'venv/lib/python3.13/site-packages/google/protobuf/proto_json.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/python_edition_defaults.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/der/__init__.py', 'venv/lib/python3.13/site-packages/google/protobuf/pyext/__init__.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/well_known_types.py', 'venv/lib/python3.13/site-packages/pyasn1/__init__.py', 'venv/lib/python3.13/site-packages/google/protobuf/type_pb2.py', 'venv/lib/python3.13/site-packages/google/protobuf/wrappers_pb2.py', 'venv/lib/python3.13/site-packages/google/protobuf/runtime_version.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/der/encoder.py', 'venv/lib/python3.13/site-packages/google/protobuf/service.py', 'venv/lib/python3.13/site-packages/google/protobuf/compiler/plugin_pb2.py', 'venv/lib/python3.13/site-packages/google/protobuf/any.py', 'venv/lib/python3.13/site-packages/pyasn1/type/constraint.py', 'venv/lib/python3.13/site-packages/pyasn1/compat/integer.py', 'venv/lib/python3.13/site-packages/pyasn1/codec/cer/__init__.py', 'venv/lib/python3.13/site-packages/google/protobuf/internal/containers.py'. Reloading...
Process SpawnProcess-4:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 8, in <module>
    import google.generativeai as genai
ModuleNotFoundError: No module named 'google.generativeai'
Process SpawnProcess-5:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 8, in <module>
    import google.generativeai as genai
ModuleNotFoundError: No module named 'google.generativeai'
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/requests/status_codes.py', 'venv/lib/python3.13/site-packages/google/auth/iam.py', 'venv/lib/python3.13/site-packages/cachetools/func.py', 'venv/lib/python3.13/site-packages/httplib2/socks.py', 'venv/lib/python3.13/site-packages/httplib2/__init__.py', 'venv/lib/python3.13/site-packages/rsa/prime.py', 'venv/lib/python3.13/site-packages/google/auth/_refresh_worker.py', 'venv/lib/python3.13/site-packages/grpc/_typing.py', 'venv/lib/python3.13/site-packages/google/api/context_pb2.py', 'venv/lib/python3.13/site-packages/google/auth/_default.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/generative_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/auth/crypt/es256.py', 'venv/lib/python3.13/site-packages/google/oauth2/service_account.py', 'venv/lib/python3.13/site-packages/google/auth/transport/urllib3.py', 'venv/lib/python3.13/site-packages/google/auth/_default_async.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5752.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8209.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8649.py', 'venv/lib/python3.13/site-packages/google/type/phone_number_pb2.py', 'venv/lib/python3.13/site-packages/requests/help.py', 'venv/lib/python3.13/site-packages/grpc/framework/interfaces/base/utilities.py', 'venv/lib/python3.13/site-packages/google/api_core/general_helpers.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3447.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/discuss_service/__init__.py', 'venv/lib/python3.13/site-packages/grpc/_plugin_wrapping.py', 'venv/lib/python3.13/site-packages/google/auth/external_account_authorized_user.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4043.py', 'venv/lib/python3.13/site-packages/proto/marshal/collections/__init__.py', 'venv/lib/python3.13/site-packages/google/auth/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4108.py', 'venv/lib/python3.13/site-packages/rsa/core.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8017.py', 'venv/lib/python3.13/site-packages/grpc/beta/interfaces.py', 'venv/lib/python3.13/site-packages/proto/marshal/rules/stringy_numbers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/model_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/grpc/framework/interfaces/base/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3274.py', 'venv/lib/python3.13/site-packages/google_auth_httplib2.py', 'venv/lib/python3.13/site-packages/proto/marshal/rules/enums.py', 'venv/lib/python3.13/site-packages/rsa/pkcs1_v2.py', 'venv/lib/python3.13/site-packages/requests/packages.py', 'venv/lib/python3.13/site-packages/google/api/launch_stage_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6482.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8494.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/generative_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/auth/crypt/_python_rsa.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/prediction_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8619.py', 'venv/lib/python3.13/site-packages/requests/certs.py', 'venv/lib/python3.13/site-packages/google/api/http_pb2.py', 'venv/lib/python3.13/site-packages/googleapiclient/http.py', 'venv/lib/python3.13/site-packages/proto/__init__.py', 'venv/lib/python3.13/site-packages/google/auth/api_key.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2631.py', 'venv/lib/python3.13/site-packages/google/api/routing_pb2.py', 'venv/lib/python3.13/site-packages/google/oauth2/_service_account_async.py', 'venv/lib/python3.13/site-packages/grpc/_auth.py', 'venv/lib/python3.13/site-packages/proto/marshal/rules/dates.py', 'venv/lib/python3.13/site-packages/grpc/_runtime_protos.py', 'venv/lib/python3.13/site-packages/google/api_core/version_header.py', 'venv/lib/python3.13/site-packages/google/oauth2/challenges.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8410.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/model_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/permission_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/cache_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/permission_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/operations_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/generative_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5915.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc1905.py', 'venv/lib/python3.13/site-packages/google/api/config_change_pb2.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/cache_service/client.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4010.py', 'venv/lib/python3.13/site-packages/google/auth/_oauth2client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/discuss_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/requests/__version__.py', 'venv/lib/python3.13/site-packages/grpc/framework/common/__init__.py', 'venv/lib/python3.13/site-packages/google/gapic/metadata/gapic_metadata_pb2.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/model_service/client.py', 'venv/lib/python3.13/site-packages/google/auth/compute_engine/_metadata.py', 'venv/lib/python3.13/site-packages/grpc/beta/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/generative_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/requests/api.py', 'venv/lib/python3.13/site-packages/google/auth/transport/_aiohttp_requests.py', 'venv/lib/python3.13/site-packages/grpc_status/_common.py', 'venv/lib/python3.13/site-packages/requests/exceptions.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7229.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/generative_service/transports/rest.py', 'venv/lib/python3.13/site-packages/grpc/aio/_typing.py', 'venv/lib/python3.13/site-packages/google/oauth2/id_token.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc1902.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/abstract_operations_client.py', 'venv/lib/python3.13/site-packages/grpc/aio/_utils.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8769.py', 'venv/lib/python3.13/site-packages/google/api/monitoring_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3537.py', 'venv/lib/python3.13/site-packages/grpc/framework/interfaces/face/utilities.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/operations_async_client.py', 'venv/lib/python3.13/site-packages/requests/models.py', 'venv/lib/python3.13/site-packages/grpc/aio/_base_call.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3114.py', 'venv/lib/python3.13/site-packages/google/auth/app_engine.py', 'venv/lib/python3.13/site-packages/google/api/distribution_pb2.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/retriever_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/auth/aio/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8103.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/types/__init__.py', 'venv/lib/python3.13/site-packages/google/api_core/gapic_v1/client_info.py', 'venv/lib/python3.13/site-packages/google/type/timeofday_pb2.py', 'venv/lib/python3.13/site-packages/google/api/source_info_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2459.py', 'venv/lib/python3.13/site-packages/grpc/_observability.py', 'venv/lib/python3.13/site-packages/apiclient/__init__.py', 'venv/lib/python3.13/site-packages/charset_normalizer/cd.py', 'venv/lib/python3.13/site-packages/rsa/transform.py', 'venv/lib/python3.13/site-packages/httplib2/certs.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5480.py', 'venv/lib/python3.13/site-packages/google/api_core/retry/retry_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/generative_service/__init__.py', 'venv/lib/python3.13/site-packages/google/api_core/operation.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/generative_service/transports/base.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5639.py', 'venv/lib/python3.13/site-packages/proto/marshal/rules/message.py', 'venv/lib/python3.13/site-packages/google/api_core/gapic_v1/method_async.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/file_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/generative_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/retriever_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7030.py', 'venv/lib/python3.13/site-packages/proto/marshal/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8479.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8708.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2986.py', 'venv/lib/python3.13/site-packages/grpc/_cython/_cygrpc/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/file_service/transports/base.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5914.py', 'venv/lib/python3.13/site-packages/requests/_internal_utils.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8419.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/operations_rest_client_async.py', 'venv/lib/python3.13/site-packages/google/api/httpbody_pb2.py', 'venv/lib/python3.13/site-packages/google/auth/crypt/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/types/citation.py', 'venv/lib/python3.13/site-packages/googleapiclient/model.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5917.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5751.py', 'venv/lib/python3.13/site-packages/google/type/latlng_pb2.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/cache_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/api_core/future/__init__.py', 'venv/lib/python3.13/site-packages/google/oauth2/_reauth_async.py', 'venv/lib/python3.13/site-packages/google/api/billing_pb2.py', 'venv/lib/python3.13/site-packages/grpc/framework/common/style.py', 'venv/lib/python3.13/site-packages/proto/fields.py', 'venv/lib/python3.13/site-packages/grpc/experimental/aio/__init__.py', 'venv/lib/python3.13/site-packages/google/oauth2/_client.py', 'venv/lib/python3.13/site-packages/googleapiclient/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/__init__.py', 'venv/lib/python3.13/site-packages/google/api_core/future/_helpers.py', 'venv/lib/python3.13/site-packages/google/type/postal_address_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5934.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/permission_service/client.py', 'venv/lib/python3.13/site-packages/grpc_status/_async.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/generative_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/grpc/_cython/__init__.py', 'venv/lib/python3.13/site-packages/grpc/_compression.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc1901.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/prediction_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/types/generative_service.py', 'venv/lib/python3.13/site-packages/proto/message.py', 'venv/lib/python3.13/site-packages/grpc/beta/implementations.py', 'venv/lib/python3.13/site-packages/google/longrunning/operations_grpc.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5697.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5035.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8418.py', 'venv/lib/python3.13/site-packages/google/api_core/retry/retry_unary.py', 'venv/lib/python3.13/site-packages/google/api/log_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5275.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3852.py', 'venv/lib/python3.13/site-packages/google/api_core/rest_streaming_async.py', 'venv/lib/python3.13/site-packages/grpc/beta/_metadata.py', 'venv/lib/python3.13/site-packages/charset_normalizer/cli/__init__.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/api/label_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7894.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/retriever_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/api_core/gapic_v1/__init__.py', 'venv/lib/python3.13/site-packages/grpc/_utilities.py', 'venv/lib/python3.13/site-packages/googleapiclient/schema.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8398.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/retriever_service/pagers.py', 'venv/lib/python3.13/site-packages/google/auth/compute_engine/credentials.py', 'venv/lib/python3.13/site-packages/rsa/util.py', 'venv/lib/python3.13/site-packages/httplib2/iri2uri.py', 'venv/lib/python3.13/site-packages/proto/marshal/rules/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/model_service/client.py', 'venv/lib/python3.13/site-packages/google/type/month_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5280.py', 'venv/lib/python3.13/site-packages/google/auth/credentials.py', 'venv/lib/python3.13/site-packages/google/oauth2/webauthn_handler_factory.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/cache_service/transports/rest.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7191.py', 'venv/lib/python3.13/site-packages/google/oauth2/sts.py', 'venv/lib/python3.13/site-packages/googleapiclient/discovery.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/permission_service/transports/rest.py', 'venv/lib/python3.13/site-packages/requests/structures.py', 'venv/lib/python3.13/site-packages/google/auth/transport/_mtls_helper.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6210.py', 'venv/lib/python3.13/site-packages/googleapiclient/sample_tools.py', 'venv/lib/python3.13/site-packages/grpc_status/rpc_status.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6032.py', 'venv/lib/python3.13/site-packages/proto/_package_info.py', 'venv/lib/python3.13/site-packages/google/auth/impersonated_credentials.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3565.py', 'venv/lib/python3.13/site-packages/google/api_core/page_iterator_async.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/generative_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/file_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/permission_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/api/annotations_pb2.py', 'venv/lib/python3.13/site-packages/google/auth/transport/mtls.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8226.py', 'venv/lib/python3.13/site-packages/requests/hooks.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7508.py', 'venv/lib/python3.13/site-packages/google/auth/_service_account_info.py', 'venv/lib/python3.13/site-packages/google/auth/transport/_http_client.py', 'venv/lib/python3.13/site-packages/google/auth/_jwt_async.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2985.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2437.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8018.py', 'venv/lib/python3.13/site-packages/google/auth/metrics.py', 'venv/lib/python3.13/site-packages/google/api/endpoint_pb2.py', 'venv/lib/python3.13/site-packages/google/auth/identity_pool.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3779.py', 'venv/lib/python3.13/site-packages/google/api_core/path_template.py', 'venv/lib/python3.13/site-packages/google/longrunning/operations_proto.py', 'venv/lib/python3.13/site-packages/proto/marshal/compat.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3820.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7773.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7296.py', 'venv/lib/python3.13/site-packages/google/longrunning/operations_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4683.py', 'venv/lib/python3.13/site-packages/google/auth/_credentials_async.py', 'venv/lib/python3.13/site-packages/grpc/aio/_interceptor.py', 'venv/lib/python3.13/site-packages/google/api_core/retry/retry_streaming_async.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/generative_service/client.py', 'venv/lib/python3.13/site-packages/google/rpc/http_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6019.py', 'venv/lib/python3.13/site-packages/googleapiclient/version.py', 'venv/lib/python3.13/site-packages/google/api_core/client_info.py', 'venv/lib/python3.13/site-packages/google/rpc/context/attribute_context_pb2.py', 'venv/lib/python3.13/site-packages/grpc/aio/_metadata.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/__init__.py', 'venv/lib/python3.13/site-packages/google/api_core/gapic_v1/config_async.py', 'venv/lib/python3.13/site-packages/cachetools/_decorators.py', 'venv/lib/python3.13/site-packages/google/type/expr_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3770.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/pagers_async.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8520.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5208.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/types/model_service.py', 'venv/lib/python3.13/site-packages/googleapiclient/errors.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4476.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/file_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/api_core/gapic_v1/method.py', 'venv/lib/python3.13/site-packages/google/api_core/rest_streaming.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/file_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/auth/transport/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/file_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/prediction_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/api/resource_pb2.py', 'venv/lib/python3.13/site-packages/google/type/datetime_pb2.py', 'venv/lib/python3.13/site-packages/rsa/parallel.py', 'venv/lib/python3.13/site-packages/google/api_core/retry/retry_unary_async.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/abstract_operations_base_client.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2876.py', 'venv/lib/python3.13/site-packages/google/api_core/gapic_v1/routing_header.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3370.py', 'venv/lib/python3.13/site-packages/google/api/logging_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3279.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/permission_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/auth/environment_vars.py', 'venv/lib/python3.13/site-packages/google/oauth2/webauthn_handler.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/cache_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/proto/enums.py', 'venv/lib/python3.13/site-packages/proto/marshal/rules/struct.py', 'venv/lib/python3.13/site-packages/google/api/quota_pb2.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/model_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3560.py', 'venv/lib/python3.13/site-packages/google/auth/aio/_helpers.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8358.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3657.py', 'venv/lib/python3.13/site-packages/google/api_core/gapic_v1/config.py', 'venv/lib/python3.13/site-packages/google/auth/_credentials_base.py', 'venv/lib/python3.13/site-packages/google/auth/transport/grpc.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2314.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5958.py', 'venv/lib/python3.13/site-packages/grpc/_channel.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5916.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/model_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/permission_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/prediction_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/model_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/model_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/cache_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/auth/_cloud_sdk.py', 'venv/lib/python3.13/site-packages/google/rpc/context/audit_context_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6120.py', 'venv/lib/python3.13/site-packages/charset_normalizer/api.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7292.py', 'venv/lib/python3.13/site-packages/charset_normalizer/constant.py', 'venv/lib/python3.13/site-packages/grpc/beta/utilities.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3739.py', 'venv/lib/python3.13/site-packages/grpc/framework/foundation/future.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2511.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2560.py', 'venv/lib/python3.13/site-packages/google/api_core/datetime_helpers.py', 'venv/lib/python3.13/site-packages/google/api/error_reason_pb2.py', 'venv/lib/python3.13/site-packages/rsa/pem.py', 'venv/lib/python3.13/site-packages/google/oauth2/_credentials_async.py', 'venv/lib/python3.13/site-packages/rsa/pkcs1.py', 'venv/lib/python3.13/site-packages/google/api/consumer_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6211.py', 'venv/lib/python3.13/site-packages/google/type/color_pb2.py', 'venv/lib/python3.13/site-packages/google/auth/transport/_custom_tls_signer.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/prediction_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/cache_service/async_client.py', 'venv/lib/python3.13/site-packages/google/api/backend_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5753.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6664.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/model_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/oauth2/gdch_credentials.py', 'venv/lib/python3.13/site-packages/google/oauth2/webauthn_types.py', 'venv/lib/python3.13/site-packages/google/rpc/error_details_pb2.py', 'venv/lib/python3.13/site-packages/google/api/usage_pb2.py', 'venv/lib/python3.13/site-packages/google/auth/compute_engine/__init__.py', 'venv/lib/python3.13/site-packages/googleapiclient/channel.py', 'venv/lib/python3.13/site-packages/grpc/__init__.py', 'venv/lib/python3.13/site-packages/grpc/aio/_server.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4357.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/operations_client_config.py', 'venv/lib/python3.13/site-packages/requests/cookies.py', 'venv/lib/python3.13/site-packages/requests/adapters.py', 'venv/lib/python3.13/site-packages/grpc/framework/interfaces/base/base.py', 'venv/lib/python3.13/site-packages/grpc/aio/_call.py', 'venv/lib/python3.13/site-packages/grpc/framework/interfaces/face/face.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/retriever_service/client.py', 'venv/lib/python3.13/site-packages/google/auth/pluggable.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/types/model.py', 'venv/lib/python3.13/site-packages/google/rpc/status_pb2.py', 'venv/lib/python3.13/site-packages/google/api_core/extended_operation.py', 'venv/lib/python3.13/site-packages/google/auth/external_account.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6486.py', 'venv/lib/python3.13/site-packages/google/auth/_helpers.py', 'venv/lib/python3.13/site-packages/google/api/visibility_pb2.py', 'venv/lib/python3.13/site-packages/grpc/experimental/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/cache_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/grpc/framework/interfaces/face/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/permission_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/cache_service/pagers.py', 'venv/lib/python3.13/site-packages/googleapiclient/discovery_cache/file_cache.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/file_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/retriever_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6170.py', 'venv/lib/python3.13/site-packages/grpc/framework/interfaces/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/retriever_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/api_core/retry/__init__.py', 'venv/lib/python3.13/site-packages/google/api_core/retry_async.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5636.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4387.py', 'venv/lib/python3.13/site-packages/google/auth/transport/requests.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/discuss_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/types/content.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/retriever_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/discuss_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/generative_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/api_core/client_logging.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/discuss_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/type/money_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3414.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/model_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/generative_service/transports/rest.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6187.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6960.py', 'venv/lib/python3.13/site-packages/google/rpc/code_pb2.py', 'venv/lib/python3.13/site-packages/google/api_core/protobuf_helpers.py', 'venv/lib/python3.13/site-packages/charset_normalizer/version.py', 'venv/lib/python3.13/site-packages/proto/utils.py', 'venv/lib/python3.13/site-packages/googleapiclient/mimeparse.py', 'venv/lib/python3.13/site-packages/requests/auth.py', 'venv/lib/python3.13/site-packages/google/auth/downscoped.py', 'venv/lib/python3.13/site-packages/google/api_core/bidi.py', 'venv/lib/python3.13/site-packages/cachetools/keys.py', 'venv/lib/python3.13/site-packages/google/type/calendar_period_pb2.py', 'venv/lib/python3.13/site-packages/proto/marshal/collections/maps.py', 'venv/lib/python3.13/site-packages/google/type/quaternion_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5649.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/prediction_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/cache_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8696.py', 'venv/lib/python3.13/site-packages/proto/version.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/model_service/async_client.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/transports/base.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/pagers_base.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7585.py', 'venv/lib/python3.13/site-packages/grpc/aio/_base_channel.py', 'venv/lib/python3.13/site-packages/google/api_core/future/polling.py', 'venv/lib/python3.13/site-packages/google/api/monitored_resource_pb2.py', 'venv/lib/python3.13/site-packages/proto/datetime_helpers.py', 'venv/lib/python3.13/site-packages/google/auth/transport/_requests_base.py', 'venv/lib/python3.13/site-packages/google/auth/aio/credentials.py', 'venv/lib/python3.13/site-packages/google/api_core/timeout.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6402.py', 'venv/lib/python3.13/site-packages/cachetools/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2315.py', 'venv/lib/python3.13/site-packages/proto/primitives.py', 'venv/lib/python3.13/site-packages/google/api_core/future/async_future.py', 'venv/lib/python3.13/site-packages/google/logging/type/http_request_pb2.py', 'venv/lib/python3.13/site-packages/google/type/localized_text_pb2.py', 'venv/lib/python3.13/site-packages/google/api/service_pb2.py', 'venv/lib/python3.13/site-packages/proto/_file_info.py', 'venv/lib/python3.13/site-packages/grpc/_server.py', 'venv/lib/python3.13/site-packages/charset_normalizer/legacy.py', 'venv/lib/python3.13/site-packages/google/api_core/version.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4211.py', 'venv/lib/python3.13/site-packages/google/logging/type/log_severity_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6955.py', 'venv/lib/python3.13/site-packages/google/api_core/_rest_streaming_base.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8692.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5913.py', 'venv/lib/python3.13/site-packages/google/api/metric_pb2.py', 'venv/lib/python3.13/site-packages/google/type/decimal_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2634.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/model_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/permission_service/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3161.py', 'venv/lib/python3.13/site-packages/google/auth/crypt/rsa.py', 'venv/lib/python3.13/site-packages/grpc/beta/_client_adaptations.py', 'venv/lib/python3.13/site-packages/google/api/field_info_pb2.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/file_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/api/field_behavior_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5084.py', 'venv/lib/python3.13/site-packages/googleapiclient/_helpers.py', 'venv/lib/python3.13/site-packages/google/api_core/client_options.py', 'venv/lib/python3.13/site-packages/grpc/aio/_channel.py', 'venv/lib/python3.13/site-packages/google/oauth2/utils.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/generative_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/proto/marshal/rules/field_mask.py', 'venv/lib/python3.13/site-packages/google/type/fraction_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8360.py', 'venv/lib/python3.13/site-packages/grpc/aio/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/prediction_service/__init__.py', 'venv/lib/python3.13/site-packages/google/longrunning/operations_proto_pb2.py', 'venv/lib/python3.13/site-packages/charset_normalizer/cli/__main__.py', 'venv/lib/python3.13/site-packages/grpc/experimental/gevent.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/retriever_service/async_client.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/transports/rest_asyncio.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3281.py', 'venv/lib/python3.13/site-packages/google/api_core/universe.py', 'venv/lib/python3.13/site-packages/google/auth/version.py', 'venv/lib/python3.13/site-packages/google/api_core/grpc_helpers_async.py', 'venv/lib/python3.13/site-packages/google/api_core/operation_async.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5083.py', 'venv/lib/python3.13/site-packages/google/api_core/rest_helpers.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5940.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/prediction_service/transports/rest.py', 'venv/lib/python3.13/site-packages/googleapiclient/_auth.py', 'venv/lib/python3.13/site-packages/google/api/client_pb2.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/gapic_version.py', 'venv/lib/python3.13/site-packages/google/oauth2/credentials.py', 'venv/lib/python3.13/site-packages/grpc/beta/_server_adaptations.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5990.py', 'venv/lib/python3.13/site-packages/httplib2/auth.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/discuss_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/pagers.py', 'venv/lib/python3.13/site-packages/google/api_core/future/base.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc8702.py', 'venv/lib/python3.13/site-packages/grpc/aio/_base_server.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/model_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/api/control_pb2.py', 'venv/lib/python3.13/site-packages/charset_normalizer/__main__.py', 'venv/lib/python3.13/site-packages/google/type/interval_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4985.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6010.py', 'venv/lib/python3.13/site-packages/proto/marshal/marshal.py', 'venv/lib/python3.13/site-packages/grpc/experimental/session_cache.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4491.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/model_service/pagers.py', 'venv/lib/python3.13/site-packages/google/api_core/operations_v1/__init__.py', 'venv/lib/python3.13/site-packages/google/longrunning/operations_pb2_grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/model_service/__init__.py', 'venv/lib/python3.13/site-packages/requests/utils.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7906.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/model_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/proto/marshal/rules/bytes.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5126.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5755.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/file_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage/gapic_version.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4490.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/prediction_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/api_core/grpc_helpers.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5652.py', 'venv/lib/python3.13/site-packages/requests/compat.py', 'venv/lib/python3.13/site-packages/charset_normalizer/utils.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3412.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/services/model_service/transports/base.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7633.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc2251.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1/types/safety.py', 'venv/lib/python3.13/site-packages/httplib2/error.py', 'venv/lib/python3.13/site-packages/proto/marshal/rules/wrappers.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6031.py', 'venv/lib/python3.13/site-packages/google/oauth2/_id_token_async.py', 'venv/lib/python3.13/site-packages/google/auth/exceptions.py', 'venv/lib/python3.13/site-packages/rsa/key.py', 'venv/lib/python3.13/site-packages/google/api/system_parameter_pb2.py', 'venv/lib/python3.13/site-packages/proto/modules.py', 'venv/lib/python3.13/site-packages/google/type/date_pb2.py', 'venv/lib/python3.13/site-packages/google/api_core/exceptions.py', 'venv/lib/python3.13/site-packages/google/auth/aws.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc5924.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/generative_service/async_client.py', 'venv/lib/python3.13/site-packages/google/type/dayofweek_pb2.py', 'venv/lib/python3.13/site-packages/rsa/randnum.py', 'venv/lib/python3.13/site-packages/charset_normalizer/md.py', 'venv/lib/python3.13/site-packages/google/longrunning/operations_grpc_pb2.py', 'venv/lib/python3.13/site-packages/google/auth/_exponential_backoff.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3058.py', 'venv/lib/python3.13/site-packages/google/api/documentation_pb2.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4073.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3280.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/permission_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage/__init__.py', 'venv/lib/python3.13/site-packages/google/auth/aio/transport/aiohttp.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/discuss_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/model_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/oauth2/_client_async.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc7914.py', 'venv/lib/python3.13/site-packages/google/auth/aio/transport/__init__.py', 'venv/lib/python3.13/site-packages/charset_normalizer/models.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/retriever_service/__init__.py', 'venv/lib/python3.13/site-packages/grpc_status/__init__.py', 'venv/lib/python3.13/site-packages/google/oauth2/reauth.py', 'venv/lib/python3.13/site-packages/grpc/_interceptor.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/model_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/api_core/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3125.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4055.py', 'venv/lib/python3.13/site-packages/google/api/policy_pb2.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/discuss_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/auth/jwt.py', 'venv/lib/python3.13/site-packages/google/api_core/page_iterator.py', 'venv/lib/python3.13/site-packages/google/api_core/retry/retry_streaming.py', 'venv/lib/python3.13/site-packages/proto/marshal/collections/repeated.py', 'venv/lib/python3.13/site-packages/google/api_core/iam.py', 'venv/lib/python3.13/site-packages/grpc/_common.py', 'venv/lib/python3.13/site-packages/google/oauth2/__init__.py', 'venv/lib/python3.13/site-packages/google/api/auth_pb2.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/discuss_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4210.py', 'venv/lib/python3.13/site-packages/grpc/_grpcio_metadata.py', 'venv/lib/python3.13/site-packages/grpc/framework/common/cardinality.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc4334.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc1157.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc6487.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/model_service/async_client.py', 'venv/lib/python3.13/site-packages/google/auth/aio/transport/sessions.py', 'venv/lib/python3.13/site-packages/requests/sessions.py', 'venv/lib/python3.13/site-packages/pyasn1_modules/rfc3709.py'. Reloading...
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/types/citation.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/generation_types.py', 'venv/lib/python3.13/site-packages/google/generativeai/audio_models/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/discuss_service.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/model_registry.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/file_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/text_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/permission_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/gapic_version.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/input_utils.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/prediction_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/discuss_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/text_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/llmfn_input_utils.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/text_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/text_service/__init__.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/flag_def.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/permission_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/types/safety.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/retriever_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/prediction_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/model_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/cache_service/client.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/run_cmd.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/types/discuss_service.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/command_utils.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/file.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/discuss_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/llm_function.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/discuss_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/text_service.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/command.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/types/discuss_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/model_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/model_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/text_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/file_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/cache_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/prediction_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/discuss_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/text_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/html_utils.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/model_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/model_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/permission_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/model_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/text_service/async_client.py', 'venv/lib/python3.13/site-packages/google/generativeai/permission.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/file_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/model_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/prompt_utils.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/permission_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/model_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/generativeai/caching.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/permission_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/text_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/cache_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/permission.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/types/permission.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/retriever_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/citation_types.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/permission_service.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/sheets_sanitize_url.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/discuss_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/model_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/model_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/permission_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/generative_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/retriever_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/__init__.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/helper_types.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/file_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/model_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/model_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/discuss_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/text_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/cache_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/prediction_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/answer_types.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/llmfn_outputs.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/prediction_service.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/palm_safety_types.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/text_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/model_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/content.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/model_service.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/argument_parser.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/text_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/text_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/discuss_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/prediction_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/prediction_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/py_utils.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/text_model.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/permission_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/permission_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/text_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/sheets_id.py', 'venv/lib/python3.13/site-packages/google/generativeai/files.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/types/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/text_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/magics.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/text_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/ipython_env_impl.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/discuss_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/model_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/permission_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/file_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/text_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/retriever_service.py', 'venv/lib/python3.13/site-packages/google/generativeai/embedding.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/cache_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/model_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/file_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/text_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/text_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/cache_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/model.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/text_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/permission_service/client.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/magics_engine.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/permission_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/tuned_model.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/text_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/file_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/cached_content.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/discuss_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/retriever_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/retriever_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/discuss_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/discuss_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/generativeai/string_utils.py', 'venv/lib/python3.13/site-packages/google/generativeai/utils.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/retriever_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/model_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/eval_cmd.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/content.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/model_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/model_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/generative_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/retriever_types.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/model_types.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/permission_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/model_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/prediction_service/async_client.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/compile_cmd.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/cached_content.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/model_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/permission_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/discuss_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/text_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/permission_service/__init__.py', 'venv/lib/python3.13/site-packages/google/generativeai/answer.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/llmfn_post_process_cmds.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/safety.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/file_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/text_service/client.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/llmfn_post_process.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/gspread_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/retriever_service/client.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/unique_fn.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/discuss_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/text_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/model.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/discuss_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/text_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/output_utils.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/file_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/retriever_service/async_client.py', 'venv/lib/python3.13/site-packages/google/generativeai/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/cache_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/discuss_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/file_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/retriever.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/types/model_service.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/file_types.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/retriever_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/gapic_version.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/types/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/model_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/types/text_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/types/citation.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/ipython_env.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/sheets_utils.py', 'venv/lib/python3.13/site-packages/google/generativeai/audio_models/_audio_models.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/discuss_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/llmfn_inputs_source.py', 'venv/lib/python3.13/site-packages/google/generativeai/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/model_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/discuss_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/text_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/tuned_model.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/model_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/generativeai/protos.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/citation.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/model_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/types/model.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/text_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/discuss_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/cache_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/text_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/types/model.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/prediction_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/permission_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/generativeai/retriever.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/discuss_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/prediction_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/discuss_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/cache_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/generativeai/operations.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/gapic_version.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/model_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/permission_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/model_service/__init__.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/model.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/post_process_utils.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/citation.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/model_service/pagers.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/model_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/retriever_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/text_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/types/safety.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/safety_types.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/types/model_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/types/text_service.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/file_service/transports/grpc_asyncio.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/safety.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/retriever.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/permission_service/transports/grpc.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/permission_types.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/discuss_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/permission_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/discuss_service.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/text_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/permission_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/types/tuned_model.py', 'venv/lib/python3.13/site-packages/google/generativeai/version.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/permission_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/model_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/services/text_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/prediction_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/permission_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/cache_service/transports/__init__.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/content_types.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/post_process_utils_test_helper.py', 'venv/lib/python3.13/site-packages/google/generativeai/models.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/lib/llmfn_output_row.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/text_types.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/file_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/retriever_service/transports/rest_base.py', 'venv/lib/python3.13/site-packages/google/generativeai/types/caching_types.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/discuss_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/discuss_service/client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1alpha/types/permission.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/discuss_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/text_service/transports/rest.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/generative_service/transports/base.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/services/discuss_service/async_client.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/types/file.py', 'venv/lib/python3.13/site-packages/google/generativeai/generative_models.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta3/types/permission_service.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/parsed_args_lib.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/model_service/async_client.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/cmd_line_parser.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/__init__.py', 'venv/lib/python3.13/site-packages/google/generativeai/notebook/compare_cmd.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/cache_service/__init__.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta2/services/discuss_service/client.py', 'venv/lib/python3.13/site-packages/google/generativeai/responder.py', 'venv/lib/python3.13/site-packages/google/ai/generativelanguage_v1beta/services/model_service/transports/grpc.py'. Reloading...
Process SpawnProcess-6:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 9, in <module>
    from faster_whisper import WhisperModel
ModuleNotFoundError: No module named 'faster_whisper'
Process SpawnProcess-7:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 9, in <module>
    from faster_whisper import WhisperModel
ModuleNotFoundError: No module named 'faster_whisper'
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/sympy/sets/fancysets.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_rotation.py', 'venv/lib/python3.13/site-packages/sympy/polys/benchmarks/bench_solvers.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_immutable.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/delta_functions.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_maxima.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/tests/test_integers.py', 'venv/lib/python3.13/site-packages/sympy/algebras/tests/test_quaternion.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_rref.py', 'venv/lib/python3.13/site-packages/sympy/geometry/tests/test_entity.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/finitefield.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/facts.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/tests/test_frame.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_partfrac.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_slice.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/systems/cgs.py', 'venv/lib/python3.13/site-packages/sympy/plotting/tests/test_textplot.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_cse_diff.py', 'venv/lib/python3.13/site-packages/sympy/core/decorators.py', 'venv/lib/python3.13/site-packages/sympy/codegen/abstract_nodes.py', 'venv/lib/python3.13/site-packages/sympy/core/facts.py', 'venv/lib/python3.13/site-packages/sympy/polys/rootoftools.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_functions2.py', 'venv/lib/python3.13/site-packages/sympy/strategies/core.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_density.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_matmul.py', 'venv/lib/python3.13/site-packages/sympy/external/pythonmpq.py', 'venv/lib/python3.13/site-packages/sympy/core/_print_helpers.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/__init__.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/inertia.py', 'venv/lib/python3.13/site-packages/sympy/geometry/curve.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_operations.py', 'venv/lib/python3.13/site-packages/sympy/discrete/convolutions.py', 'venv/lib/python3.13/site-packages/sympy/plotting/intervalmath/interval_membership.py', 'venv/lib/python3.13/site-packages/sympy/utilities/codegen.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_named_groups.py', 'venv/lib/python3.13/site-packages/sympy/matrices/matrixbase.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/tests/test_context.py', 'venv/lib/python3.13/site-packages/sympy/calculus/tests/test_finite_diff.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/fractionfield.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_constructor.py', 'venv/lib/python3.13/site-packages/sympy/printing/codeprinter.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/tests/test_util.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_nseries.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/pydy-example-repo/chaos_pendulum.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/linsolve.py', 'venv/lib/python3.13/site-packages/sympy/logic/algorithms/z3_wrapper.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_pyutils.py', 'venv/lib/python3.13/site-packages/sympy/stats/drv_types.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_demidovich.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_qft.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/subscheck.py', 'venv/lib/python3.13/site-packages/sympy/integrals/singularityfunctions.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/gamma_functions.py', 'venv/lib/python3.13/site-packages/sympy/tensor/index_methods.py', 'venv/lib/python3.13/site-packages/mpmath/calculus/polynomials.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/__init__.py', 'venv/lib/python3.13/site-packages/sympy/parsing/mathematica.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_modulargcd.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/matrixcache.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/tests/test_query.py', 'venv/lib/python3.13/site-packages/sympy/sandbox/tests/__init__.py', 'venv/lib/python3.13/site-packages/sympy/simplify/simplify.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_linsolve.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_implicit_multiplication_application.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/constants.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_aesaracode.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/qasm.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/group_constructs.py', 'venv/lib/python3.13/site-packages/sympy/interactive/tests/test_ipython.py', 'venv/lib/python3.13/site-packages/sympy/solvers/solvers.py', 'venv/lib/python3.13/site-packages/sympy/external/tests/test_codegen.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_power.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_determinant.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/bessel.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/tests/test_prefixes.py', 'venv/lib/python3.13/site-packages/sympy/printing/llvmjitcode.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/tests/test_interface.py', 'venv/lib/python3.13/site-packages/sympy/polys/monomials.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_polyhedron.py', 'venv/lib/python3.13/site-packages/sympy/testing/tests/test_deprecated.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_axes.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/simpledomain.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_integrals.py', 'venv/lib/python3.13/site-packages/sympy/plotting/intervalmath/__init__.py', 'venv/lib/python3.13/site-packages/mpmath/functions/zetazeros.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/gmpyintegerring.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/slice.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_kane2.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/__init__.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_smtlib.py', 'venv/lib/python3.13/site-packages/sympy/simplify/powsimp.py', 'venv/lib/python3.13/site-packages/sympy/testing/tests/test_runtests_pytest.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_particle.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest5.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_shor.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/transforms.py', 'venv/lib/python3.13/site-packages/sympy/utilities/_compilation/__init__.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/galoisgroups.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/tests/test_numbers.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_jax.py', 'venv/lib/python3.13/site-packages/sympy/parsing/sympy_parser.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/multinomial.py', 'venv/lib/python3.13/site-packages/sympy/abc.py', 'venv/lib/python3.13/site-packages/sympy/calculus/singularities.py', 'venv/lib/python3.13/site-packages/sympy/series/benchmarks/bench_limit.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_printing.py', 'venv/lib/python3.13/site-packages/sympy/polys/densebasic.py', 'venv/lib/python3.13/site-packages/sympy/simplify/hyperexpand.py', 'venv/lib/python3.13/site-packages/sympy/stats/symbolic_multivariate_probability.py', 'venv/lib/python3.13/site-packages/sympy/plotting/backends/matplotlibbackend/__init__.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/circuitutils.py', 'venv/lib/python3.13/site-packages/sympy/sets/handlers/functions.py', 'venv/lib/python3.13/site-packages/sympy/polys/galoistools.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_eigen.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_codegen.py', 'venv/lib/python3.13/site-packages/sympy/series/limitseq.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/trace.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/ask_generated.py', 'venv/lib/python3.13/site-packages/sympy/stats/rv.py', 'venv/lib/python3.13/site-packages/sympy/printing/lambdarepr.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_commutator.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_indexing.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/definitions/__init__.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_method.py', 'venv/lib/python3.13/site-packages/sympy/plotting/utils.py', 'venv/lib/python3.13/site-packages/sympy/stats/crv.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_ratsimp.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/tests/test_polarization.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest6.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/relation/binrel.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_bbp_pi.py', 'venv/lib/python3.13/site-packages/sympy/logic/algorithms/dpll2.py', 'venv/lib/python3.13/site-packages/sympy/stats/__init__.py', 'venv/lib/python3.13/site-packages/sympy/printing/pytorch.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/diagonal.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_str.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/companion.py', 'venv/lib/python3.13/site-packages/sympy/conftest.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/hyperbolic.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/dimensions.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/tests/test_ndim_array_conversions.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/util.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_type_G.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/predicates/__init__.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/shor.py', 'venv/lib/python3.13/site-packages/sympy/printing/printer.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/tests/test_array_comprehension.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/waves.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/matexpr.py', 'venv/lib/python3.13/site-packages/sympy/matrices/kind.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_calculus.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_abstract_nodes.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_operator.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_tensorproduct.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_cxxnodes.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/hadamard.py', 'venv/lib/python3.13/site-packages/sympy/parsing/latex/lark/__init__.py', 'venv/lib/python3.13/site-packages/sympy/polys/fields.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_compound_rv.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_codeprinter.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_manual.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/polynomialring.py', 'venv/lib/python3.13/site-packages/flatbuffers/util.py', 'venv/lib/python3.13/site-packages/sympy/categories/__init__.py', 'venv/lib/python3.13/site-packages/sympy/solvers/tests/test_solveset.py', 'venv/lib/python3.13/site-packages/sympy/diffgeom/tests/test_diffgeom.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest7.py', 'venv/lib/python3.13/site-packages/sympy/utilities/decorator.py', 'venv/lib/python3.13/site-packages/sympy/core/trace.py', 'venv/lib/python3.13/site-packages/sympy/utilities/_compilation/availability.py', 'venv/lib/python3.13/site-packages/sympy/concrete/__init__.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/dagger.py', 'venv/lib/python3.13/site-packages/sympy/external/importtools.py', 'venv/lib/python3.13/site-packages/sympy/geometry/tests/test_util.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_str.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_represent.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_gamma_functions.py', 'venv/lib/python3.13/site-packages/sympy/sets/setexpr.py', 'venv/lib/python3.13/site-packages/sympy/core/power.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_rcode.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_digits.py', 'venv/lib/python3.13/site-packages/sympy/strategies/branch/core.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/tests/test_as_explicit.py', 'venv/lib/python3.13/site-packages/sympy/physics/hep/gamma_matrices.py', 'venv/lib/python3.13/site-packages/sympy/logic/algorithms/minisat22_wrapper.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/elliptic_curve.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_sparse.py', 'venv/lib/python3.13/site-packages/sympy/series/sequences.py', 'venv/lib/python3.13/site-packages/sympy/simplify/__init__.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_spin.py', 'venv/lib/python3.13/site-packages/sympy/physics/continuum_mechanics/truss.py', 'venv/lib/python3.13/site-packages/sympy/plotting/__init__.py', 'venv/lib/python3.13/site-packages/sympy/core/benchmarks/bench_expand.py', 'venv/lib/python3.13/site-packages/sympy/sets/sets.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_diff.py', 'venv/lib/python3.13/site-packages/sympy/series/benchmarks/bench_order.py', 'venv/lib/python3.13/site-packages/sympy/physics/hydrogen.py', 'venv/lib/python3.13/site-packages/sympy/core/logic.py', 'venv/lib/python3.13/site-packages/sympy/sets/tests/test_sets.py', 'venv/lib/python3.13/site-packages/sympy/polys/orthopolys.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_homomorphisms.py', 'venv/lib/python3.13/site-packages/sympy/strategies/tests/test_tree.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_pc_groups.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_factortools.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_cartan_matrix.py', 'venv/lib/python3.13/site-packages/sympy/functions/combinatorial/tests/test_comb_factorials.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/predicates/matrices.py', 'venv/lib/python3.13/site-packages/sympy/unify/core.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_cupy.py', 'venv/lib/python3.13/site-packages/sympy/logic/algorithms/pycosat_wrapper.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_power.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/array_derivatives.py', 'venv/lib/python3.13/site-packages/sympy/plotting/experimental_lambdify.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/subsets.py', 'venv/lib/python3.13/site-packages/sympy/unify/usympy.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/tests/test_waves.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_basic.py', 'venv/lib/python3.13/site-packages/sympy/core/backend.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/determinant.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/rewritingsystem_fsm.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/systems.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_preview.py', 'venv/lib/python3.13/site-packages/sympy/parsing/fortran/__init__.py', 'venv/lib/python3.13/site-packages/sympy/strategies/tests/test_rl.py', 'venv/lib/python3.13/site-packages/sympy/core/coreerrors.py', 'venv/lib/python3.13/site-packages/sympy/series/__init__.py', 'venv/lib/python3.13/site-packages/sympy/solvers/tests/test_simplex.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_powsimp.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_lineintegrals.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_joint.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_system_class.py', 'venv/lib/python3.13/site-packages/sympy/printing/mathml.py', 'venv/lib/python3.13/site-packages/sympy/polys/fglmtools.py', 'venv/lib/python3.13/site-packages/sympy/printing/rcode.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/tests/test_convert_array_to_indexed.py', 'venv/lib/python3.13/site-packages/sympy/testing/runtests_pytest.py', 'venv/lib/python3.13/site-packages/sympy/printing/aesaracode.py', 'venv/lib/python3.13/site-packages/sympy/geometry/tests/test_line.py', 'venv/lib/python3.13/site-packages/sympy/printing/octave.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/old_polynomialring.py', 'venv/lib/python3.13/site-packages/sympy/core/relational.py', 'venv/lib/python3.13/site-packages/sympy/physics/control/lti.py', 'venv/lib/python3.13/site-packages/mpmath/usertools.py', 'venv/lib/python3.13/site-packages/sympy/categories/tests/test_baseclasses.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_object.py', 'venv/lib/python3.13/site-packages/sympy/external/tests/test_autowrap.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_kane.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_trig.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/exceptions.py', 'venv/lib/python3.13/site-packages/sympy/external/ntheory.py', 'venv/lib/python3.13/site-packages/sympy/polys/polyconfig.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_sqfreetools.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/tests/test_hyperbolic.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_cfunctions.py', 'venv/lib/python3.13/site-packages/sympy/logic/__init__.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_dotproduct.py', 'venv/lib/python3.13/site-packages/sympy/diffgeom/__init__.py', 'venv/lib/python3.13/site-packages/sympy/geometry/line.py', 'venv/lib/python3.13/site-packages/sympy/tensor/indexed.py', 'venv/lib/python3.13/site-packages/mpmath/identification.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/state.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/permutation.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_qapply.py', 'venv/lib/python3.13/site-packages/mpmath/matrices/__init__.py', 'venv/lib/python3.13/site-packages/sympy/holonomic/numerical.py', 'venv/lib/python3.13/site-packages/sympy/core/__init__.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_linearity_of_velocity_constraints.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_logic.py', 'venv/lib/python3.13/site-packages/sympy/polys/agca/__init__.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_bsplines.py', 'venv/lib/python3.13/site-packages/sympy/testing/tests/test_module_imports.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_gammasimp.py', 'venv/lib/python3.13/site-packages/sympy/sets/conditionset.py', 'venv/lib/python3.13/site-packages/sympy/series/formal.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_latex_deps.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/normalforms.py', 'venv/lib/python3.13/site-packages/flatbuffers/table.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_funcmatrix.py', 'venv/lib/python3.13/site-packages/sympy/polys/polyoptions.py', 'venv/lib/python3.13/site-packages/mpmath/tests/extratest_zeta.py', 'venv/lib/python3.13/site-packages/sympy/matrices/determinant.py', 'venv/lib/python3.13/site-packages/sympy/series/gruntz.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_expand.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/util.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_gtk.py', 'venv/lib/python3.13/site-packages/sympy/physics/biomechanics/tests/test_curve.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_matrix_distributions.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_models.py', 'venv/lib/python3.13/site-packages/sympy/solvers/tests/test_recurr.py', 'venv/lib/python3.13/site-packages/sympy/tensor/functions.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/generate.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_symbolic_probability.py', 'venv/lib/python3.13/site-packages/sympy/concrete/tests/test_sums_products.py', 'venv/lib/python3.13/site-packages/sympy/integrals/intpoly.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/tests/test_functions.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_primetest.py', 'venv/lib/python3.13/site-packages/sympy/stats/symbolic_probability.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_wrapping_geometry.py', 'venv/lib/python3.13/site-packages/sympy/strategies/traverse.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_trigonometry.py', 'venv/lib/python3.13/site-packages/sympy/simplify/fu.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_determinant.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_tensor_can.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/from_array_to_indexed.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/tests/test_immutable_ndim_array.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_approximants.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/tests/test_point.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_cg.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/system.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_matpow.py', 'venv/lib/python3.13/site-packages/sympy/polys/benchmarks/bench_galoispolys.py', 'venv/lib/python3.13/site-packages/mpmath/calculus/extrapolation.py', 'venv/lib/python3.13/site-packages/sympy/matrices/__init__.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_transpose.py', 'venv/lib/python3.13/site-packages/sympy/logic/algorithms/lra_theory.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/permutations.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_truediv.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_type_C.py', 'venv/lib/python3.13/site-packages/sympy/polys/agca/tests/test_homomorphisms.py', 'venv/lib/python3.13/site-packages/mpmath/functions/hypergeometric.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/from_indexed_to_array.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/resolvent_lookup.py', 'venv/lib/python3.13/site-packages/sympy/diffgeom/tests/test_function_diffgeom_book.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_hypothesis.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_cse.py', 'venv/lib/python3.13/site-packages/sympy/solvers/tests/test_inequalities.py', 'venv/lib/python3.13/site-packages/sympy/discrete/tests/test_transforms.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/special.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/ode.py', 'venv/lib/python3.13/site-packages/sympy/solvers/tests/test_pde.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_scipy_nodes.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_hypothesis.py', 'venv/lib/python3.13/site-packages/sympy/physics/wigner.py', 'venv/lib/python3.13/site-packages/mpmath/calculus/odes.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_group_numbers.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_modes.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tensor_functions.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/piab.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_limits.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_mix.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_distributedmodules.py', 'venv/lib/python3.13/site-packages/sympy/simplify/combsimp.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_complex.py', 'venv/lib/python3.13/site-packages/sympy/simplify/hyperexpand_doc.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_mathieu.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_qasm.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_partitions.py', 'venv/lib/python3.13/site-packages/sympy/utilities/_compilation/runners.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_innerproduct.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/fermion.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_latex.py', 'venv/lib/python3.13/site-packages/sympy/this.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_formal.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_group_constructs.py', 'venv/lib/python3.13/site-packages/sympy/functions/__init__.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/lie_group.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/medium.py', 'venv/lib/python3.13/site-packages/sympy/polys/ring_series.py', 'venv/lib/python3.13/site-packages/sympy/concrete/delta.py', 'venv/lib/python3.13/site-packages/sympy/codegen/cfunctions.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_blockmatrix.py', 'venv/lib/python3.13/site-packages/sympy/strategies/branch/tests/test_tools.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_special.py', 'venv/lib/python3.13/site-packages/sympy/categories/baseclasses.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_failing_integrals.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_cxx.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/gmpyfinitefield.py', 'venv/lib/python3.13/site-packages/sympy/unify/rewrite.py', 'venv/lib/python3.13/site-packages/sympy/series/acceleration.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/tests/test_convert_matrix_to_array.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/type_a.py', 'venv/lib/python3.13/site-packages/flatbuffers/builder.py', 'venv/lib/python3.13/site-packages/sympy/sets/tests/test_setexpr.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_domainscalar.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/mathieu_functions.py', 'venv/lib/python3.13/site-packages/sympy/polys/groebnertools.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/operatorordering.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_rootisolation.py', 'venv/lib/python3.13/site-packages/sympy/matrices/dense.py', 'venv/lib/python3.13/site-packages/sympy/calculus/tests/test_util.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/from_matrix_to_array.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/type_e.py', 'venv/lib/python3.13/site-packages/sympy/matrices/utilities.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/partitions_.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/qs.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/hypergeometric.py', 'venv/lib/python3.13/site-packages/mpmath/calculus/optimization.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/__init__.py', 'venv/lib/python3.13/site-packages/sympy/sets/handlers/union.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/handlers/__init__.py', 'venv/lib/python3.13/site-packages/sympy/solvers/diophantine/tests/test_diophantine.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/tests/test_galoisgroups.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_perm_groups.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/tests/test_polynomialring.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/tests/test_miscellaneous.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/pydy-example-repo/non_min_pendulum.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_combsimp.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tensorproduct.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/matadd.py', 'venv/lib/python3.13/site-packages/sympy/testing/__init__.py', 'venv/lib/python3.13/site-packages/sympy/discrete/tests/test_convolutions.py', 'venv/lib/python3.13/site-packages/sympy/stats/stochastic_process_types.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/arrayop.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_polymatrix.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_aseries.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_visualization.py', 'venv/lib/python3.13/site-packages/sympy/utilities/memoization.py', 'venv/lib/python3.13/site-packages/sympy/strategies/branch/tests/test_core.py', 'venv/lib/python3.13/site-packages/sympy/physics/biomechanics/tests/test_musculotendon.py', 'venv/lib/python3.13/site-packages/sympy/physics/control/control_plots.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_normalforms.py', 'venv/lib/python3.13/site-packages/sympy/calculus/util.py', 'venv/lib/python3.13/site-packages/sympy/core/assumptions_generated.py', 'venv/lib/python3.13/site-packages/sympy/concrete/summations.py', 'venv/lib/python3.13/site-packages/sympy/polys/domainmatrix.py', 'venv/lib/python3.13/site-packages/sympy/core/evalf.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/perm_groups.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_graph.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/trigonometric.py', 'venv/lib/python3.13/site-packages/sympy/plotting/intervalmath/interval_arithmetic.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_rationaltools.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/_antlr/autolevparser.py', 'venv/lib/python3.13/site-packages/sympy/plotting/intervalmath/lib_interval.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/beta_functions.py', 'venv/lib/python3.13/site-packages/mpmath/matrices/eigen_symmetric.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/density.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/operatorset.py', 'venv/lib/python3.13/site-packages/sympy/codegen/algorithms.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/handlers/ntheory.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/cartan_matrix.py', 'venv/lib/python3.13/site-packages/sympy/integrals/trigonometry.py', 'venv/lib/python3.13/site-packages/sympy/physics/biomechanics/tests/test_mixin.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_lambdarepr.py', 'venv/lib/python3.13/site-packages/sympy/logic/algorithms/dpll.py', 'venv/lib/python3.13/site-packages/sympy/logic/tests/test_boolalg.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/tests/test_array_derivatives.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_numbers.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_matrices.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_hilbert.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_gammazeta.py', 'venv/lib/python3.13/site-packages/sympy/concrete/tests/test_products.py', 'venv/lib/python3.13/site-packages/sympy/simplify/trigsimp.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/groundtypes.py', 'venv/lib/python3.13/site-packages/sympy/logic/boolalg.py', 'venv/lib/python3.13/site-packages/sympy/matrices/immutable.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_numpy_nodes.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_epathtools.py', 'venv/lib/python3.13/site-packages/sympy/stats/frv.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_ode.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_approximations.py', 'venv/lib/python3.13/site-packages/sympy/plotting/backends/textbackend/text.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/satask.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_linalg.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_matexpr.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/polyhedron.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/sho1d.py', 'venv/lib/python3.13/site-packages/sympy/integrals/meijerint_doc.py', 'venv/lib/python3.13/site-packages/sympy/physics/continuum_mechanics/__init__.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/utils.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_rewriting.py', 'venv/lib/python3.13/site-packages/sympy/utilities/pytest.py', 'venv/lib/python3.13/site-packages/sympy/physics/tests/test_physics_matrices.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_dot.py', 'venv/lib/python3.13/site-packages/sympy/polys/sqfreetools.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/kronecker.py', 'venv/lib/python3.13/site-packages/mpmath/tests/runtests.py', 'venv/lib/python3.13/site-packages/sympy/holonomic/holonomic.py', 'venv/lib/python3.13/site-packages/sympy/benchmarks/bench_symbench.py', 'venv/lib/python3.13/site-packages/sympy/printing/tensorflow.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/__init__.py', 'venv/lib/python3.13/site-packages/sympy/utilities/randtest.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_loads.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_continuous_rv.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_containers.py', 'venv/lib/python3.13/site-packages/sympy/tensor/tests/test_tensor.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_random_matrix.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/rref.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/domainmatrix.py', 'venv/lib/python3.13/site-packages/sympy/geometry/plane.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_tree.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/dynkin_diagram.py', 'venv/lib/python3.13/site-packages/sympy/plotting/intervalmath/tests/test_interval_functions.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/represent.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_heuristicgcd.py', 'venv/lib/python3.13/site-packages/sympy/printing/__init__.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_function.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/mpelements.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_specialpolys.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_matrixbase.py', 'venv/lib/python3.13/site-packages/sympy/parsing/maxima.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/old_fractionfield.py', 'venv/lib/python3.13/site-packages/sympy/core/function.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/prufer.py', 'venv/lib/python3.13/site-packages/sympy/concrete/tests/__init__.py', 'venv/lib/python3.13/site-packages/sympy/stats/matrix_distributions.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_type_E.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_solvers.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_controller.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_sho1d.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/gaussiandomains.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/type_g.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/funcmatrix.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/systems/mksa.py', 'venv/lib/python3.13/site-packages/sympy/categories/tests/test_drawing.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/tests/test_quotientring.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/bsplines.py', 'venv/lib/python3.13/site-packages/sympy/polys/constructor.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_eigen_symmetric.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/digits.py', 'venv/lib/python3.13/site-packages/mpmath/libmp/libelefun.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_generators.py', 'venv/lib/python3.13/site-packages/mpmath/function_docs.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_densearith.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest3.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/partitions.py', 'venv/lib/python3.13/site-packages/sympy/polys/euclidtools.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/benchmarks/bench_special.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/anticommutator.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/algebraicfield.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_linearize.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_maple.py', 'venv/lib/python3.13/site-packages/sympy/physics/biomechanics/musculotendon.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_conventions.py', 'venv/lib/python3.13/site-packages/sympy/series/series_class.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/tests/test_subfield.py', 'venv/lib/python3.13/site-packages/sympy/testing/tmpfiles.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/named_groups.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/handlers/matrices.py', 'venv/lib/python3.13/site-packages/sympy/sandbox/__init__.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/tests/test_convert_indexed_to_array.py', 'venv/lib/python3.13/site-packages/sympy/testing/tests/diagnose_imports.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/_antlr/__init__.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_ring_series.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_summation.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_factor_.py', 'venv/lib/python3.13/site-packages/sympy/utilities/timeutils.py', 'venv/lib/python3.13/site-packages/sympy/printing/tree.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/tests/test_dimensions.py', 'venv/lib/python3.13/site-packages/sympy/holonomic/tests/test_holonomic.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest10.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/rationalfield.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_transforms.py', 'venv/lib/python3.13/site-packages/sympy/algebras/__init__.py', 'venv/lib/python3.13/site-packages/sympy/solvers/recurr.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_codegen_octave.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_tensor_functions.py', 'venv/lib/python3.13/site-packages/sympy/functions/combinatorial/numbers.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_galoistools.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_mode_base.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_actuator.py', 'venv/lib/python3.13/site-packages/sympy/plotting/series.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/array_comprehension.py', 'venv/lib/python3.13/site-packages/sympy/discrete/tests/test_recurrences.py', 'venv/lib/python3.13/site-packages/sympy/geometry/tests/test_curve.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/elliptic_integrals.py', 'venv/lib/python3.13/site-packages/sympy/polys/rings.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_window.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_constants.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/exceptions.py', 'venv/lib/python3.13/site-packages/sympy/interactive/tests/test_interactive.py', 'venv/lib/python3.13/site-packages/sympy/simplify/traversaltools.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_fourier.py', 'venv/lib/python3.13/site-packages/sympy/plotting/intervalmath/tests/test_interval_membership.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/group_numbers.py', 'venv/lib/python3.13/site-packages/mpmath/calculus/inverselaplace.py', 'venv/lib/python3.13/site-packages/sympy/physics/__init__.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/piecewise.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/tests/test_rel_queries.py', 'venv/lib/python3.13/site-packages/sympy/geometry/tests/test_ellipse.py', 'venv/lib/python3.13/site-packages/sympy/stats/sampling/sample_pymc.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/tests/test_ode.py', 'venv/lib/python3.13/site-packages/sympy/printing/preview.py', 'venv/lib/python3.13/site-packages/sympy/printing/repr.py', 'venv/lib/python3.13/site-packages/sympy/physics/paulialgebra.py', 'venv/lib/python3.13/site-packages/mpmath/functions/signals.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_tensorflow.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_function.py', 'venv/lib/python3.13/site-packages/sympy/simplify/gammasimp.py', 'venv/lib/python3.13/site-packages/sympy/polys/distributedmodules.py', 'venv/lib/python3.13/site-packages/mpmath/functions/expintegrals.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/systems/length_weight_time.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/tests/test_matrices.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/fourier.py', 'venv/lib/python3.13/site-packages/sympy/diffgeom/tests/test_class_structure.py', 'venv/lib/python3.13/site-packages/sympy/polys/factortools.py', 'venv/lib/python3.13/site-packages/sympy/integrals/prde.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/models.py', 'venv/lib/python3.13/site-packages/sympy/core/mod.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/tests/test_trigonometric.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/testutil.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/_dfm.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/pc_groups.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_levin.py', 'venv/lib/python3.13/site-packages/sympy/stats/stochastic_process.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/basis.py', 'venv/lib/python3.13/site-packages/sympy/codegen/matrix_nodes.py', 'venv/lib/python3.13/site-packages/sympy/geometry/tests/test_geometrysets.py', 'venv/lib/python3.13/site-packages/sympy/sets/tests/test_ordinals.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/tests/test_primes.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_egyptian_fraction.py', 'venv/lib/python3.13/site-packages/sympy/polys/rootisolation.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/managed_window.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/gate.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/systems/mks.py', 'venv/lib/python3.13/site-packages/sympy/utilities/matchpy_connector.py', 'venv/lib/python3.13/site-packages/sympy/sets/tests/test_contains.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/tests/test_ndim_array.py', 'venv/lib/python3.13/site-packages/sympy/parsing/sym_expr.py', 'venv/lib/python3.13/site-packages/sympy/plotting/tests/test_series.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_continued_fraction.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/kind.py', 'venv/lib/python3.13/site-packages/sympy/printing/conventions.py', 'venv/lib/python3.13/site-packages/mpmath/ctx_mp.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_trace.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_arit.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_jscode.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_subspaces.py', 'venv/lib/python3.13/site-packages/sympy/core/benchmarks/bench_assumptions.py', 'venv/lib/python3.13/site-packages/sympy/holonomic/tests/test_recurrence.py', 'venv/lib/python3.13/site-packages/sympy/printing/glsl.py', 'venv/lib/python3.13/site-packages/mpmath/calculus/approximation.py', 'venv/lib/python3.13/site-packages/sympy/polys/orderings.py', 'venv/lib/python3.13/site-packages/sympy/benchmarks/bench_discrete_log.py', 'venv/lib/python3.13/site-packages/sympy/physics/sho.py', 'venv/lib/python3.13/site-packages/sympy/codegen/cutils.py', 'venv/lib/python3.13/site-packages/mpmath/tests/torture.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_functions.py', 'venv/lib/python3.13/site-packages/sympy/geometry/util.py', 'venv/lib/python3.13/site-packages/sympy/physics/hep/tests/test_gamma_matrices.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_subs.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_octave.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/tests/test_dimensionsystem.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/jointsmethod.py', 'venv/lib/python3.13/site-packages/sympy/multipledispatch/conflict.py', 'venv/lib/python3.13/site-packages/sympy/strategies/rl.py', 'venv/lib/python3.13/site-packages/sympy/categories/tests/__init__.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_heurisch.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/definitions/dimension_definitions.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_spherical_harmonics.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_sympy_parser.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_lagrange2.py', 'venv/lib/python3.13/site-packages/mpmath/functions/rszeta.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_laplace.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/tests/test_gaussopt.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_polyutils.py', 'venv/lib/python3.13/site-packages/sympy/core/benchmarks/bench_numbers.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/arrayexpr_derivatives.py', 'venv/lib/python3.13/site-packages/sympy/sets/handlers/mul.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/util.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_zeta_functions.py', 'venv/lib/python3.13/site-packages/sympy/polys/specialpolys.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_sym_expr.py', 'venv/lib/python3.13/site-packages/sympy/matrices/inverse.py', 'venv/lib/python3.13/site-packages/mpmath/ctx_base.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_injections.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_mpmath.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/primetest.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_kind.py', 'venv/lib/python3.13/site-packages/sympy/codegen/numpy_nodes.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/tests/test_exponential.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/tests/test_complexes.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_rv.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_hadamard.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/tests/test_domains.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/type_f.py', 'venv/lib/python3.13/site-packages/sympy/printing/theanocode.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_finite_rv.py', 'venv/lib/python3.13/site-packages/sympy/utilities/source.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_generate.py', 'venv/lib/python3.13/site-packages/sympy/stats/sampling/sample_scipy.py', 'venv/lib/python3.13/site-packages/sympy/external/tests/test_importtools.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/primes.py', 'venv/lib/python3.13/site-packages/sympy/polys/subresultants_qq_zz.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_densetools.py', 'venv/lib/python3.13/site-packages/sympy/core/traversal.py', 'venv/lib/python3.13/site-packages/sympy/core/compatibility.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/residue_ntheory.py', 'venv/lib/python3.13/site-packages/sympy/physics/biomechanics/activation.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_pickle.py', 'venv/lib/python3.13/site-packages/mpmath/matrices/eigen.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_glsl.py', 'venv/lib/python3.13/site-packages/sympy/logic/utilities/dimacs.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_latex.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_mathematica.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/refine.py', 'venv/lib/python3.13/site-packages/sympy/sets/tests/test_fancysets.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_gate.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/quotientring.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/zeta_functions.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/__init__.py', 'venv/lib/python3.13/site-packages/flatbuffers/__init__.py', 'venv/lib/python3.13/site-packages/mpmath/rational.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/handlers/calculus.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_cache.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/integerring.py', 'venv/lib/python3.13/site-packages/sympy/plotting/tests/test_plot.py', 'venv/lib/python3.13/site-packages/sympy/physics/control/tests/test_control_plots.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_graycode.py', 'venv/lib/python3.13/site-packages/mpmath/libmp/__init__.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_rewrite.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_inertia.py', 'venv/lib/python3.13/site-packages/sympy/solvers/tests/test_decompogen.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_repr.py', 'venv/lib/python3.13/site-packages/sympy/plotting/tests/test_plot_implicit.py', 'venv/lib/python3.13/site-packages/sympy/physics/qho_1d.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_python.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_count_ops.py', 'venv/lib/python3.13/site-packages/sympy/logic/tests/test_dimacs.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/homomorphisms.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/pydy-example-repo/mass_spring_damper.py', 'venv/lib/python3.13/site-packages/sympy/polys/modulargcd.py', 'venv/lib/python3.13/site-packages/sympy/strategies/tests/test_tools.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/eigen.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_orthopolys.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_error_prop.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/pythonfinitefield.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_camera.py', 'venv/lib/python3.13/site-packages/mpmath/calculus/differentiation.py', 'venv/lib/python3.13/site-packages/sympy/integrals/rationaltools.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/spin.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_fields.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_var.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_llvmjit.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/hilbert.py', 'venv/lib/python3.13/site-packages/sympy/utilities/autowrap.py', 'venv/lib/python3.13/site-packages/sympy/stats/rv_interface.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/compositedomain.py', 'venv/lib/python3.13/site-packages/sympy/polys/solvers.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/__init__.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_reductions.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_cartesian.py', 'venv/lib/python3.13/site-packages/sympy/concrete/tests/test_delta.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_convert.py', 'venv/lib/python3.13/site-packages/sympy/codegen/futils.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/dfm.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/continued_fraction.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/__init__.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/dotproduct.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/handlers/order.py', 'venv/lib/python3.13/site-packages/sympy/codegen/cxxnodes.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/expressiondomain.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest12.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/inverse.py', 'venv/lib/python3.13/site-packages/sympy/core/core.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/__init__.py', 'venv/lib/python3.13/site-packages/sympy/polys/agca/tests/test_ideals.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_relational.py', 'venv/lib/python3.13/site-packages/sympy/concrete/guess.py', 'venv/lib/python3.13/site-packages/sympy/core/parameters.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_fp_groups.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_hyperexpand.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_permutation.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/tests/test_satask.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/adjoint.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_multidimensional.py', 'venv/lib/python3.13/site-packages/sympy/stats/joint_rv_types.py', 'venv/lib/python3.13/site-packages/sympy/core/expr.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest1.py', 'venv/lib/python3.13/site-packages/sympy/core/benchmarks/bench_sympify.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_xxm.py', 'venv/lib/python3.13/site-packages/sympy/strategies/tree.py', 'venv/lib/python3.13/site-packages/sympy/sets/handlers/intersection.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_derivatives.py', 'venv/lib/python3.13/site-packages/sympy/integrals/quadrature.py', 'venv/lib/python3.13/site-packages/sympy/external/tests/test_pythonmpq.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/_trigonometric_special.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/sets.py', 'venv/lib/python3.13/site-packages/sympy/external/tests/test_ntheory.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/tests/test_vector.py', 'venv/lib/python3.13/site-packages/sympy/utilities/_compilation/util.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest8.py', 'venv/lib/python3.13/site-packages/sympy/simplify/_cse_diff.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_trace.py', 'venv/lib/python3.13/site-packages/sympy/core/operations.py', 'venv/lib/python3.13/site-packages/sympy/testing/tests/test_pytest.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/qapply.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_coset_table.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_polyroots.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/__init__.py', 'venv/lib/python3.13/site-packages/sympy/interactive/traversal.py', 'venv/lib/python3.13/site-packages/sympy/strategies/tests/test_core.py', 'venv/lib/python3.13/site-packages/sympy/matrices/eigen.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_error_functions.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/domain.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/quantities.py', 'venv/lib/python3.13/site-packages/sympy/testing/tests/test_code_quality.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/_listener_autolev_antlr.py', 'venv/lib/python3.13/site-packages/sympy/external/tests/test_numpy.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_radsimp.py', 'venv/lib/python3.13/site-packages/sympy/strategies/branch/__init__.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/root_system.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_modular.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_subresultants_qq_zz.py', 'venv/lib/python3.13/site-packages/sympy/physics/continuum_mechanics/tests/test_cable.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/tests/test_basis.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/domainscalar.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_operatorordering.py', 'venv/lib/python3.13/site-packages/sympy/plotting/backends/matplotlibbackend/matplotlib.py', 'venv/lib/python3.13/site-packages/sympy/matrices/repmatrix.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/gmpyrationalfield.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_pycode.py', 'venv/lib/python3.13/site-packages/sympy/integrals/benchmarks/bench_integrate.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/dense.py', 'venv/lib/python3.13/site-packages/sympy/multipledispatch/tests/test_dispatcher.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/tests/test_unitsystem.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/tests/test_subscheck.py', 'venv/lib/python3.13/site-packages/sympy/strategies/tools.py', 'venv/lib/python3.13/site-packages/sympy/core/numbers.py', 'venv/lib/python3.13/site-packages/sympy/codegen/fnodes.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_nullspace.py', 'venv/lib/python3.13/site-packages/sympy/sets/handlers/__init__.py', 'venv/lib/python3.13/site-packages/sympy/simplify/sqrtdenest.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/sparse_ndim_array.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_qexpr.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/__init__.py', 'venv/lib/python3.13/site-packages/mpmath/ctx_mp_python.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_kind.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_fourier.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/type_b.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_sparsetools.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/systems/__init__.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/tests/test_lie_group.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/tests/test_unit_system_cgs_gauss.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_series.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/matpow.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_factorizations.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_prde.py', 'venv/lib/python3.13/site-packages/sympy/polys/puiseux.py', 'venv/lib/python3.13/site-packages/sympy/calculus/accumulationbounds.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/particle.py', 'venv/lib/python3.13/site-packages/sympy/concrete/gosper.py', 'venv/lib/python3.13/site-packages/sympy/polys/agca/tests/test_modules.py', 'venv/lib/python3.13/site-packages/sympy/physics/continuum_mechanics/tests/test_beam.py', 'venv/lib/python3.13/site-packages/sympy/core/benchmarks/bench_basic.py', 'venv/lib/python3.13/site-packages/sympy/simplify/ratsimp.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_ast.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_limitseq.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/grover.py', 'venv/lib/python3.13/site-packages/sympy/crypto/__init__.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/transpose.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/polynomials.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/relation/equality.py', 'venv/lib/python3.13/site-packages/sympy/parsing/latex/_parse_latex_antlr.py', 'venv/lib/python3.13/site-packages/sympy/solvers/deutils.py', 'venv/lib/python3.13/site-packages/sympy/stats/random_matrix.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/identitysearch.py', 'venv/lib/python3.13/site-packages/sympy/physics/continuum_mechanics/tests/test_arch.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_kane5.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/tests/test_arrayop.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/tests/test_printing.py', 'venv/lib/python3.13/site-packages/sympy/physics/biomechanics/curve.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/applyfunc.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_codegen_rust.py', 'venv/lib/python3.13/site-packages/mpmath/functions/zeta.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_piab.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_surface.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_rde.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/conv_indexed_to_array.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/sathandlers.py', 'venv/lib/python3.13/site-packages/sympy/parsing/latex/_antlr/latexparser.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_residue.py', 'venv/lib/python3.13/site-packages/sympy/functions/combinatorial/tests/__init__.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_schur_number.py', 'venv/lib/python3.13/site-packages/sympy/physics/tests/test_sho.py', 'venv/lib/python3.13/site-packages/mpmath/ctx_fp.py', 'venv/lib/python3.13/site-packages/sympy/polys/polyquinticconst.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/modular.py', 'venv/lib/python3.13/site-packages/sympy/utilities/lambdify.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_spec_polynomials.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_numpy.py', 'venv/lib/python3.13/site-packages/sympy/codegen/cnodes.py', 'venv/lib/python3.13/site-packages/sympy/integrals/transforms.py', 'venv/lib/python3.13/site-packages/sympy/multipledispatch/utils.py', 'venv/lib/python3.13/site-packages/sympy/testing/randtest.py', 'venv/lib/python3.13/site-packages/sympy/printing/rust.py', 'venv/lib/python3.13/site-packages/sympy/simplify/cse_main.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_lagrange.py', 'venv/lib/python3.13/site-packages/sympy/crypto/tests/test_crypto.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_eval.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_polytools.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/benchmarks/bench_exp.py', 'venv/lib/python3.13/site-packages/sympy/utilities/exceptions.py', 'venv/lib/python3.13/site-packages/flatbuffers/flexbuffers.py', 'venv/lib/python3.13/site-packages/sympy/geometry/parabola.py', 'venv/lib/python3.13/site-packages/sympy/concrete/expr_with_intlimits.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_monomials.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/cartesian.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_rules.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/printing.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_fortran_parser.py', 'venv/lib/python3.13/site-packages/sympy/physics/biomechanics/_mixin.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/predicates/calculus.py', 'venv/lib/python3.13/site-packages/sympy/parsing/latex/lark/transformer.py', 'venv/lib/python3.13/site-packages/sympy/physics/continuum_mechanics/tests/test_truss.py', 'venv/lib/python3.13/site-packages/sympy/plotting/plotgrid.py', 'venv/lib/python3.13/site-packages/sympy/utilities/iterables.py', 'venv/lib/python3.13/site-packages/sympy/__init__.py', 'venv/lib/python3.13/site-packages/mpmath/functions/__init__.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_grover.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_priority.py', 'venv/lib/python3.13/site-packages/sympy/algebras/quaternion.py', 'venv/lib/python3.13/site-packages/sympy/integrals/rde.py', 'venv/lib/python3.13/site-packages/mpmath/visualization.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_interval.py', 'venv/lib/python3.13/site-packages/mpmath/__init__.py', 'venv/lib/python3.13/site-packages/flatbuffers/encode.py', 'venv/lib/python3.13/site-packages/sympy/calculus/tests/test_euler.py', 'venv/lib/python3.13/site-packages/mpmath/functions/theta.py', 'venv/lib/python3.13/site-packages/sympy/polys/polymatrix.py', 'venv/lib/python3.13/site-packages/sympy/integrals/laplace.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_util.py', 'venv/lib/python3.13/site-packages/sympy/polys/densetools.py', 'venv/lib/python3.13/site-packages/sympy/release.py', 'venv/lib/python3.13/site-packages/sympy/printing/latex.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_ddm.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_meijerint.py', 'venv/lib/python3.13/site-packages/sympy/calculus/finite_diff.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_pauli.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/conv_matrix_to_array.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/qexpr.py', 'venv/lib/python3.13/site-packages/sympy/printing/str.py', 'venv/lib/python3.13/site-packages/sympy/testing/quality_unicode.py', 'venv/lib/python3.13/site-packages/sympy/plotting/tests/test_utils.py', 'venv/lib/python3.13/site-packages/sympy/parsing/c/__init__.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_dynkin_diagram.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/_typing.py', 'venv/lib/python3.13/site-packages/sympy/stats/joint_rv.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/galois_resolvents.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/tests/test_refine.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_fp.py', 'venv/lib/python3.13/site-packages/mpmath/tests/extratest_gamma.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest2.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_domainmatrix.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_puiseux.py', 'venv/lib/python3.13/site-packages/sympy/crypto/crypto.py', 'venv/lib/python3.13/site-packages/sympy/codegen/pynodes.py', 'venv/lib/python3.13/site-packages/sympy/galgebra.py', 'venv/lib/python3.13/site-packages/sympy/sets/ordinals.py', 'venv/lib/python3.13/site-packages/sympy/integrals/meijerint.py', 'venv/lib/python3.13/site-packages/sympy/core/add.py', 'venv/lib/python3.13/site-packages/sympy/integrals/manualintegrate.py', 'venv/lib/python3.13/site-packages/sympy/holonomic/recurrence.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_symbol.py', 'venv/lib/python3.13/site-packages/sympy/parsing/__init__.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tmpfiles.py', 'venv/lib/python3.13/site-packages/flatbuffers/number_types.py', 'venv/lib/python3.13/site-packages/sympy/core/random.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/utils.py', 'venv/lib/python3.13/site-packages/sympy/polys/densearith.py', 'venv/lib/python3.13/site-packages/sympy/plotting/textplot.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_permutations.py', 'venv/lib/python3.13/site-packages/sympy/logic/tests/test_inference.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/factor_.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/pathway.py', 'venv/lib/python3.13/site-packages/sympy/concrete/tests/test_guess.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/gaussopt.py', 'venv/lib/python3.13/site-packages/sympy/series/aseries.py', 'venv/lib/python3.13/site-packages/sympy/printing/maple.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/method.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_repmatrix.py', 'venv/lib/python3.13/site-packages/sympy/matrices/normalforms.py', 'venv/lib/python3.13/site-packages/sympy/stats/random_matrix_models.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_mathematica.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_quad.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_bessel.py', 'venv/lib/python3.13/site-packages/sympy/core/basic.py', 'venv/lib/python3.13/site-packages/sympy/sets/handlers/issubset.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_interactions.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_rust.py', 'venv/lib/python3.13/site-packages/sympy/geometry/tests/test_plane.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/tests/test_fieldfunctions.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/conv_array_to_indexed.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/functions.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_sorting.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_interval.py', 'venv/lib/python3.13/site-packages/mpmath/calculus/calculus.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/boson.py', 'venv/lib/python3.13/site-packages/sympy/core/kind.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/systems/si.py', 'venv/lib/python3.13/site-packages/sympy/codegen/pyutils.py', 'venv/lib/python3.13/site-packages/sympy/geometry/tests/test_point.py', 'venv/lib/python3.13/site-packages/sympy/geometry/tests/test_polygon.py', 'venv/lib/python3.13/site-packages/sympy/solvers/tests/test_constantsimp.py', 'venv/lib/python3.13/site-packages/sympy/codegen/rewriting.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_type_A.py', 'venv/lib/python3.13/site-packages/sympy/polys/polyutils.py', 'venv/lib/python3.13/site-packages/sympy/calculus/euler.py', 'venv/lib/python3.13/site-packages/sympy/physics/pring.py', 'venv/lib/python3.13/site-packages/sympy/solvers/__init__.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_dispersion.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/_build_autolev_antlr.py', 'venv/lib/python3.13/site-packages/sympy/tensor/tests/test_tensor_operators.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_singleton.py', 'venv/lib/python3.13/site-packages/sympy/tensor/tests/test_printing.py', 'venv/lib/python3.13/site-packages/mpmath/libmp/libmpc.py', 'venv/lib/python3.13/site-packages/sympy/testing/pytest.py', 'venv/lib/python3.13/site-packages/sympy/testing/runtests.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_elliptic.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/tests/test_wrapper.py', 'venv/lib/python3.13/site-packages/sympy/solvers/decompogen.py', 'venv/lib/python3.13/site-packages/sympy/multipledispatch/tests/test_core.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_julia.py', 'venv/lib/python3.13/site-packages/sympy/series/residues.py', 'venv/lib/python3.13/site-packages/sympy/tensor/__init__.py', 'venv/lib/python3.13/site-packages/sympy/logic/inference.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_applyfunc.py', 'venv/lib/python3.13/site-packages/sympy/diffgeom/rn.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/tests/test_plotting.py', 'venv/lib/python3.13/site-packages/sympy/discrete/__init__.py', 'venv/lib/python3.13/site-packages/sympy/concrete/products.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_polyfuncs.py', 'venv/lib/python3.13/site-packages/sympy/stats/error_prop.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_evalf.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/domainelement.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/tests/test_single.py', 'venv/lib/python3.13/site-packages/sympy/strategies/branch/tests/test_traverse.py', 'venv/lib/python3.13/site-packages/mpmath/libmp/gammazeta.py', 'venv/lib/python3.13/site-packages/sympy/integrals/heurisch.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_type_B.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/tests/test_minpoly.py', 'venv/lib/python3.13/site-packages/sympy/printing/numpy.py', 'venv/lib/python3.13/site-packages/sympy/solvers/polysys.py', 'venv/lib/python3.13/site-packages/mpmath/functions/bessel.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/polarization.py', 'venv/lib/python3.13/site-packages/sympy/utilities/__init__.py', 'venv/lib/python3.13/site-packages/mpmath/ctx_iv.py', 'venv/lib/python3.13/site-packages/sympy/printing/precedence.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/loads.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_companion.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/ddm.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_rationaltools.py', 'venv/lib/python3.13/site-packages/sympy/parsing/latex/_antlr/latexlexer.py', 'venv/lib/python3.13/site-packages/sympy/printing/jscode.py', 'venv/lib/python3.13/site-packages/sympy/diffgeom/tests/test_hyperbolic_space.py', 'venv/lib/python3.13/site-packages/mpmath/math2.py', 'venv/lib/python3.13/site-packages/sympy/physics/tests/test_clebsch_gordan.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_pythonrational.py', 'venv/lib/python3.13/site-packages/sympy/discrete/transforms.py', 'venv/lib/python3.13/site-packages/mpmath/libmp/libmpf.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_sympify.py', 'venv/lib/python3.13/site-packages/sympy/utilities/enumerative.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/tests/test_assumptions_2.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_root_system.py', 'venv/lib/python3.13/site-packages/sympy/matrices/solvers.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_sdm.py', 'venv/lib/python3.13/site-packages/sympy/physics/continuum_mechanics/cable.py', 'venv/lib/python3.13/site-packages/mpmath/matrices/calculus.py', 'venv/lib/python3.13/site-packages/sympy/matrices/matrices.py', 'venv/lib/python3.13/site-packages/sympy/core/alphabets.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/_antlr/autolevlistener.py', 'venv/lib/python3.13/site-packages/sympy/solvers/solveset.py', 'venv/lib/python3.13/site-packages/sympy/polys/multivariate_resultants.py', 'venv/lib/python3.13/site-packages/mpmath/functions/qfunctions.py', 'venv/lib/python3.13/site-packages/sympy/physics/matrices.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_curve.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/ring.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_sqrtdenest.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest11.py', 'venv/lib/python3.13/site-packages/sympy/strategies/util.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_lseries.py', 'venv/lib/python3.13/site-packages/sympy/physics/tests/test_pring.py', 'venv/lib/python3.13/site-packages/sympy/codegen/approximations.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/ask.py', 'venv/lib/python3.13/site-packages/sympy/codegen/__init__.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_args.py', 'venv/lib/python3.13/site-packages/mpmath/libmp/libmpi.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_matrix_nodes.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/point.py', 'venv/lib/python3.13/site-packages/sympy/parsing/latex/_build_latex_antlr.py', 'venv/lib/python3.13/site-packages/sympy/printing/tableform.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/tests/test_systems.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_diagonal.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_sets.py', 'venv/lib/python3.13/site-packages/mpmath/libmp/backend.py', 'venv/lib/python3.13/site-packages/sympy/concrete/tests/test_gosper.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/ecm.py', 'venv/lib/python3.13/site-packages/sympy/matrices/sparse.py', 'venv/lib/python3.13/site-packages/sympy/polys/agca/tests/test_extensions.py', 'venv/lib/python3.13/site-packages/sympy/matrices/decompositions.py', 'venv/lib/python3.13/site-packages/sympy/sets/tests/test_powerset.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_noncommutative.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/from_array_to_matrix.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/conv_array_to_matrix.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/tests/test_piecewise.py', 'venv/lib/python3.13/site-packages/sympy/stats/sampling/sample_numpy.py', 'venv/lib/python3.13/site-packages/sympy/printing/fortran.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/_antlr/autolevlexer.py', 'venv/lib/python3.13/site-packages/sympy/polys/benchmarks/bench_groebnertools.py', 'venv/lib/python3.13/site-packages/sympy/polys/polyfuncs.py', 'venv/lib/python3.13/site-packages/sympy/integrals/risch.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_eigen.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_codegen_julia.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_basic_ops.py', 'venv/lib/python3.13/site-packages/sympy/holonomic/__init__.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/body_base.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_transforms.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_circuitutils.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_rigidbody.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tensor_can.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/bbp_pi.py', 'venv/lib/python3.13/site-packages/sympy/geometry/entity.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_joint_rv.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/tests/test_dyadic.py', 'venv/lib/python3.13/site-packages/mpmath/libmp/libhyper.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/singularity_functions.py', 'venv/lib/python3.13/site-packages/sympy/core/sorting.py', 'venv/lib/python3.13/site-packages/sympy/integrals/integrals.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/dyadic.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_risch.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_polyoptions.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_mathml.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_exprtools.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/mutable_ndim_array.py', 'venv/lib/python3.13/site-packages/sympy/parsing/latex/lark/latex_parser.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_state.py', 'venv/lib/python3.13/site-packages/sympy/logic/utilities/__init__.py', 'venv/lib/python3.13/site-packages/sympy/polys/polyerrors.py', 'venv/lib/python3.13/site-packages/sympy/matrices/common.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_fu.py', 'venv/lib/python3.13/site-packages/sympy/solvers/tests/test_solvers.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_decompositions.py', 'venv/lib/python3.13/site-packages/sympy/physics/tests/test_hydrogen.py', 'venv/lib/python3.13/site-packages/sympy/matrices/graph.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_domains.py', 'venv/lib/python3.13/site-packages/sympy/testing/matrices.py', 'venv/lib/python3.13/site-packages/sympy/polys/agca/ideals.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_fortran.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_discrete_rv.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_inverse.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_order.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_autowrap.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/plot_mode.py', 'venv/lib/python3.13/site-packages/sympy/polys/polytools.py', 'venv/lib/python3.13/site-packages/sympy/interactive/__init__.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_theanocode.py', 'venv/lib/python3.13/site-packages/sympy/unify/__init__.py', 'venv/lib/python3.13/site-packages/sympy/printing/pretty/tests/test_pretty.py', 'venv/lib/python3.13/site-packages/sympy/parsing/latex/__init__.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_partitions.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/handlers/sets.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/nonhomogeneous.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/__init__.py', 'venv/lib/python3.13/site-packages/sympy/parsing/fortran/fortran_parser.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_appellseqs.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_testutil.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/field.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_fnodes.py', 'venv/lib/python3.13/site-packages/sympy/simplify/radsimp.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_kane3.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_division.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/commutator.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/fp_groups.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/systems/natural.py', 'venv/lib/python3.13/site-packages/sympy/benchmarks/bench_meijerint.py', 'venv/lib/python3.13/site-packages/sympy/codegen/ast.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_fflu.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_euclidtools.py', 'venv/lib/python3.13/site-packages/sympy/interactive/printing.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_quadrature.py', 'venv/lib/python3.13/site-packages/sympy/core/symbol.py', 'venv/lib/python3.13/site-packages/mpmath/functions/elliptic.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_body.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/tests/test_riccati.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_constructor_postprocessor.py', 'venv/lib/python3.13/site-packages/sympy/utilities/_compilation/tests/test_compilation.py', 'venv/lib/python3.13/site-packages/sympy/sandbox/tests/test_indexed_integrals.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/tests/test_sathandlers.py', 'venv/lib/python3.13/site-packages/sympy/physics/biomechanics/__init__.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/matmul.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/minpoly.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/error_functions.py', 'venv/lib/python3.13/site-packages/sympy/plotting/backends/textbackend/__init__.py', 'venv/lib/python3.13/site-packages/flatbuffers/compat.py', 'venv/lib/python3.13/site-packages/sympy/solvers/inequalities.py', 'venv/lib/python3.13/site-packages/sympy/integrals/benchmarks/bench_trigintegrate.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_system.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/innerproduct.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/tests/test_arrayexpr_derivatives.py', 'venv/lib/python3.13/site-packages/sympy/core/assumptions.py', 'venv/lib/python3.13/site-packages/sympy/polys/partfrac.py', 'venv/lib/python3.13/site-packages/sympy/physics/tests/test_paulialgebra.py', 'venv/lib/python3.13/site-packages/sympy/sets/tests/__init__.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_cnodes.py', 'venv/lib/python3.13/site-packages/sympy/tensor/tests/test_indexed.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_prufer.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/realfield.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/lra_satask.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/predicates/common.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_singularity_functions.py', 'venv/lib/python3.13/site-packages/sympy/core/exprtools.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/spherical_harmonics.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_weyl_group.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_ecm.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/miscellaneous.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_solvers.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_trigsimp.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_match.py', 'venv/lib/python3.13/site-packages/sympy/core/containers.py', 'venv/lib/python3.13/site-packages/sympy/series/order.py', 'venv/lib/python3.13/site-packages/sympy/polys/polyroots.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/tests/test_quantities.py', 'venv/lib/python3.13/site-packages/sympy/parsing/latex/_antlr/__init__.py', 'venv/lib/python3.13/site-packages/sympy/core/mul.py', 'venv/lib/python3.13/site-packages/sympy/utilities/misc.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/expressionrawdomain.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/free_groups.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_multinomial.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_applications.py', 'venv/lib/python3.13/site-packages/sympy/external/__init__.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_inverse.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_qs.py', 'venv/lib/python3.13/site-packages/sympy/printing/cxx.py', 'venv/lib/python3.13/site-packages/sympy/solvers/benchmarks/bench_solvers.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_pathway.py', 'venv/lib/python3.13/site-packages/sympy/printing/defaults.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/tests/test_array_expressions.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_singularityfunctions.py', 'venv/lib/python3.13/site-packages/sympy/core/intfunc.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/_shape.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_parameters.py', 'venv/lib/python3.13/site-packages/sympy/tensor/tests/test_index_methods.py', 'venv/lib/python3.13/site-packages/sympy/simplify/epathtools.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_rings.py', 'venv/lib/python3.13/site-packages/sympy/printing/dot.py', 'venv/lib/python3.13/site-packages/sympy/external/gmpy.py', 'venv/lib/python3.13/site-packages/sympy/discrete/recurrences.py', 'venv/lib/python3.13/site-packages/sympy/solvers/tests/test_polysys.py', 'venv/lib/python3.13/site-packages/sympy/physics/continuum_mechanics/arch.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/rigidbody.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_compatibility.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_jointsmethod.py', 'venv/lib/python3.13/site-packages/mpmath/functions/orthogonal.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_circuitplot.py', 'venv/lib/python3.13/site-packages/sympy/strategies/tests/test_traverse.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/predicates/ntheory.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_bitwise.py', 'venv/lib/python3.13/site-packages/sympy/solvers/simplex.py', 'venv/lib/python3.13/site-packages/sympy/series/series.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/actuator.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_precedence.py', 'venv/lib/python3.13/site-packages/sympy/physics/tests/test_qho_1d.py', 'venv/lib/python3.13/site-packages/sympy/sets/__init__.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/tests/test_elliptic_curve.py', 'venv/lib/python3.13/site-packages/sympy/physics/secondquant.py', 'venv/lib/python3.13/site-packages/sympy/diffgeom/diffgeom.py', 'venv/lib/python3.13/site-packages/sympy/external/tests/test_scipy.py', 'venv/lib/python3.13/site-packages/sympy/utilities/mathml/__init__.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_facts.py', 'venv/lib/python3.13/site-packages/sympy/tensor/tests/test_tensor_element.py', 'venv/lib/python3.13/site-packages/sympy/multipledispatch/dispatcher.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_rewriting.py', 'venv/lib/python3.13/site-packages/sympy/plotting/plot.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/relation/__init__.py', 'venv/lib/python3.13/site-packages/sympy/stats/sampling/tests/test_sample_finite_rv.py', 'venv/lib/python3.13/site-packages/flatbuffers/_version.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/tests/test_output.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/riccati.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/unitsystem.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_orderings.py', 'venv/lib/python3.13/site-packages/sympy/solvers/bivariate.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/qft.py', 'venv/lib/python3.13/site-packages/sympy/printing/python.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_expr.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/operator.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_qubit.py', 'venv/lib/python3.13/site-packages/sympy/tensor/tensor.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/tests/test_mutable_ndim_array.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/__init__.py', 'venv/lib/python3.13/site-packages/sympy/strategies/branch/traverse.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_multivariate_resultants.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_subsets.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/pauli.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/utilities.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/lll.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_sequences.py', 'venv/lib/python3.13/site-packages/isympy.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/tests/test_utils.py', 'venv/lib/python3.13/site-packages/sympy/tensor/tests/test_functions.py', 'venv/lib/python3.13/site-packages/sympy/multipledispatch/__init__.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_deltafunctions.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/circuitplot.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_operatorset.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/pythonintegerring.py', 'venv/lib/python3.13/site-packages/sympy/series/kauers.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_gruntz.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/frame.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest9.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/pydy-example-repo/double_pendulum.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_adjoint.py', 'venv/lib/python3.13/site-packages/sympy/concrete/expr_with_limits.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/tests/test_convert_array_to_matrix.py', 'venv/lib/python3.13/site-packages/sympy/plotting/tests/test_experimental_lambdify.py', 'venv/lib/python3.13/site-packages/sympy/series/approximants.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/__init__.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_latex_lark.py', 'venv/lib/python3.13/site-packages/sympy/functions/combinatorial/factorials.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_dense.py', 'venv/lib/python3.13/site-packages/sympy/stats/crv_types.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_matrices.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_delta_functions.py', 'venv/lib/python3.13/site-packages/sympy/utilities/runtests.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/definitions/unit_definitions.py', 'venv/lib/python3.13/site-packages/sympy/solvers/tests/test_numeric.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/weyl_group.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_stochastic_process.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_algorithms.py', 'venv/lib/python3.13/site-packages/sympy/functions/combinatorial/__init__.py', 'venv/lib/python3.13/site-packages/sympy/series/fourier.py', 'venv/lib/python3.13/site-packages/sympy/sandbox/indexed_integrals.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_c.py', 'venv/lib/python3.13/site-packages/sympy/matrices/exceptions.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_galois.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/sdm.py', 'venv/lib/python3.13/site-packages/sympy/physics/biomechanics/tests/test_activation.py', 'venv/lib/python3.13/site-packages/sympy/multipledispatch/core.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_compatibility.py', 'venv/lib/python3.13/site-packages/sympy/stats/drv.py', 'venv/lib/python3.13/site-packages/sympy/integrals/tests/test_intpoly.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_c_parser.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/__init__.py', 'venv/lib/python3.13/site-packages/sympy/tensor/toperators.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_kauers.py', 'venv/lib/python3.13/site-packages/sympy/plotting/plot_implicit.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_groebnertools.py', 'venv/lib/python3.13/site-packages/sympy/geometry/ellipse.py', 'venv/lib/python3.13/site-packages/sympy/polys/rationaltools.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/expressions/tests/test_deprecated_conv_modules.py', 'venv/lib/python3.13/site-packages/sympy/logic/tests/test_lra_theory.py', 'venv/lib/python3.13/site-packages/sympy/printing/pretty/pretty.py', 'venv/lib/python3.13/site-packages/sympy/multipledispatch/tests/test_conflict.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_equal.py', 'venv/lib/python3.13/site-packages/sympy/simplify/tests/test_simplify.py', 'venv/lib/python3.13/site-packages/mpmath/tests/__init__.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/__init__.py', 'venv/lib/python3.13/site-packages/sympy/sets/tests/test_conditionset.py', 'venv/lib/python3.13/site-packages/mpmath/functions/factorials.py', 'venv/lib/python3.13/site-packages/sympy/plotting/backends/base_backend.py', 'venv/lib/python3.13/site-packages/sympy/geometry/tests/test_parabola.py', 'venv/lib/python3.13/site-packages/sympy/interactive/session.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/rewritingsystem.py', 'venv/lib/python3.13/site-packages/sympy/sets/contains.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/lagrange.py', 'venv/lib/python3.13/site-packages/sympy/utilities/pkgdata.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/predicates/sets.py', 'venv/lib/python3.13/site-packages/sympy/physics/units/prefixes.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_rootfinding.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/blockmatrix.py', 'venv/lib/python3.13/site-packages/sympy/printing/pycode.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/dense_ndim_array.py', 'venv/lib/python3.13/site-packages/sympy/core/benchmarks/bench_arit.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/handlers/common.py', 'venv/lib/python3.13/site-packages/mpmath/calculus/quadrature.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/trace.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/test-examples/ruletest4.py', 'venv/lib/python3.13/site-packages/sympy/printing/pretty/pretty_symbology.py', 'venv/lib/python3.13/site-packages/sympy/solvers/diophantine/diophantine.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/linearize.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_hp.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_autolev.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/functions.py', 'venv/lib/python3.13/site-packages/sympy/series/tests/test_residues.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_fermion.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_normalforms.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_identitysearch.py', 'venv/lib/python3.13/site-packages/sympy/matrices/reductions.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/subfield.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_torch.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/tests/test_modules.py', 'venv/lib/python3.13/site-packages/mpmath/functions/functions.py', 'venv/lib/python3.13/site-packages/sympy/functions/combinatorial/tests/test_comb_numbers.py', 'venv/lib/python3.13/site-packages/flatbuffers/packer.py', 'venv/lib/python3.13/site-packages/sympy/polys/agca/homomorphisms.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/tests/test_free_groups.py', 'venv/lib/python3.13/site-packages/sympy/physics/tests/test_secondquant.py', 'venv/lib/python3.13/site-packages/sympy/polys/matrices/tests/test_lll.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/vector.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_commonmatrix.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/complexes.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_diff.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_matadd.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/body.py', 'venv/lib/python3.13/site-packages/sympy/printing/c.py', 'venv/lib/python3.13/site-packages/sympy/parsing/c/c_parser.py', 'venv/lib/python3.13/site-packages/sympy/core/singleton.py', 'venv/lib/python3.13/site-packages/sympy/stats/compound_rv.py', 'venv/lib/python3.13/site-packages/sympy/polys/polyclasses.py', 'venv/lib/python3.13/site-packages/sympy/tensor/array/ndim_array.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/wrapper.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/tests/test_medium.py', 'venv/lib/python3.13/site-packages/sympy/polys/agca/modules.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/cg.py', 'venv/lib/python3.13/site-packages/sympy/strategies/branch/tools.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/integers.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_functions.py', 'venv/lib/python3.13/site-packages/sympy/polys/heuristicgcd.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/graycode.py', 'venv/lib/python3.13/site-packages/sympy/printing/tests/test_tableform.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_special.py', 'venv/lib/python3.13/site-packages/sympy/matrices/tests/test_eigen.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_matrixutils.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_densebasic.py', 'venv/lib/python3.13/site-packages/mpmath/libmp/libintmath.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_hyper.py', 'venv/lib/python3.13/site-packages/sympy/parsing/latex/errors.py', 'venv/lib/python3.13/site-packages/sympy/physics/control/tests/test_lti.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/schur_number.py', 'venv/lib/python3.13/site-packages/sympy/parsing/tests/test_custom_latex.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/modules.py', 'venv/lib/python3.13/site-packages/sympy/utilities/_compilation/compilation.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/predicates/order.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/factorizations.py', 'venv/lib/python3.13/site-packages/sympy/printing/smtlib.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_dagger.py', 'venv/lib/python3.13/site-packages/sympy/core/cache.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/tests/test_boson.py', 'venv/lib/python3.13/site-packages/sympy/polys/numberfields/tests/test_utilities.py', 'venv/lib/python3.13/site-packages/sympy/stats/frv_types.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/cartan_type.py', 'venv/lib/python3.13/site-packages/sympy/calculus/tests/test_singularities.py', 'venv/lib/python3.13/site-packages/sympy/core/rules.py', 'venv/lib/python3.13/site-packages/sympy/solvers/ode/single.py', 'venv/lib/python3.13/site-packages/sympy/printing/pretty/stringpict.py', 'venv/lib/python3.13/site-packages/sympy/matrices/expressions/tests/test_kronecker.py', 'venv/lib/python3.13/site-packages/sympy/geometry/exceptions.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_traversal.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/galois.py', 'venv/lib/python3.13/site-packages/sympy/codegen/tests/test_pynodes.py', 'venv/lib/python3.13/site-packages/sympy/polys/dispersion.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/wrapping_geometry.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/assume.py', 'venv/lib/python3.13/site-packages/sympy/plotting/intervalmath/tests/test_intervalmath.py', 'venv/lib/python3.13/site-packages/sympy/core/sympify.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/pythonrationalfield.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/qubit.py', 'venv/lib/python3.13/site-packages/sympy/series/limits.py', 'venv/lib/python3.13/site-packages/sympy/sets/powerset.py', 'venv/lib/python3.13/site-packages/sympy/physics/optics/tests/__init__.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/hyper.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_polyclasses.py', 'venv/lib/python3.13/site-packages/sympy/matrices/sparsetools.py', 'venv/lib/python3.13/site-packages/mpmath/calculus/__init__.py', 'venv/lib/python3.13/site-packages/sympy/polys/agca/extensions.py', 'venv/lib/python3.13/site-packages/mpmath/tests/test_identify.py', 'venv/lib/python3.13/site-packages/sympy/sets/handlers/power.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/tests/test_kane4.py', 'venv/lib/python3.13/site-packages/sympy/solvers/pde.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/kane.py', 'venv/lib/python3.13/site-packages/sympy/assumptions/cnf.py', 'venv/lib/python3.13/site-packages/sympy/sets/handlers/comparison.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/type_c.py', 'venv/lib/python3.13/site-packages/sympy/holonomic/holonomicerrors.py', 'venv/lib/python3.13/site-packages/mpmath/matrices/matrices.py', 'venv/lib/python3.13/site-packages/sympy/printing/julia.py', 'venv/lib/python3.13/site-packages/sympy/parsing/autolev/_parse_autolev_antlr.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/__init__.py', 'venv/lib/python3.13/site-packages/sympy/solvers/diophantine/__init__.py', 'venv/lib/python3.13/site-packages/sympy/physics/quantum/matrixutils.py', 'venv/lib/python3.13/site-packages/sympy/matrices/subspaces.py', 'venv/lib/python3.13/site-packages/sympy/external/tests/test_gmpy.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_beta_functions.py', 'venv/lib/python3.13/site-packages/sympy/physics/vector/fieldfunctions.py', 'venv/lib/python3.13/site-packages/sympy/sets/handlers/add.py', 'venv/lib/python3.13/site-packages/sympy/parsing/ast_parser.py', 'venv/lib/python3.13/site-packages/sympy/printing/mathematica.py', 'venv/lib/python3.13/site-packages/sympy/stats/tests/test_symbolic_multivariate.py', 'venv/lib/python3.13/site-packages/sympy/physics/control/__init__.py', 'venv/lib/python3.13/site-packages/sympy/combinatorics/generators.py', 'venv/lib/python3.13/site-packages/sympy/core/multidimensional.py', 'venv/lib/python3.13/site-packages/sympy/categories/diagram_drawing.py', 'venv/lib/python3.13/site-packages/sympy/physics/continuum_mechanics/beam.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/modularinteger.py', 'venv/lib/python3.13/site-packages/sympy/geometry/point.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_cartan_type.py', 'venv/lib/python3.13/site-packages/sympy/utilities/magic.py', 'venv/lib/python3.13/site-packages/sympy/functions/elementary/exponential.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/characteristiczero.py', 'venv/lib/python3.13/site-packages/sympy/polys/tests/test_rootoftools.py', 'venv/lib/python3.13/site-packages/sympy/codegen/scipy_nodes.py', 'venv/lib/python3.13/site-packages/sympy/calculus/tests/test_accumulationbounds.py', 'venv/lib/python3.13/site-packages/sympy/geometry/polygon.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/pythonrational.py', 'venv/lib/python3.13/site-packages/sympy/plotting/pygletplot/color_scheme.py', 'venv/lib/python3.13/site-packages/sympy/polys/domains/complexfield.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/type_d.py', 'venv/lib/python3.13/site-packages/sympy/simplify/cse_opts.py', 'venv/lib/python3.13/site-packages/sympy/physics/mechanics/joint.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_random.py', 'venv/lib/python3.13/site-packages/sympy/printing/gtk.py', 'venv/lib/python3.13/site-packages/mpmath/matrices/linalg.py', 'venv/lib/python3.13/site-packages/sympy/functions/special/tests/test_elliptic_integrals.py', 'venv/lib/python3.13/site-packages/sympy/core/tests/test_assumptions.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_type_F.py', 'venv/lib/python3.13/site-packages/sympy/liealgebras/tests/test_type_D.py', 'venv/lib/python3.13/site-packages/sympy/ntheory/egyptian_fraction.py'. Reloading...
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_timeutils.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_mathml.py', 'venv/lib/python3.13/site-packages/sympy/vector/tests/test_functions.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_exceptions.py', 'venv/lib/python3.13/site-packages/sympy/vector/point.py', 'venv/lib/python3.13/site-packages/sympy/vector/kind.py', 'venv/lib/python3.13/site-packages/sympy/vector/operators.py', 'venv/lib/python3.13/site-packages/sympy/vector/parametricregion.py', 'venv/lib/python3.13/site-packages/sympy/vector/orienters.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_deprecated.py', 'venv/lib/python3.13/site-packages/sympy/vector/tests/test_printing.py', 'venv/lib/python3.13/site-packages/sympy/vector/tests/test_implicitregion.py', 'venv/lib/python3.13/site-packages/sympy/vector/tests/test_operators.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_decorator.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_lambdify.py', 'venv/lib/python3.13/site-packages/sympy/vector/vector.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_misc.py', 'venv/lib/python3.13/site-packages/sympy/vector/tests/test_parametricregion.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_iterables.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_xxe.py', 'venv/lib/python3.13/site-packages/sympy/vector/tests/test_integrals.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_wester.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_matchpy_connector.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_enumerative.py', 'venv/lib/python3.13/site-packages/sympy/vector/scalar.py', 'venv/lib/python3.13/site-packages/sympy/vector/tests/test_vector.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_pickling.py', 'venv/lib/python3.13/site-packages/sympy/utilities/tests/test_source.py'. Reloading...
Process SpawnProcess-8:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 9, in <module>
    from faster_whisper import WhisperModel
ModuleNotFoundError: No module named 'faster_whisper'
Process SpawnProcess-9:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 9, in <module>
    from faster_whisper import WhisperModel
ModuleNotFoundError: No module named 'faster_whisper'
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_memo.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/versionpredicate.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_bdist_rpm.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/unixccompiler.py', 'venv/lib/python3.13/site-packages/setuptools/monkey.py', 'venv/lib/python3.13/site-packages/setuptools/_shutil.py', 'venv/lib/python3.13/site-packages/setuptools/tests/config/downloads/preload.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/errors.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/build_ext.py', 'venv/lib/python3.13/site-packages/setuptools/tests/integration/test_pip_install_sdist.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/api.py', 'venv/lib/python3.13/site-packages/setuptools/_discovery.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/metadata.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/install_data.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/unix.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_virtualenv.py', 'venv/lib/python3.13/site-packages/setuptools/command/bdist_egg.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/backports/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/command/saveopts.py', 'venv/lib/python3.13/site-packages/setuptools/tests/integration/test_pbr.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/windows.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_re.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_bdist_dumb.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_depends.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_compat.py', 'venv/lib/python3.13/site-packages/setuptools/_importlib.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_clib.py', 'venv/lib/python3.13/site-packages/pkg_resources/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/compat/py310.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_functions.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_meta.py', 'venv/lib/python3.13/site-packages/setuptools/tests/text.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py', 'venv/lib/python3.13/site-packages/setuptools/tests/namespaces.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/install_headers.py', 'venv/lib/python3.13/site-packages/setuptools/command/dist_info.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_util.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_msvc.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_musllinux.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_warnings.py', 'venv/lib/python3.13/site-packages/setuptools/compat/py39.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/base.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/tests/config/test_pyprojecttoml_dynamic_deps.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/dep_util.py', 'venv/lib/python3.13/site-packages/setuptools/installer.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_sdist.py', 'venv/lib/python3.13/site-packages/setuptools/config/pyprojecttoml.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_scripts.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_parser.py', 'venv/lib/python3.13/site-packages/setuptools/depends.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_manifest.py', 'venv/lib/python3.13/site-packages/setuptools/_core_metadata.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/autocommand/autocommand.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py', 'venv/lib/python3.13/site-packages/setuptools/compat/py311.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/install_scripts.py', 'venv/lib/python3.13/site-packages/setuptools/tests/fixtures.py', 'venv/lib/python3.13/site-packages/pkg_resources/tests/test_resources.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_build.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/compat/py39.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_log.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_dir_util.py', 'venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_bdist_wheel.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_types.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_importhook.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/compat/py310.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/clean.py', 'venv/lib/python3.13/site-packages/setuptools/launch.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/_framework_compat.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/debug.py', 'venv/lib/python3.13/site-packages/setuptools/warnings.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/unix_compat.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_build_clib.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_elffile.py', 'venv/lib/python3.13/site-packages/setuptools/command/test.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/to-dvorak.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_namespaces.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_dist.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/autocommand/errors.py', 'venv/lib/python3.13/site-packages/pkg_resources/tests/test_markers.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_decorators.py', 'venv/lib/python3.13/site-packages/setuptools/command/_requirestxt.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/utils.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_transformer.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_data.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/dir_util.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/build.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/autocommand/autoasync.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_bdist_deprecations.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_functools.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/unpack.py', 'venv/lib/python3.13/site-packages/setuptools/config/_apply_pyprojecttoml.py', 'venv/lib/python3.13/site-packages/setuptools/version.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_dist.py', 'venv/lib/python3.13/site-packages/setuptools/modified.py', 'venv/lib/python3.13/site-packages/setuptools/command/build.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/version.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_bdist.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_core.py', 'venv/lib/python3.13/site-packages/setuptools/command/setopt.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_archive_util.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_tokenizer.py', 'venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_windows_wrappers.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py', 'venv/lib/python3.13/site-packages/setuptools/_itertools.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_shutil_wrapper.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_clean.py', 'venv/lib/python3.13/site-packages/setuptools/tests/environment.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_develop.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py', 'venv/lib/python3.13/site-packages/setuptools/command/alias.py', 'venv/lib/python3.13/site-packages/setuptools/command/sdist.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/install_egg_info.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py', 'venv/lib/python3.13/site-packages/setuptools/command/install_scripts.py', 'venv/lib/python3.13/site-packages/setuptools/tests/mod_with_constant.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_scripts.py', 'venv/lib/python3.13/site-packages/setuptools/namespaces.py', 'venv/lib/python3.13/site-packages/setuptools/command/build_py.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/zosccompiler.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/inflect/compat/py38.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/autocommand/automain.py', 'venv/lib/python3.13/site-packages/setuptools/tests/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/dist.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_build_ext.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/filelist.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_config_cmd.py', 'venv/lib/python3.13/site-packages/setuptools/_scripts.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_versionpredicate.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/convert.py', 'venv/lib/python3.13/site-packages/setuptools/tests/textwrap.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/macosx_libfile.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_union_transformer.py', 'venv/lib/python3.13/site-packages/setuptools/build_meta.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_pytest_plugin.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_editable_install.py', 'venv/lib/python3.13/site-packages/pkg_resources/tests/data/my-test-package-source/setup.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/build_clib.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/strip-prefix.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/wheelfile.py', 'venv/lib/python3.13/site-packages/setuptools/command/install_egg_info.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/show-newlines.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/config.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/requirements.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/util.py', 'venv/lib/python3.13/site-packages/setuptools/tests/contexts.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_install_scripts.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/cmd.py', 'venv/lib/python3.13/site-packages/_distutils_hack/override.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/markers.py', 'venv/lib/python3.13/site-packages/setuptools/_static.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py', 'venv/lib/python3.13/site-packages/setuptools/tests/config/downloads/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_egg_info.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/unix.py', 'venv/lib/python3.13/site-packages/setuptools/config/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_reqs.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/errors.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/collections/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/tests/config/test_expand.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/version.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_scripts.py', 'venv/lib/python3.13/site-packages/setuptools/config/expand.py', 'venv/lib/python3.13/site-packages/setuptools/command/rotate.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/build_scripts.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/extension.py', 'venv/lib/python3.13/site-packages/setuptools/_path.py', 'venv/lib/python3.13/site-packages/setuptools/tests/config/test_apply_pyprojecttoml.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/check.py', 'venv/lib/python3.13/site-packages/pkg_resources/tests/test_integration_zope_interface.py', 'venv/lib/python3.13/site-packages/setuptools/tests/compat/py39.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/fancy_getopt.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/tomli/_parser.py', 'venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/extra_validations.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_modified.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_setopt.py', 'venv/lib/python3.13/site-packages/setuptools/windows_support.py', 'venv/lib/python3.13/site-packages/setuptools/config/setupcfg.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typing_extensions.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/macos.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/msvc.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_structures.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_dist_info.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/archive_util.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/version.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_utils.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/bdist_dumb.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/sdist.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/diagnose.py', 'venv/lib/python3.13/site-packages/setuptools/logging.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_suppression.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_text_file.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_spawn.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/to-qwerty.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_build.py', 'venv/lib/python3.13/site-packages/setuptools/command/install_lib.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_checkers.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_wheel.py', 'venv/lib/python3.13/site-packages/setuptools/command/bdist_wheel.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_build_py.py', 'venv/lib/python3.13/site-packages/setuptools/command/editable_wheel.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_collections.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/inflect/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_extern.py', 'venv/lib/python3.13/site-packages/setuptools/archive_util.py', 'venv/lib/python3.13/site-packages/setuptools/discovery.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_headers.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_core_metadata.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/support.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/bdist.py', 'venv/lib/python3.13/site-packages/setuptools/glob.py', 'venv/lib/python3.13/site-packages/setuptools/compat/py312.py', 'venv/lib/python3.13/site-packages/setuptools/_entry_points.py', 'venv/lib/python3.13/site-packages/setuptools/unicode_utils.py', 'venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_logging.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/licenses/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/cygwinccompiler.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_config.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_sysconfig.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_file_util.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/ccompiler.py', 'venv/lib/python3.13/site-packages/pkg_resources/tests/test_pkg_resources.py', 'venv/lib/python3.13/site-packages/setuptools/command/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/_manylinux.py', 'venv/lib/python3.13/site-packages/setuptools/command/build_ext.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_unix.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/bdist_rpm.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/_text.py', 'venv/lib/python3.13/site-packages/setuptools/_normalization.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_install.py', 'venv/lib/python3.13/site-packages/setuptools/tests/integration/helpers.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/autocommand/autoparse.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_build_meta.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_check.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_find_packages.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_archive_util.py', 'venv/lib/python3.13/site-packages/setuptools/command/develop.py', 'venv/lib/python3.13/site-packages/setuptools/extension.py', 'venv/lib/python3.13/site-packages/setuptools/command/egg_info.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_unicode_utils.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/metadata.py', 'venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/error_reporting.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/zos.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/pack.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/_msvccompiler.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_version.py', 'venv/lib/python3.13/site-packages/setuptools/tests/config/test_setupcfg.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_setuptools.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/core.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/sysconfig.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/log.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_glob.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compat/py39.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/text_file.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/util.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_find_py_modules.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/typeguard/_exceptions.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/importlib_metadata/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_distutils_adoption.py', 'venv/lib/python3.13/site-packages/setuptools/command/easy_install.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_extension.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/wheel/cli/tags.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_py.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_mingw.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/install_lib.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/more.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/install.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/context.py', 'venv/lib/python3.13/site-packages/setuptools/command/install.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_sdist.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/text/layouts.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/jaraco/functools/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/msvc.py', 'venv/lib/python3.13/site-packages/setuptools/errors.py', 'venv/lib/python3.13/site-packages/setuptools/wheel.py', 'venv/lib/python3.13/site-packages/setuptools/__init__.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/command/build_py.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/tags.py', 'venv/lib/python3.13/site-packages/setuptools/command/build_clib.py', 'venv/lib/python3.13/site-packages/pkg_resources/tests/test_find_distributions.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/more_itertools/recipes.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/specifiers.py', 'venv/lib/python3.13/site-packages/setuptools/tests/script-with-bom.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/tests/test_cygwin.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/zipp/glob.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compat/numpy.py', 'venv/lib/python3.13/site-packages/setuptools/tests/config/test_pyprojecttoml.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_cmd.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/backports/tarfile/__main__.py', 'venv/lib/python3.13/site-packages/setuptools/_imp.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/file_util.py', 'venv/lib/python3.13/site-packages/setuptools/command/bdist_rpm.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_build_ext.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/packaging/licenses/_spdx.py', 'venv/lib/python3.13/site-packages/setuptools/config/_validate_pyproject/formats.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_install_lib.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/spawn.py', 'venv/lib/python3.13/site-packages/setuptools/dist.py', 'venv/lib/python3.13/site-packages/pkg_resources/tests/test_working_set.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/tests/test_filelist.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_bdist_egg.py', 'venv/lib/python3.13/site-packages/setuptools/_distutils/compilers/C/cygwin.py', 'venv/lib/python3.13/site-packages/setuptools/tests/test_config_discovery.py', 'venv/lib/python3.13/site-packages/setuptools/_vendor/platformdirs/android.py'. Reloading...
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/numpy/dtypes.py', 'venv/lib/python3.13/site-packages/numpy/_distributor_init.py', 'venv/lib/python3.13/site-packages/packaging/tags.py', 'venv/lib/python3.13/site-packages/packaging/_elffile.py', 'venv/lib/python3.13/site-packages/packaging/_musllinux.py', 'venv/lib/python3.13/site-packages/packaging/metadata.py', 'venv/lib/python3.13/site-packages/numpy/_globals.py', 'venv/lib/python3.13/site-packages/numpy/_array_api_info.py', 'venv/lib/python3.13/site-packages/numpy/version.py', 'venv/lib/python3.13/site-packages/packaging/_parser.py', 'venv/lib/python3.13/site-packages/numpy/_core/_methods.py', 'venv/lib/python3.13/site-packages/packaging/licenses/_spdx.py', 'venv/lib/python3.13/site-packages/numpy/_core/_dtype.py', 'venv/lib/python3.13/site-packages/packaging/_tokenizer.py', 'venv/lib/python3.13/site-packages/numpy/__init__.py', 'venv/lib/python3.13/site-packages/packaging/specifiers.py', 'venv/lib/python3.13/site-packages/numpy/exceptions.py', 'venv/lib/python3.13/site-packages/numpy/_core/_add_newdocs.py', 'venv/lib/python3.13/site-packages/packaging/__init__.py', 'venv/lib/python3.13/site-packages/packaging/version.py', 'venv/lib/python3.13/site-packages/packaging/_manylinux.py', 'venv/lib/python3.13/site-packages/packaging/requirements.py', 'venv/lib/python3.13/site-packages/numpy/_core/_machar.py', 'venv/lib/python3.13/site-packages/packaging/utils.py', 'venv/lib/python3.13/site-packages/numpy/_core/_add_newdocs_scalars.py', 'venv/lib/python3.13/site-packages/numpy/conftest.py', 'venv/lib/python3.13/site-packages/packaging/markers.py', 'venv/lib/python3.13/site-packages/numpy/_core/_exceptions.py', 'venv/lib/python3.13/site-packages/numpy/_core/_dtype_ctypes.py', 'venv/lib/python3.13/site-packages/numpy/_core/_internal.py', 'venv/lib/python3.13/site-packages/numpy/_expired_attrs_2_0.py', 'venv/lib/python3.13/site-packages/numpy/_pytesttester.py', 'venv/lib/python3.13/site-packages/numpy/matlib.py', 'venv/lib/python3.13/site-packages/numpy/_core/_asarray.py', 'venv/lib/python3.13/site-packages/numpy/_configtool.py', 'venv/lib/python3.13/site-packages/packaging/_structures.py'. Reloading...
Process SpawnProcess-10:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 9, in <module>
    from faster_whisper import WhisperModel
ModuleNotFoundError: No module named 'faster_whisper'
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/numpy/matrixlib/tests/test_masked_matrix.py', 'venv/lib/python3.13/site-packages/numpy/lib/_type_check_impl.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_ufunc.py', 'venv/lib/python3.13/site-packages/numpy/_core/records.py', 'venv/lib/python3.13/site-packages/numpy/testing/_private/extbuild.py', 'venv/lib/python3.13/site-packages/numpy/ma/tests/test_core.py', 'venv/lib/python3.13/site-packages/numpy/lib/_stride_tricks_impl.py', 'venv/lib/python3.13/site-packages/numpy/_core/_type_aliases.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_numeric.py', 'venv/lib/python3.13/site-packages/numpy/f2py/f90mod_rules.py', 'venv/lib/python3.13/site-packages/numpy/random/_examples/cffi/parse.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/arrayterator.py', 'venv/lib/python3.13/site-packages/numpy/lib/_iotools.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_parameter.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_polynomial.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/numeric.py', 'venv/lib/python3.13/site-packages/numpy/testing/overrides.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/warnings_and_errors.py', 'venv/lib/python3.13/site-packages/numpy/linalg/__init__.py', 'venv/lib/python3.13/site-packages/numpy/ma/tests/test_old_ma.py', 'venv/lib/python3.13/site-packages/numpy/matrixlib/tests/test_numeric.py', 'venv/lib/python3.13/site-packages/numpy/core/records.py', 'venv/lib/python3.13/site-packages/numpy/ma/extras.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/fromnumeric.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_overrides.py', 'venv/lib/python3.13/site-packages/numpy/testing/tests/test_utils.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/ufuncs.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_scalars.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_format.py', 'venv/lib/python3.13/site-packages/numpy/ctypeslib/_ctypeslib.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_umath_accuracy.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_errstate.py', 'venv/lib/python3.13/site-packages/numpy/random/tests/test_regression.py', 'venv/lib/python3.13/site-packages/numpy/ma/__init__.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/numerictypes.py', 'venv/lib/python3.13/site-packages/numpy/ctypeslib/__init__.py', 'venv/lib/python3.13/site-packages/numpy/_core/einsumfunc.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_cython.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_nbit.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/tests/test_polynomial.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_histograms.py', 'venv/lib/python3.13/site-packages/numpy/f2py/func2subr.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_recfunctions.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/bitwise_ops.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_regression.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_unicode.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_kind.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_crackfortran.py', 'venv/lib/python3.13/site-packages/numpy/testing/_private/utils.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_type_check.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_pyf_src.py', 'venv/lib/python3.13/site-packages/numpy/char/__init__.py', 'venv/lib/python3.13/site-packages/numpy/core/numerictypes.py', 'venv/lib/python3.13/site-packages/numpy/core/defchararray.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_dlpack.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/tests/test_legendre.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_simd_module.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/tests/test_laguerre.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_return_complex.py', 'venv/lib/python3.13/site-packages/numpy/lib/_arraypad_impl.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/tests/test_hermite_e.py', 'venv/lib/python3.13/site-packages/numpy/random/tests/test_seed_sequence.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_getlimits.py', 'venv/lib/python3.13/site-packages/numpy/core/umath.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_function_base.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_abc.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/test_typing.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_regression.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_array_utils.py', 'venv/lib/python3.13/site-packages/numpy/tests/test_ctypeslib.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/hermite.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_symbolic.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_character.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_numerictypes.py', 'venv/lib/python3.13/site-packages/numpy/tests/test_numpy_config.py', 'venv/lib/python3.13/site-packages/numpy/fft/_helper.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_stride_tricks.py', 'venv/lib/python3.13/site-packages/numpy/lib/format.py', 'venv/lib/python3.13/site-packages/numpy/core/overrides.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/util.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/examples/limited_api/setup.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/nditer.py', 'venv/lib/python3.13/site-packages/numpy/lib/_function_base_impl.py', 'venv/lib/python3.13/site-packages/numpy/_utils/__init__.py', 'venv/lib/python3.13/site-packages/numpy/lib/_utils_impl.py', 'venv/lib/python3.13/site-packages/numpy/_core/arrayprint.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_limited_api.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test__iotools.py', 'venv/lib/python3.13/site-packages/numpy/_core/strings.py', 'venv/lib/python3.13/site-packages/numpy/tests/test_matlib.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_array_from_pyobj.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_arraymethod.py', 'venv/lib/python3.13/site-packages/numpy/f2py/symbolic.py', 'venv/lib/python3.13/site-packages/numpy/lib/_format_impl.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/array_constructors.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_indexerrors.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_einsum.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_scalarmath.py', 'venv/lib/python3.13/site-packages/numpy/tests/test__all__.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_common.py', 'venv/lib/python3.13/site-packages/numpy/f2py/rules.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_mem_overlap.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_io.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_indexing.py', 'venv/lib/python3.13/site-packages/numpy/lib/_arraysetops_impl.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_scalar_ctors.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_nditer.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_mem_policy.py', 'venv/lib/python3.13/site-packages/numpy/matrixlib/__init__.py', 'venv/lib/python3.13/site-packages/numpy/_core/numeric.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_longdouble.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_f2cmap.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/__init__.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_casting_floatingpoint_errors.py', 'venv/lib/python3.13/site-packages/numpy/lib/_ufunclike_impl.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/__init__.py', 'venv/lib/python3.13/site-packages/numpy/f2py/__version__.py', 'venv/lib/python3.13/site-packages/numpy/ma/tests/test_regression.py', 'venv/lib/python3.13/site-packages/numpy/core/einsumfunc.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_value_attrspec.py', 'venv/lib/python3.13/site-packages/numpy/f2py/use_rules.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_machar.py', 'venv/lib/python3.13/site-packages/numpy/core/_dtype.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_item_selection.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_umath_complex.py', 'venv/lib/python3.13/site-packages/numpy/ma/tests/test_extras.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_shape_base.py', 'venv/lib/python3.13/site-packages/numpy/core/getlimits.py', 'venv/lib/python3.13/site-packages/numpy/lib/_scimath_impl.py', 'venv/lib/python3.13/site-packages/numpy/tests/test_lazyloading.py', 'venv/lib/python3.13/site-packages/numpy/ma/core.py', 'venv/lib/python3.13/site-packages/numpy/lib/_arrayterator_impl.py', 'venv/lib/python3.13/site-packages/numpy/lib/mixins.py', 'venv/lib/python3.13/site-packages/numpy/lib/_datasource.py', 'venv/lib/python3.13/site-packages/numpy/_utils/_pep440.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/einsumfunc.py', 'venv/lib/python3.13/site-packages/numpy/lib/_array_utils_impl.py', 'venv/lib/python3.13/site-packages/numpy/_core/_string_helpers.py', 'venv/lib/python3.13/site-packages/numpy/ma/tests/test_arrayobject.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_mixed.py', 'venv/lib/python3.13/site-packages/numpy/matrixlib/tests/test_interaction.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/index_tricks.py', 'venv/lib/python3.13/site-packages/numpy/_core/fromnumeric.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_dtype_like.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_arrayprint.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_arraysetops.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/random.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_array_interface.py', 'venv/lib/python3.13/site-packages/numpy/f2py/cfuncs.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test__datasource.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_return_integer.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/ufunclike.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/examples/cython/setup.py', 'venv/lib/python3.13/site-packages/numpy/f2py/__main__.py', 'venv/lib/python3.13/site-packages/numpy/lib/_index_tricks_impl.py', 'venv/lib/python3.13/site-packages/numpy/_core/numerictypes.py', 'venv/lib/python3.13/site-packages/numpy/lib/array_utils.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_return_character.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_utils.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_cpu_dispatcher.py', 'venv/lib/python3.13/site-packages/numpy/f2py/crackfortran.py', 'venv/lib/python3.13/site-packages/numpy/tests/test_configtool.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_scalarbuffer.py', 'venv/lib/python3.13/site-packages/numpy/f2py/_backends/_backend.py', 'venv/lib/python3.13/site-packages/numpy/matrixlib/defmatrix.py', 'venv/lib/python3.13/site-packages/numpy/core/arrayprint.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_extint128.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_char_codes.py', 'venv/lib/python3.13/site-packages/numpy/lib/_twodim_base_impl.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_return_logical.py', 'venv/lib/python3.13/site-packages/numpy/tests/test_warnings.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_multithreading.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_index_tricks.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_packbits.py', 'venv/lib/python3.13/site-packages/numpy/lib/_version.py', 'venv/lib/python3.13/site-packages/numpy/core/_internal.py', 'venv/lib/python3.13/site-packages/numpy/lib/_user_array_impl.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_nbit_base.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_scalarinherit.py', 'venv/lib/python3.13/site-packages/numpy/_core/getlimits.py', 'venv/lib/python3.13/site-packages/numpy/f2py/common_rules.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/comparisons.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_scalar_methods.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_add_docstring.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_twodim_base.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_defchararray.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_arrayobject.py', 'venv/lib/python3.13/site-packages/numpy/lib/__init__.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/polyutils.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_return_real.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/hermite_e.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_datetime.py', 'venv/lib/python3.13/site-packages/numpy/f2py/_src_pyf.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_loadtxt.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/tests/test_classes.py', 'venv/lib/python3.13/site-packages/numpy/lib/_polynomial_impl.py', 'venv/lib/python3.13/site-packages/numpy/lib/_nanfunctions_impl.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/test_runtime.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/scalars.py', 'venv/lib/python3.13/site-packages/numpy/lib/stride_tricks.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_isoc.py', 'venv/lib/python3.13/site-packages/numpy/random/_examples/numba/extending.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_cpu_features.py', 'venv/lib/python3.13/site-packages/numpy/typing/__init__.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/mod.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/polynomial.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_ufunc.py', 'venv/lib/python3.13/site-packages/numpy/fft/tests/test_helper.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/ndarray_shape_manipulation.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_f2py2e.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/laguerre.py', 'venv/lib/python3.13/site-packages/numpy/core/_dtype_ctypes.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_multiarray.py', 'venv/lib/python3.13/site-packages/numpy/core/shape_base.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_docs.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/_polybase.py', 'venv/lib/python3.13/site-packages/numpy/strings/__init__.py', 'venv/lib/python3.13/site-packages/numpy/_core/printoptions.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_print.py', 'venv/lib/python3.13/site-packages/numpy/tests/test_public_api.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/arrayprint.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_quoted_character.py', 'venv/lib/python3.13/site-packages/numpy/core/fromnumeric.py', 'venv/lib/python3.13/site-packages/numpy/ma/testutils.py', 'venv/lib/python3.13/site-packages/numpy/random/_pickle.py', 'venv/lib/python3.13/site-packages/numpy/random/tests/test_generator_mt19937_regressions.py', 'venv/lib/python3.13/site-packages/numpy/lib/scimath.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_shape.py', 'venv/lib/python3.13/site-packages/numpy/random/tests/test_randomstate_regression.py', 'venv/lib/python3.13/site-packages/numpy/f2py/diagnose.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_arraypad.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_block_docstring.py', 'venv/lib/python3.13/site-packages/numpy/lib/_shape_base_impl.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_umath.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_casting_unittests.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_protocols.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/ndarray_misc.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_hashtable.py', 'venv/lib/python3.13/site-packages/numpy/linalg/_linalg.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/lib_utils.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_modules.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_scalarprint.py', 'venv/lib/python3.13/site-packages/numpy/tests/test_numpy_version.py', 'venv/lib/python3.13/site-packages/numpy/_core/shape_base.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/array_like.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_assumed_shape.py', 'venv/lib/python3.13/site-packages/numpy/ma/tests/test_mrecords.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/flatiter.py', 'venv/lib/python3.13/site-packages/numpy/_pyinstaller/tests/__init__.py', 'venv/lib/python3.13/site-packages/numpy/matrixlib/tests/test_matrix_linalg.py', 'venv/lib/python3.13/site-packages/numpy/_typing/__init__.py', 'venv/lib/python3.13/site-packages/numpy/_core/overrides.py', 'venv/lib/python3.13/site-packages/numpy/f2py/__init__.py', 'venv/lib/python3.13/site-packages/numpy/testing/__init__.py', 'venv/lib/python3.13/site-packages/numpy/lib/user_array.py', 'venv/lib/python3.13/site-packages/numpy/random/_examples/numba/extending_distributions.py', 'venv/lib/python3.13/site-packages/numpy/ma/tests/test_deprecations.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/arithmetic.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_nested_sequence.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_half.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/tests/test_hermite.py', 'venv/lib/python3.13/site-packages/numpy/fft/__init__.py', 'venv/lib/python3.13/site-packages/numpy/_core/defchararray.py', 'venv/lib/python3.13/site-packages/numpy/linalg/tests/test_deprecations.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_nanfunctions.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_api.py', 'venv/lib/python3.13/site-packages/numpy/f2py/f2py2e.py', 'venv/lib/python3.13/site-packages/numpy/fft/_pocketfft.py', 'venv/lib/python3.13/site-packages/numpy/f2py/_isocbind.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/lib_version.py', 'venv/lib/python3.13/site-packages/numpy/random/_examples/cffi/extending.py', 'venv/lib/python3.13/site-packages/numpy/_core/cversions.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/shape.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_size.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/modules.py', 'venv/lib/python3.13/site-packages/numpy/fft/tests/test_pocketfft.py', 'venv/lib/python3.13/site-packages/numpy/lib/_histograms_impl.py', 'venv/lib/python3.13/site-packages/numpy/core/numeric.py', 'venv/lib/python3.13/site-packages/numpy/lib/_npyio_impl.py', 'venv/lib/python3.13/site-packages/numpy/tests/test_reloading.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/literal.py', 'venv/lib/python3.13/site-packages/numpy/_pyinstaller/tests/pyinstaller-smoke.py', 'venv/lib/python3.13/site-packages/numpy/f2py/cb_rules.py', 'venv/lib/python3.13/site-packages/numpy/f2py/capi_maps.py', 'venv/lib/python3.13/site-packages/numpy/_pyinstaller/tests/test_pyinstaller.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/lib_user_array.py', 'venv/lib/python3.13/site-packages/numpy/linalg/tests/test_linalg.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_abstract_interface.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/chebyshev.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_array_like.py', 'venv/lib/python3.13/site-packages/numpy/random/tests/test_randomstate.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/dtype.py', 'venv/lib/python3.13/site-packages/numpy/matrixlib/tests/test_multiarray.py', 'venv/lib/python3.13/site-packages/numpy/core/_multiarray_umath.py', 'venv/lib/python3.13/site-packages/numpy/ma/tests/test_subclassing.py', 'venv/lib/python3.13/site-packages/numpy/lib/introspect.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_stringdtype.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_arrayterator.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_conversion_utils.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_mixins.py', 'venv/lib/python3.13/site-packages/numpy/ma/mrecords.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_routines.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_ufunclike.py', 'venv/lib/python3.13/site-packages/numpy/random/tests/test_random.py', 'venv/lib/python3.13/site-packages/numpy/_utils/_convertions.py', 'venv/lib/python3.13/site-packages/numpy/f2py/_backends/_distutils.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_strings.py', 'venv/lib/python3.13/site-packages/numpy/core/_utils.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_semicolon_split.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/ufunc_config.py', 'venv/lib/python3.13/site-packages/numpy/lib/recfunctions.py', 'venv/lib/python3.13/site-packages/numpy/_core/multiarray.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/legendre.py', 'venv/lib/python3.13/site-packages/numpy/core/multiarray.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_callback.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_regression.py', 'venv/lib/python3.13/site-packages/numpy/_core/memmap.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/multiarray.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/recfunctions.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test__version.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_array_api_info.py', 'venv/lib/python3.13/site-packages/numpy/core/function_base.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/tests/test_symbol.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/simple.py', 'venv/lib/python3.13/site-packages/numpy/typing/mypy_plugin.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_custom_dtypes.py', 'venv/lib/python3.13/site-packages/numpy/_core/umath.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_simd.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/test_isfile.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_deprecations.py', 'venv/lib/python3.13/site-packages/numpy/_typing/_extended_precision.py', 'venv/lib/python3.13/site-packages/numpy/doc/ufuncs.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_string.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_nep50_promotions.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_array_coercion.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/tests/test_polyutils.py', 'venv/lib/python3.13/site-packages/numpy/linalg/tests/test_regression.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_function_base.py', 'venv/lib/python3.13/site-packages/numpy/_core/_ufunc_config.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_argparse.py', 'venv/lib/python3.13/site-packages/numpy/linalg/linalg.py', 'venv/lib/python3.13/site-packages/numpy/core/__init__.py', 'venv/lib/python3.13/site-packages/numpy/_utils/_inspect.py', 'venv/lib/python3.13/site-packages/numpy/f2py/_backends/_meson.py', 'venv/lib/python3.13/site-packages/numpy/f2py/auxfuncs.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_dtype.py', 'venv/lib/python3.13/site-packages/numpy/lib/npyio.py', 'venv/lib/python3.13/site-packages/numpy/fft/helper.py', 'venv/lib/python3.13/site-packages/numpy/testing/print_coercion_tables.py', 'venv/lib/python3.13/site-packages/numpy/matrixlib/tests/test_defmatrix.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/ndarray_conversion.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/simple_py3.py', 'venv/lib/python3.13/site-packages/numpy/tests/test_scripts.py', 'venv/lib/python3.13/site-packages/numpy/_core/function_base.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_memmap.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/tests/test_chebyshev.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/data/pass/ma.py', 'venv/lib/python3.13/site-packages/numpy/f2py/tests/test_data.py', 'venv/lib/python3.13/site-packages/numpy/matrixlib/tests/test_regression.py', 'venv/lib/python3.13/site-packages/numpy/lib/tests/test_shape_base.py', 'venv/lib/python3.13/site-packages/numpy/polynomial/tests/test_printing.py', 'venv/lib/python3.13/site-packages/numpy/_core/tests/test_records.py', 'venv/lib/python3.13/site-packages/numpy/f2py/_backends/__init__.py', 'venv/lib/python3.13/site-packages/numpy/typing/tests/__init__.py', 'venv/lib/python3.13/site-packages/numpy/random/tests/test_smoke.py'. Reloading...
Process SpawnProcess-11:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 9, in <module>
    from faster_whisper import WhisperModel
ModuleNotFoundError: No module named 'faster_whisper'
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/av/packet.py', 'venv/lib/python3.13/site-packages/humanfriendly/terminal/html.py', 'venv/lib/python3.13/site-packages/fsspec/tests/abstract/common.py', 'venv/lib/python3.13/site-packages/av/datasets.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/cache_mapper.py', 'venv/lib/python3.13/site-packages/filelock/asyncio.py', 'venv/lib/python3.13/site-packages/humanfriendly/sphinx.py', 'venv/lib/python3.13/site-packages/fsspec/utils.py', 'venv/lib/python3.13/site-packages/humanfriendly/tables.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/libarchive.py', 'venv/lib/python3.13/site-packages/fsspec/config.py', 'venv/lib/python3.13/site-packages/av/about.py', 'venv/lib/python3.13/site-packages/filelock/_api.py', 'venv/lib/python3.13/site-packages/humanfriendly/usage.py', 'venv/lib/python3.13/site-packages/fsspec/json.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/__init__.py', 'venv/lib/python3.13/site-packages/fsspec/__init__.py', 'venv/lib/python3.13/site-packages/av/codec/__init__.py', 'venv/lib/python3.13/site-packages/fsspec/tests/abstract/open.py', 'venv/lib/python3.13/site-packages/av/audio/__init__.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/memory.py', 'venv/lib/python3.13/site-packages/filelock/_soft.py', 'venv/lib/python3.13/site-packages/fsspec/transaction.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/reference.py', 'venv/lib/python3.13/site-packages/fsspec/fuse.py', 'venv/lib/python3.13/site-packages/av/filter/__init__.py', 'venv/lib/python3.13/site-packages/fsspec/parquet.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/local.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/webhdfs.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/arrow.py', 'venv/lib/python3.13/site-packages/humanfriendly/compat.py', 'venv/lib/python3.13/site-packages/humanfriendly/terminal/spinners.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/git.py', 'venv/lib/python3.13/site-packages/fsspec/core.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/data.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/github.py', 'venv/lib/python3.13/site-packages/fsspec/_version.py', 'venv/lib/python3.13/site-packages/fsspec/generic.py', 'venv/lib/python3.13/site-packages/humanfriendly/testing.py', 'venv/lib/python3.13/site-packages/humanfriendly/deprecation.py', 'venv/lib/python3.13/site-packages/fsspec/tests/abstract/copy.py', 'venv/lib/python3.13/site-packages/filelock/_unix.py', 'venv/lib/python3.13/site-packages/fsspec/mapping.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/tar.py', 'venv/lib/python3.13/site-packages/humanfriendly/__init__.py', 'venv/lib/python3.13/site-packages/humanfriendly/terminal/__init__.py', 'venv/lib/python3.13/site-packages/fsspec/tests/abstract/__init__.py', 'venv/lib/python3.13/site-packages/fsspec/spec.py', 'venv/lib/python3.13/site-packages/filelock/_util.py', 'venv/lib/python3.13/site-packages/hf_xet/__init__.py', 'venv/lib/python3.13/site-packages/av/audio/codeccontext.py', 'venv/lib/python3.13/site-packages/fsspec/callbacks.py', 'venv/lib/python3.13/site-packages/fsspec/tests/abstract/pipe.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/gist.py', 'venv/lib/python3.13/site-packages/filelock/version.py', 'venv/lib/python3.13/site-packages/filelock/__init__.py', 'venv/lib/python3.13/site-packages/fsspec/conftest.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/cached.py', 'venv/lib/python3.13/site-packages/fsspec/compression.py', 'venv/lib/python3.13/site-packages/humanfriendly/cli.py', 'venv/lib/python3.13/site-packages/fsspec/exceptions.py', 'venv/lib/python3.13/site-packages/fsspec/caching.py', 'venv/lib/python3.13/site-packages/av/data/__init__.py', 'venv/lib/python3.13/site-packages/filelock/_error.py', 'venv/lib/python3.13/site-packages/fsspec/tests/abstract/get.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/jupyter.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/dirfs.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/http.py', 'venv/lib/python3.13/site-packages/fsspec/gui.py', 'venv/lib/python3.13/site-packages/humanfriendly/text.py', 'venv/lib/python3.13/site-packages/av/__main__.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/dbfs.py', 'venv/lib/python3.13/site-packages/fsspec/registry.py', 'venv/lib/python3.13/site-packages/fsspec/asyn.py', 'venv/lib/python3.13/site-packages/fsspec/tests/abstract/mv.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/http_sync.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/ftp.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/cache_metadata.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/smb.py', 'venv/lib/python3.13/site-packages/fsspec/archive.py', 'venv/lib/python3.13/site-packages/humanfriendly/tests.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/dask.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/asyn_wrapper.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/sftp.py', 'venv/lib/python3.13/site-packages/av/__init__.py', 'venv/lib/python3.13/site-packages/humanfriendly/prompts.py', 'venv/lib/python3.13/site-packages/filelock/_windows.py', 'venv/lib/python3.13/site-packages/humanfriendly/case.py', 'venv/lib/python3.13/site-packages/fsspec/tests/abstract/put.py', 'venv/lib/python3.13/site-packages/fsspec/dircache.py', 'venv/lib/python3.13/site-packages/humanfriendly/decorators.py', 'venv/lib/python3.13/site-packages/fsspec/implementations/zip.py'. Reloading...
Process SpawnProcess-12:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 9, in <module>
    from faster_whisper import WhisperModel
ModuleNotFoundError: No module named 'faster_whisper'
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/image_segmentation.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/__init__.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_deprecation.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_mcp/cli.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/logging.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_hf_folder.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_paths.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/hf_inference.py', 'venv/lib/python3.13/site-packages/huggingface_hub/community.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_lfs.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_safetensors.py', 'venv/lib/python3.13/site-packages/huggingface_hub/__init__.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_mcp/_cli_hacks.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_http.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/repo_files.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_headers.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/zero_shot_image_classification.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/nebius.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/visual_question_answering.py', 'venv/lib/python3.13/site-packages/huggingface_hub/repository.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/video_classification.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/fal_ai.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/repo.py', 'venv/lib/python3.13/site-packages/huggingface_hub/serialization/_base.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/tqdm.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/image_to_text.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/zero_shot_object_detection.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/huggingface_cli.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_common.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/fireworks_ai.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_subprocess.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_chunk_utils.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/document_question_answering.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_inference_endpoints.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_pagination.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/featherless_ai.py', 'venv/lib/python3.13/site-packages/huggingface_hub/errors.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_validators.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_mcp/utils.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/translation.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_typing.py', 'venv/lib/python3.13/site-packages/huggingface_hub/hub_mixin.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/env.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/question_answering.py', 'venv/lib/python3.13/site-packages/huggingface_hub/serialization/_torch.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_cache_assets.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/cohere.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference_api.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_commit_api.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/text_to_image.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/upload_large_folder.py', 'venv/lib/python3.13/site-packages/huggingface_hub/hf_file_system.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_space_api.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/summarization.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/groq.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/__init__.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/audio_to_audio.py', 'venv/lib/python3.13/site-packages/huggingface_hub/serialization/_tensorflow.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/tag.py', 'venv/lib/python3.13/site-packages/av/filter/loudnorm.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/table_question_answering.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_runtime.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/text_to_speech.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_auth.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/sha.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/cerebras.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/text_to_video.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/_async_client.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/image_to_image.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/sambanova.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_webhooks_payload.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_experimental.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/depth_estimation.py', 'venv/lib/python3.13/site-packages/huggingface_hub/constants.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_fixes.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/nscale.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_login.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/automatic_speech_recognition.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/chat_completion.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_telemetry.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_mcp/mcp_client.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/novita.py', 'venv/lib/python3.13/site-packages/av/sidedata/__init__.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_oauth.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_mcp/types.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_xet.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_webhooks_server.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_commit_scheduler.py', 'venv/lib/python3.13/site-packages/huggingface_hub/repocard_data.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/audio_classification.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/_common.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/version.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_client.py', 'venv/lib/python3.13/site-packages/huggingface_hub/lfs.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/endpoint_helpers.py', 'venv/lib/python3.13/site-packages/huggingface_hub/fastai_utils.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/scan_cache.py', 'venv/lib/python3.13/site-packages/huggingface_hub/repocard.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_tensorboard_logger.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/upload.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/lfs.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/download.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/__init__.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/delete_cache.py', 'venv/lib/python3.13/site-packages/huggingface_hub/serialization/_dduf.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/zero_shot_classification.py', 'venv/lib/python3.13/site-packages/huggingface_hub/serialization/__init__.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_local_folder.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/token_classification.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/user.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/image_classification.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_mcp/agent.py', 'venv/lib/python3.13/site-packages/huggingface_hub/hf_api.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/insecure_hashlib.py', 'venv/lib/python3.13/site-packages/huggingface_hub/dataclasses.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/text_to_audio.py', 'venv/lib/python3.13/site-packages/huggingface_hub/keras_mixin.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/hyperbolic.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_datetime.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/_cli_utils.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/feature_extraction.py', 'venv/lib/python3.13/site-packages/av/video/__init__.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/together.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_mcp/constants.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_git_credential.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/replicate.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/black_forest_labs.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/base.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/text_generation.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_providers/openai.py', 'venv/lib/python3.13/site-packages/huggingface_hub/utils/_cache_manager.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/object_detection.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_snapshot_download.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/sentence_similarity.py', 'venv/lib/python3.13/site-packages/huggingface_hub/commands/__init__.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/fill_mask.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/text_classification.py', 'venv/lib/python3.13/site-packages/huggingface_hub/_upload_large_folder.py', 'venv/lib/python3.13/site-packages/huggingface_hub/inference/_generated/types/text2text_generation.py', 'venv/lib/python3.13/site-packages/huggingface_hub/file_download.py'. Reloading...
Process SpawnProcess-13:
Traceback (most recent call last):
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    ~~~~~~~~^^
  File "/home/<USER>/miniconda3/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    ~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ~~~~~~~~~~^^^^^^
  File "/home/<USER>/miniconda3/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 70, in serve
    await self._serve(sockets)
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/server.py", line 77, in _serve
    config.load()
    ~~~~~~~~~~~^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/config.py", line 435, in load
    self.loaded_app = import_from_string(self.app)
                      ~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 22, in import_from_string
    raise exc from None
  File "/home/<USER>/PROJ/diarizator/backend/venv/lib/python3.13/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
  File "/home/<USER>/miniconda3/lib/python3.13/importlib/__init__.py", line 88, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 1026, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/PROJ/diarizator/backend/src/main.py", line 15, in <module>
    from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb
  File "/home/<USER>/PROJ/diarizator/backend/src/processing.py", line 9, in <module>
    from faster_whisper import WhisperModel
ModuleNotFoundError: No module named 'faster_whisper'
WARNING:  WatchFiles detected changes in 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_conformer_attention.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecordContainerEntry.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_model_processor.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_biassplitgelu.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/diffusion_models.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/optimize_pipeline.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/profile_result_processor.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/activation.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/shape_infer_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Attribute.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/utils.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/opennmt_py.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SparseTensor.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/gpt2/parity_check_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_attention_vae.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_bert_keras.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/static_quantize_runner.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/calibrate.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OptimizerGroup.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_skiplayernorm.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/types.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/gemm.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/operator_type_usage_processors.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/longformer/benchmark_longformer.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/EdgeEnd.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_rotary_attention.py', 'venv/lib/python3.13/site-packages/ctranslate2/version.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_embedlayer.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/longformer/longformer_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/pytorch_export_helpers.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/sam2_demo.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/conv.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/opus_mt.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/concat.py', 'venv/lib/python3.13/site-packages/tokenizers/tools/__init__.py', 'venv/lib/python3.13/site-packages/tokenizers/implementations/sentencepiece_bpe.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgType.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/machine_info.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/bert/eval_squad.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/__init__.py', 'venv/lib/python3.13/site-packages/faster_whisper/vad.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedKernelCreateInfos.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_attention_unet.py', 'venv/lib/python3.13/site-packages/ctranslate2/specs/attention_spec.py', 'venv/lib/python3.13/site-packages/tokenizers/implementations/base_tokenizer.py', 'venv/lib/python3.13/site-packages/tokenizers/tools/visualizer.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/benchmark_sam2.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/gpt2/benchmark_gpt2.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_qordered_matmul.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/metrics.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/norm.py', 'venv/lib/python3.13/site-packages/tokenizers/models/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/longformer/generate_test_data.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/longformer/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Tensor.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_bert.py', 'venv/lib/python3.13/site-packages/faster_whisper/transcribe.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/transformers.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedNodeIndexAndKernelDefHash.py', 'venv/lib/python3.13/site-packages/faster_whisper/audio.py', 'venv/lib/python3.13/site-packages/onnxruntime/backend/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/CalTableFlatBuffers/TrtTable.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/phi2/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/execution_providers/qnn/quant_config.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/eole_ct2.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_transpose.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/phi2/convert_to_onnx.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/bart/export.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeEdge.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/registry.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_qordered_gelu.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_options.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_group_norm.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/bert_test_data.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/base_operator.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_exporter.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/AttributeType.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/t5/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/sam2_image_onnx_predictor.py', 'venv/lib/python3.13/site-packages/onnxruntime/capi/onnxruntime_collect_build_info.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/pad.py', 'venv/lib/python3.13/site-packages/tokenizers/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_fastgelu.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/whisper_chain.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/base_quantizer.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/pytorch_export_contrib_ops.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Model.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/openai_gpt2.py', 'venv/lib/python3.13/site-packages/ctranslate2/specs/transformer_spec.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/qdq_quantizer.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/llama/llama_torch.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_gpt_attention_megatron.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/onnx_model_utils.py', 'venv/lib/python3.13/site-packages/tokenizers/trainers/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorTypeAndShape.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/FloatProperty.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/fairseq.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/split.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/pipeline_stable_diffusion.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/execution_providers/qnn/fusion_lpnorm.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/fusions/fusion_layernorm.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/fusions/fusion_gelu.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/llama/benchmark.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfo.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/whisper_decoder.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_phi.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/whisper_jump_times.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/marian.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/onnx_model.py', 'venv/lib/python3.13/site-packages/tokenizers/processors/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_simplified_layernorm.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/converter.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_mha_mmdit.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_gpt_attention_no_past.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/whisper_encoder_decoder_init.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_bart_attention.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/symbolic_shape_infer.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/PropertyBag.py', 'venv/lib/python3.13/site-packages/onnxruntime/capi/build_and_package_info.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Dimension.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrArgsEntry.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeType.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/convert_generation.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrResolver.py', 'venv/lib/python3.13/site-packages/onnxruntime/datasets/__init__.py', 'venv/lib/python3.13/site-packages/coloredlogs/tests.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_attention_clip.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/matmul_nbits_quantizer.py', 'venv/lib/python3.13/site-packages/onnxruntime/capi/convert_npz_to_onnx_adapter.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/fusions/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_conformer.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/whisper_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_skip_group_norm.py', 'venv/lib/python3.13/site-packages/ctranslate2/models/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_sam2.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfoValue.py', 'venv/lib/python3.13/site-packages/coloredlogs/demo.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_tnlr.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/whisper_encoder.py', 'venv/lib/python3.13/site-packages/tokenizers/implementations/char_level_bpe.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/where.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/torch_onnx_export_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizations.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValueType.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_clip.py', 'venv/lib/python3.13/site-packages/coloredlogs/converter/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValue.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/longformer/convert_to_onnx.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/binary_op.py', 'venv/lib/python3.13/site-packages/ctranslate2/converters/opennmt_tf.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/float16.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/profiler.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/qdq_loss_debug.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_constant_fold.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/dynamo_onnx_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Graph.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OperatorSetId.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_gelu_approximation.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/logger.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/preprocess.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/InferenceSession.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/llama/quant_kv_dataloader.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/argmax.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_t5.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSessionState.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/CalTableFlatBuffers/KeyValue.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/check_onnx_model_mobile_usability.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/gavgpool.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_bias_add.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/onnxruntime_test.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SequenceType.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_cuda.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/llama/__init__.py', 'venv/lib/python3.13/site-packages/ctranslate2/specs/common_spec.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/benchmark_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/execution_providers/qnn/mixed_precision_overrides_utils.py', 'venv/lib/python3.13/site-packages/onnxruntime/capi/_ld_preload.py', 'venv/lib/python3.13/site-packages/tokenizers/implementations/sentencepiece_unigram.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/embed_layernorm.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/direct_q8.py', 'venv/lib/python3.13/site-packages/onnxruntime/backend/backend_rep.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/resize.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_qordered_attention.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/mobile_helpers/usability_checker.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/phi2/inference_example.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/attention.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/execution_providers/qnn/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_attention_sam2.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/shape_inference.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/__init__.py', 'venv/lib/python3.13/site-packages/ctranslate2/specs/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_base.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_gemmfastgelu.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/bert_perf_test.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_utils.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/CalTableFlatBuffers/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/demo_txt2img.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/gpt2/gpt2_helper.py', 'venv/lib/python3.13/site-packages/faster_whisper/feature_extractor.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/quant_utils.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_torch.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_qordered_layernorm.py', 'venv/lib/python3.13/site-packages/onnxruntime/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ValueInfo.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/onnx_randomizer.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Node.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/utils.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/optimizer.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/shape_optimizer.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_reshape.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorDataType.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/onnx_quantizer.py', 'venv/lib/python3.13/site-packages/onnxruntime/capi/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_tensorrt.py', 'venv/lib/python3.13/site-packages/faster_whisper/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/t5/t5_helper.py', 'venv/lib/python3.13/site-packages/tokenizers/pre_tokenizers/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/tensor_quant_overrides.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/IntProperty.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSubGraphSessionState.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/gpt2/gpt2_tester.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/constants.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/whisper_inputs.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/prompt_encoder.py', 'venv/lib/python3.13/site-packages/onnxruntime/backend/backend.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/optimize_onnx_model.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_attention.py', 'venv/lib/python3.13/site-packages/coloredlogs/syslog.py', 'venv/lib/python3.13/site-packages/tokenizers/implementations/bert_wordpiece.py', 'venv/lib/python3.13/site-packages/ctranslate2/specs/wav2vec2bert_spec.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/lstm.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/fusions/fusion.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/convert_to_onnx.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_biasgelu.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgTypeAndIndex.py', 'venv/lib/python3.13/site-packages/faster_whisper/assets/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/llama/benchmark_all.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ModuleState.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_trt.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/trt_utilities.py', 'venv/lib/python3.13/site-packages/onnxruntime/capi/onnxruntime_validation.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/benchmark.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_mmdit.py', 'venv/lib/python3.13/site-packages/ctranslate2/specs/model_spec.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/quantize.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/llama/benchmark_e2e.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/gpt2/gpt2_parity.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/t5/t5_decoder.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Checkpoint.py', 'venv/lib/python3.13/site-packages/coloredlogs/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/demo_txt2img_xl.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/llama/llama_parity.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/make_dynamic_shape_fixed.py', 'venv/lib/python3.13/site-packages/ctranslate2/logging.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/execution_providers/qnn/preprocess.py', 'venv/lib/python3.13/site-packages/faster_whisper/tokenizer.py', 'venv/lib/python3.13/site-packages/ctranslate2/specs/whisper_spec.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_vae.py', 'venv/lib/python3.13/site-packages/ctranslate2/extensions.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/compare_bert_results.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_bert_tf.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/io_binding_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/quantize_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_utils.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ParameterOptimizerState.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/t5/t5_encoder.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Shape.py', 'venv/lib/python3.13/site-packages/onnxruntime/capi/_pybind_state.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/demo_utils.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/convert_onnx_models_to_ort.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/sam2_utils.py', 'venv/lib/python3.13/site-packages/tokenizers/implementations/byte_level_bpe.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/ort_optimizer.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/qdq_base_operator.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/large_model_exporter.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/t5/t5_encoder_decoder_init.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/mask_decoder.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/llama/llama_inputs.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/nvtx_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/image_encoder.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/benchmark_all.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/benchmark_controlnet.py', 'venv/lib/python3.13/site-packages/coloredlogs/cli.py', 'venv/lib/python3.13/site-packages/coloredlogs/converter/colors.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_gpt2.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_quickgelu.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/diffusion_schedulers.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/maxpool.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/benchmark.py', 'venv/lib/python3.13/site-packages/tokenizers/implementations/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/convert_to_packing_mode.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/benchmark.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/softmax.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/reduced_build_config_parser.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringProperty.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/llama/convert_to_onnx.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/file_utils.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecord.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/past_helper.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/import_utils.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/pooling.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringStringEntry.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/gpt2/__init__.py', 'venv/lib/python3.13/site-packages/ctranslate2/specs/wav2vec2_spec.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/bert/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/convert_tf_models_to_pytorch.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/llama/dist_settings.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/whisper/convert_to_onnx.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_gpt_attention.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/huggingface_models.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/gather.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_nhwc_conv.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_bart.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/gpt2/convert_to_onnx.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_layernorm.py', 'venv/lib/python3.13/site-packages/onnxruntime/capi/onnxruntime_inference_collection.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model_unet.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/t5/convert_to_onnx.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/matmul_bnb4_quantizer.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_shape.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/offline_tuning.py', 'venv/lib/python3.13/site-packages/faster_whisper/utils.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/sam2/image_decoder.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodesToOptimizeIndices.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/update_onnx_opset.py', 'venv/lib/python3.13/site-packages/ctranslate2/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OpIdKernelTypeStrArgsEntry.py', 'venv/lib/python3.13/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/MapType.py', 'venv/lib/python3.13/site-packages/onnxruntime/quantization/operators/matmul.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/onnx_model.py', 'venv/lib/python3.13/site-packages/faster_whisper/version.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/models/stable_diffusion/__init__.py', 'venv/lib/python3.13/site-packages/onnxruntime/transformers/fusion_gelu.py'. Reloading...
INFO:     Started server process [272902]
INFO:     Waiting for application startup.
Приложение запускается...
2025-06-22 12:57:02,372 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 12:57:02,373 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("users")
2025-06-22 12:57:02,373 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 12:57:02,375 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("files")
2025-06-22 12:57:02,375 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 12:57:02,376 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("user_sessions")
2025-06-22 12:57:02,377 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 12:57:02,377 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Application startup complete.
INFO:     Started server process [272937]
INFO:     Waiting for application startup.
Приложение запускается...
2025-06-22 12:57:05,403 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 12:57:05,404 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("users")
2025-06-22 12:57:05,405 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 12:57:05,407 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("files")
2025-06-22 12:57:05,407 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 12:57:05,408 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("user_sessions")
2025-06-22 12:57:05,408 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 12:57:05,409 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Application startup complete.
INFO:     127.0.0.1:44642 - "GET /health HTTP/1.1" 200 OK
🔐 Attempting auth with token: debug_mode_fake_init_data...
🔑 Bot token available: True
🔍 Validating initData: debug_mode_fake_init_data...
DEBUG: Using fake initData for testing
✅ Auth successful for user: 12345
👤 Creating/getting user with data: {'id': 12345, 'first_name': 'Константин', 'username': 'debug_user'}
2025-06-22 12:57:18,938 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 12:57:18,966 INFO sqlalchemy.engine.Engine SELECT users.id, users.telegram_id, users.username, users.first_name, users.created_at 
FROM users 
WHERE users.telegram_id = ?
2025-06-22 12:57:18,967 INFO sqlalchemy.engine.Engine [generated in 0.00074s] (12345,)
✅ Successfully got user: 12345
2025-06-22 12:57:18,973 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ? AND files.owner_id = ?
2025-06-22 12:57:18,973 INFO sqlalchemy.engine.Engine [generated in 0.00069s] ('87f30e91-de9d-48f1-9fae-d97eacd75875', 'db8e85eb-cdbe-40f9-a90a-65fe1654744d')
2025-06-22 12:57:18,977 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:49408 - "GET /api/v1/summary/87f30e91-de9d-48f1-9fae-d97eacd75875 HTTP/1.1" 200 OK
🔐 Attempting auth with token: debug_mode_fake_init_data...
🔑 Bot token available: True
🔍 Validating initData: debug_mode_fake_init_data...
DEBUG: Using fake initData for testing
✅ Auth successful for user: 12345
👤 Creating/getting user with data: {'id': 12345, 'first_name': 'Константин', 'username': 'debug_user'}
2025-06-22 12:57:28,124 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 12:57:28,125 INFO sqlalchemy.engine.Engine SELECT users.id, users.telegram_id, users.username, users.first_name, users.created_at 
FROM users 
WHERE users.telegram_id = ?
2025-06-22 12:57:28,127 INFO sqlalchemy.engine.Engine [cached since 9.161s ago] (12345,)
✅ Successfully got user: 12345
2025-06-22 12:57:28,134 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ? AND files.owner_id = ?
2025-06-22 12:57:28,134 INFO sqlalchemy.engine.Engine [cached since 9.162s ago] ('nonexistent-id', 'db8e85eb-cdbe-40f9-a90a-65fe1654744d')
2025-06-22 12:57:28,136 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:57996 - "GET /api/v1/summary/nonexistent-id HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58010 - "GET /api/v1/summary/87f30e91-de9d-48f1-9fae-d97eacd75875 HTTP/1.1" 403 Forbidden
🔐 Attempting auth with token: test_init_data...
🔑 Bot token available: True
🔍 Validating initData: test_init_data...
📊 Parsed data keys: []
❌ No hash found in initData
❌ Auth validation failed
📝 Token sample: test_init_data...
INFO:     127.0.0.1:42186 - "POST /api/v1/upload HTTP/1.1" 401 Unauthorized
🔐 Attempting auth with token: debug_mode_fake_init_data...
🔑 Bot token available: True
🔍 Validating initData: debug_mode_fake_init_data...
DEBUG: Using fake initData for testing
✅ Auth successful for user: 12345
👤 Creating/getting user with data: {'id': 12345, 'first_name': 'Константин', 'username': 'debug_user'}
2025-06-22 13:06:28,338 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:06:28,338 INFO sqlalchemy.engine.Engine SELECT users.id, users.telegram_id, users.username, users.first_name, users.created_at 
FROM users 
WHERE users.telegram_id = ?
2025-06-22 13:06:28,339 INFO sqlalchemy.engine.Engine [cached since 549.4s ago] (12345,)
✅ Successfully got user: 12345
2025-06-22 13:06:28,351 INFO sqlalchemy.engine.Engine SELECT coalesce(sum(files.file_size), ?) AS coalesce_1 
FROM files 
WHERE files.owner_id = ?
2025-06-22 13:06:28,351 INFO sqlalchemy.engine.Engine [generated in 0.00024s] (0, 'db8e85eb-cdbe-40f9-a90a-65fe1654744d')
2025-06-22 13:06:28,353 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:34538 - "POST /api/v1/upload HTTP/1.1" 400 Bad Request
🔐 Attempting auth with token: debug_mode_fake_init_data...
🔑 Bot token available: True
🔍 Validating initData: debug_mode_fake_init_data...
DEBUG: Using fake initData for testing
✅ Auth successful for user: 12345
👤 Creating/getting user with data: {'id': 12345, 'first_name': 'Константин', 'username': 'debug_user'}
2025-06-22 13:06:59,833 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:06:59,833 INFO sqlalchemy.engine.Engine SELECT users.id, users.telegram_id, users.username, users.first_name, users.created_at 
FROM users 
WHERE users.telegram_id = ?
2025-06-22 13:06:59,834 INFO sqlalchemy.engine.Engine [cached since 580.9s ago] (12345,)
✅ Successfully got user: 12345
2025-06-22 13:06:59,835 INFO sqlalchemy.engine.Engine SELECT coalesce(sum(files.file_size), ?) AS coalesce_1 
FROM files 
WHERE files.owner_id = ?
2025-06-22 13:06:59,836 INFO sqlalchemy.engine.Engine [cached since 31.48s ago] (0, 'db8e85eb-cdbe-40f9-a90a-65fe1654744d')
2025-06-22 13:06:59,900 INFO sqlalchemy.engine.Engine INSERT INTO files (id, filename, original_filename, file_size, status, created_at, summary_text, owner_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
2025-06-22 13:06:59,900 INFO sqlalchemy.engine.Engine [generated in 0.00052s] ('9eca6574-1d43-42f1-8d0b-f322b874a494', '69f11a35-c63f-4da7-b115-2e25a0417896.m4a', '333.m4a', 30305911, 'в очереди', '2025-06-22 13:06:59.900020', None, 'db8e85eb-cdbe-40f9-a90a-65fe1654744d')
2025-06-22 13:06:59,901 INFO sqlalchemy.engine.Engine COMMIT
2025-06-22 13:06:59,910 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:06:59,913 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:06:59,913 INFO sqlalchemy.engine.Engine [generated in 0.00049s] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:06:59,920 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:56096 - "POST /api/v1/upload HTTP/1.1" 202 Accepted
🎵 Начинаем обработку файла 9eca6574-1d43-42f1-8d0b-f322b874a494
2025-06-22 13:06:59,934 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:06:59,935 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:06:59,935 INFO sqlalchemy.engine.Engine [generated in 0.00029s] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:06:59,942 INFO sqlalchemy.engine.Engine UPDATE files SET status=? WHERE files.id = ?
2025-06-22 13:06:59,942 INFO sqlalchemy.engine.Engine [generated in 0.00031s] ('Подготовка к обработке... 5%', '9eca6574-1d43-42f1-8d0b-f322b874a494')
2025-06-22 13:06:59,944 INFO sqlalchemy.engine.Engine COMMIT
2025-06-22 13:06:59,950 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:06:59,950 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:06:59,951 INFO sqlalchemy.engine.Engine [cached since 0.03775s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:06:59,952 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:06:59,952 INFO sqlalchemy.engine.Engine [cached since 0.01751s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:06:59,954 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:06:59,954 INFO sqlalchemy.engine.Engine [cached since 0.01902s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:06:59,955 INFO sqlalchemy.engine.Engine UPDATE files SET status=? WHERE files.id = ?
2025-06-22 13:06:59,955 INFO sqlalchemy.engine.Engine [cached since 0.01314s ago] ('Файл найден, начинаем обработку... 10%', '9eca6574-1d43-42f1-8d0b-f322b874a494')
2025-06-22 13:06:59,958 INFO sqlalchemy.engine.Engine COMMIT
2025-06-22 13:06:59,966 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:06:59,967 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:06:59,967 INFO sqlalchemy.engine.Engine [cached since 0.0539s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:06:59,978 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:06:59,978 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:06:59,980 INFO sqlalchemy.engine.Engine [cached since 0.04534s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:06:59,984 INFO sqlalchemy.engine.Engine UPDATE files SET status=? WHERE files.id = ?
2025-06-22 13:06:59,985 INFO sqlalchemy.engine.Engine [cached since 0.04292s ago] ('Загрузка модели Whisper... 20%', '9eca6574-1d43-42f1-8d0b-f322b874a494')
2025-06-22 13:06:59,988 INFO sqlalchemy.engine.Engine COMMIT
2025-06-22 13:06:59,993 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:06:59,994 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:06:59,995 INFO sqlalchemy.engine.Engine [cached since 0.08169s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:06:59,996 INFO sqlalchemy.engine.Engine ROLLBACK
🤖 Загружаем модель Whisper для потока 123130619254272...
✅ Модель Whisper загружена для потока 123130619254272
2025-06-22 13:07:01,985 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:07:01,986 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:07:01,986 INFO sqlalchemy.engine.Engine [cached since 2.051s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:07:01,987 INFO sqlalchemy.engine.Engine UPDATE files SET status=? WHERE files.id = ?
2025-06-22 13:07:01,988 INFO sqlalchemy.engine.Engine [cached since 2.046s ago] ('Анализ аудиофайла... 25%', '9eca6574-1d43-42f1-8d0b-f322b874a494')
2025-06-22 13:07:01,989 INFO sqlalchemy.engine.Engine COMMIT
2025-06-22 13:07:01,994 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:07:01,994 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:07:01,994 INFO sqlalchemy.engine.Engine [cached since 2.081s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:07:01,995 INFO sqlalchemy.engine.Engine ROLLBACK
🎵 Длительность аудио: 941.1 секунд
🔐 Attempting auth with token: debug_mode_fake_init_data...
🔑 Bot token available: True
🔍 Validating initData: debug_mode_fake_init_data...
DEBUG: Using fake initData for testing
✅ Auth successful for user: 12345
👤 Creating/getting user with data: {'id': 12345, 'first_name': 'Константин', 'username': 'debug_user'}
2025-06-22 13:07:20,268 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:07:20,269 INFO sqlalchemy.engine.Engine SELECT users.id, users.telegram_id, users.username, users.first_name, users.created_at 
FROM users 
WHERE users.telegram_id = ?
2025-06-22 13:07:20,269 INFO sqlalchemy.engine.Engine [cached since 601.3s ago] (12345,)
✅ Successfully got user: 12345
2025-06-22 13:07:20,271 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ? AND files.owner_id = ?
2025-06-22 13:07:20,273 INFO sqlalchemy.engine.Engine [cached since 601.3s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494', 'db8e85eb-cdbe-40f9-a90a-65fe1654744d')
2025-06-22 13:07:20,275 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:46780 - "GET /api/v1/summary/9eca6574-1d43-42f1-8d0b-f322b874a494 HTTP/1.1" 400 Bad Request
❌ Ошибка транскрипции: no running event loop
❌ Ошибка при обработке файла 9eca6574-1d43-42f1-8d0b-f322b874a494: Ошибка при транскрипции аудио: no running event loop
2025-06-22 13:07:33,219 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:07:33,219 INFO sqlalchemy.engine.Engine [cached since 33.28s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
2025-06-22 13:07:33,220 INFO sqlalchemy.engine.Engine UPDATE files SET status=? WHERE files.id = ?
2025-06-22 13:07:33,221 INFO sqlalchemy.engine.Engine [cached since 33.28s ago] ('Ошибка: Ошибка при транскрипции аудио: no running event loop', '9eca6574-1d43-42f1-8d0b-f322b874a494')
2025-06-22 13:07:33,222 INFO sqlalchemy.engine.Engine COMMIT
2025-06-22 13:07:33,226 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:07:33,227 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ?
2025-06-22 13:07:33,227 INFO sqlalchemy.engine.Engine [cached since 33.31s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494',)
/home/<USER>/PROJ/diarizator/backend/src/processing.py:-1: RuntimeWarning: coroutine 'create_websocket_progress_callback.<locals>.progress_callback' was never awaited
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
2025-06-22 13:07:33,229 INFO sqlalchemy.engine.Engine ROLLBACK
🔐 Attempting auth with token: debug_mode_fake_init_data...
🔑 Bot token available: True
🔍 Validating initData: debug_mode_fake_init_data...
DEBUG: Using fake initData for testing
✅ Auth successful for user: 12345
👤 Creating/getting user with data: {'id': 12345, 'first_name': 'Константин', 'username': 'debug_user'}
2025-06-22 13:07:48,231 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:07:48,232 INFO sqlalchemy.engine.Engine SELECT users.id, users.telegram_id, users.username, users.first_name, users.created_at 
FROM users 
WHERE users.telegram_id = ?
2025-06-22 13:07:48,232 INFO sqlalchemy.engine.Engine [cached since 629.3s ago] (12345,)
✅ Successfully got user: 12345
2025-06-22 13:07:48,233 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ? AND files.owner_id = ?
2025-06-22 13:07:48,233 INFO sqlalchemy.engine.Engine [cached since 629.3s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494', 'db8e85eb-cdbe-40f9-a90a-65fe1654744d')
2025-06-22 13:07:48,234 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:42014 - "GET /api/v1/summary/9eca6574-1d43-42f1-8d0b-f322b874a494 HTTP/1.1" 400 Bad Request
🔐 Attempting auth with token: debug_mode_fake_init_data...
🔑 Bot token available: True
🔍 Validating initData: debug_mode_fake_init_data...
DEBUG: Using fake initData for testing
✅ Auth successful for user: 12345
👤 Creating/getting user with data: {'id': 12345, 'first_name': 'Константин', 'username': 'debug_user'}
2025-06-22 13:08:57,446 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:08:57,447 INFO sqlalchemy.engine.Engine SELECT users.id, users.telegram_id, users.username, users.first_name, users.created_at 
FROM users 
WHERE users.telegram_id = ?
2025-06-22 13:08:57,447 INFO sqlalchemy.engine.Engine [cached since 698.5s ago] (12345,)
✅ Successfully got user: 12345
2025-06-22 13:08:57,449 INFO sqlalchemy.engine.Engine SELECT files.id, files.filename, files.original_filename, files.file_size, files.status, files.created_at, files.summary_text, files.owner_id 
FROM files 
WHERE files.id = ? AND files.owner_id = ?
2025-06-22 13:08:57,449 INFO sqlalchemy.engine.Engine [cached since 698.5s ago] ('9eca6574-1d43-42f1-8d0b-f322b874a494', 'db8e85eb-cdbe-40f9-a90a-65fe1654744d')
2025-06-22 13:08:57,450 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:     127.0.0.1:44154 - "GET /api/v1/summary/9eca6574-1d43-42f1-8d0b-f322b874a494 HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'src/processing.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [272937]
INFO:     Started server process [365742]
INFO:     Waiting for application startup.
Приложение запускается...
2025-06-22 13:14:58,055 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:14:58,055 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("users")
2025-06-22 13:14:58,056 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:14:58,057 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("files")
2025-06-22 13:14:58,057 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:14:58,058 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("user_sessions")
2025-06-22 13:14:58,058 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:14:58,059 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/processing.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [365742]
INFO:     Started server process [365928]
INFO:     Waiting for application startup.
Приложение запускается...
2025-06-22 13:15:07,027 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:15:07,027 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("users")
2025-06-22 13:15:07,027 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:07,028 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("files")
2025-06-22 13:15:07,028 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:07,029 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("user_sessions")
2025-06-22 13:15:07,029 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:07,030 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/processing.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [365928]
INFO:     Started server process [366026]
INFO:     Waiting for application startup.
Приложение запускается...
2025-06-22 13:15:16,037 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:15:16,038 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("users")
2025-06-22 13:15:16,038 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:16,039 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("files")
2025-06-22 13:15:16,039 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:16,040 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("user_sessions")
2025-06-22 13:15:16,040 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:16,040 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [366026]
INFO:     Started server process [366147]
INFO:     Waiting for application startup.
Приложение запускается...
2025-06-22 13:15:30,659 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:15:30,659 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("users")
2025-06-22 13:15:30,660 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:30,661 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("files")
2025-06-22 13:15:30,661 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:30,662 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("user_sessions")
2025-06-22 13:15:30,662 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:30,663 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'src/main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [366147]
INFO:     Started server process [366354]
INFO:     Waiting for application startup.
Приложение запускается...
2025-06-22 13:15:58,075 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-06-22 13:15:58,075 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("users")
2025-06-22 13:15:58,075 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:58,076 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("files")
2025-06-22 13:15:58,076 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:58,077 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("user_sessions")
2025-06-22 13:15:58,077 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-06-22 13:15:58,078 INFO sqlalchemy.engine.Engine COMMIT
INFO:     Application startup complete.
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [366354]
INFO:     Stopping reloader process [270136]
/home/<USER>/miniconda3/lib/python3.13/multiprocessing/resource_tracker.py:277: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown: {'/mp-jgy2v_2v'}
  warnings.warn(
