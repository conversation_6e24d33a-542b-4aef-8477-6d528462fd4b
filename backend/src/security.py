import os
import hmac
import hashlib
import json
from urllib.parse import parse_qsl, unquote
from typing import Optional, Dict, Any

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTP<PERSON>earer
from sqlalchemy.ext.asyncio import AsyncSession

from . import crud, models
from .database import get_db

oauth2_scheme = HTTPBearer()
BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")

def validate_init_data(init_data: str, bot_token: str) -> Optional[Dict[str, Any]]:
    """
    Валидирует initData, полученные от Telegram Web App.
    Возвращает словарь с данными пользователя, если валидация прошла, иначе None.
    
    Args:
        init_data: Строка initData от Telegram Web App
        bot_token: Токен бота
        
    Returns:
        Словарь с данными пользователя или None при ошибке валидации
    """
    try:
        print(f"🔍 Validating initData: {init_data[:100]}...")
        
        # DEBUG: Временная заглушка для отладки
        if init_data == 'debug_mode_fake_init_data':
            print("DEBUG: Using fake initData for testing")
            return {
                'id': 12345,
                'first_name': 'Константин',
                'username': 'debug_user'
            }
        
        # Парсим строку initData
        parsed_data = dict(parse_qsl(init_data))
        print(f"📊 Parsed data keys: {list(parsed_data.keys())}")
        
        # Извлекаем хэш для проверки
        received_hash = parsed_data.pop('hash', None)
        if not received_hash:
            print("❌ No hash found in initData")
            return None
        
        print(f"🔐 Hash found: {received_hash[:20]}...")
        
        # Создаем строку для проверки подписи (формат: key1=value1\nkey2=value2...)
        data_check_string = '\n'.join(
            f"{key}={value}" for key, value in sorted(parsed_data.items())
        )
        
        # Генерируем секретный ключ из токена бота
        secret_key = hmac.new(
            b"WebAppData", 
            bot_token.encode(), 
            hashlib.sha256
        ).digest()
        
        # Вычисляем ожидаемый хэш
        calculated_hash = hmac.new(
            secret_key,
            data_check_string.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Сравниваем хэши безопасным способом
        if not hmac.compare_digest(calculated_hash, received_hash):
            print(f"❌ Hash mismatch! Expected: {calculated_hash[:20]}..., Got: {received_hash[:20]}...")
            return None
        
        print("✅ Hash validation passed!")
        
        # Если валидация прошла, парсим данные пользователя
        user_data = {}
        if 'user' in parsed_data:
            user_json = unquote(parsed_data['user'])
            user_data = json.loads(user_json)
        
        # Добавляем auth_date если есть
        if 'auth_date' in parsed_data:
            user_data['auth_date'] = int(parsed_data['auth_date'])
            
        return user_data
        
    except Exception as e:
        print(f"Ошибка валидации initData: {e}")
        return None

async def get_current_active_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_db)
) -> models.User:
    """
    Зависимость, которая валидирует initData и возвращает
    активного пользователя из базы данных.
    """
    if not BOT_TOKEN:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, 
            detail="Bot token not configured on server"
        )

    print(f"🔐 Attempting auth with token: {token.credentials[:100]}...")
    print(f"🔑 Bot token available: {BOT_TOKEN is not None}")
    
    user_data_outer = validate_init_data(init_data=token.credentials, bot_token=BOT_TOKEN)
    if not user_data_outer:
        print(f"❌ Auth validation failed")
        print(f"📝 Token sample: {token.credentials[:200]}...")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    
    print(f"✅ Auth successful for user: {user_data_outer.get('id', 'Unknown')}")
    
    # Получаем данные пользователя из корректной структуры
    user_data = user_data_outer  # После validate_init_data уже возвращается правильная структура
    print(f"👤 Creating/getting user with data: {user_data}")

    try:
        db_user = await crud.get_or_create_user(db=db, user_data=user_data)
        if db_user is None:
            print("❌ Failed to create/get user from database")
            raise HTTPException(status_code=404, detail="User not found")
            
        print(f"✅ Successfully got user: {db_user.telegram_id}")
        return db_user
    except Exception as e:
        print(f"❌ Database error in get_current_active_user: {e}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")