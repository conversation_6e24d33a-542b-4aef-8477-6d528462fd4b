import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from . import models, schemas

async def get_or_create_user(db: AsyncSession, user_data: dict) -> models.User:
    """
    Находит пользователя по telegram_id или создает нового, если он не существует.
    """
    telegram_id = user_data.get("id")
    
    # 1. Пытаемся найти пользователя
    result = await db.execute(select(models.User).where(models.User.telegram_id == telegram_id))
    db_user = result.scalar_one_or_none()
    
    # 2. Если нашли - возвращаем его
    if db_user:
        return db_user
    
    # 3. Если не нашли - создаем нового с обработкой race condition
    try:
        new_user_data = schemas.UserCreate(
            telegram_id=telegram_id,
            username=user_data.get("username"),
            first_name=user_data.get("first_name")
        )
        db_user = models.User(**new_user_data.model_dump())
        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)  # Обновляем объект, чтобы получить id из БД
        return db_user
    except Exception as e:
        # В случае ошибки (например, конкурентное создание), пытаемся найти еще раз
        await db.rollback()
        result = await db.execute(select(models.User).where(models.User.telegram_id == telegram_id))
        db_user = result.scalar_one_or_none()
        if db_user:
            return db_user
        else:
            raise e

# Функция для получения пользователя по telegram_id
async def get_user_by_telegram_id(db: AsyncSession, telegram_id: int):
    """Получить пользователя по telegram_id"""
    result = await db.execute(select(models.User).where(models.User.telegram_id == telegram_id))
    return result.scalar_one_or_none()

# Функция для создания пользователя
async def create_user(db: AsyncSession, user: schemas.UserCreate):
    """Создать нового пользователя"""
    db_user = models.User(
        telegram_id=user.telegram_id,
        username=user.username,
        first_name=user.first_name
    )
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    return db_user

# Функция для получения файлов пользователя
async def get_user_files(db: AsyncSession, user_id: str):
    """Получить все файлы пользователя"""
    result = await db.execute(
        select(models.File).where(models.File.owner_id == user_id).order_by(models.File.created_at.desc())
    )
    return result.scalars().all()

# Функция для подсчета общего использованного пользователем места
async def get_user_total_file_size(db: AsyncSession, user_id: str) -> int:
    """Получить общий размер всех файлов пользователя в байтах"""
    result = await db.execute(
        select(func.coalesce(func.sum(models.File.file_size), 0)).where(models.File.owner_id == user_id)
    )
    return result.scalar()

# Функция для получения файла по ID
async def get_file_by_id(db: AsyncSession, file_id: str, user_id: str = None):
    """Получить файл по ID (с опциональной проверкой владельца)"""
    query = select(models.File).where(models.File.id == file_id)
    if user_id:
        query = query.where(models.File.owner_id == user_id)
    
    result = await db.execute(query)
    return result.scalar_one_or_none()

# Функция для создания файла
async def create_file(db: AsyncSession, file: schemas.FileCreate, user_id: str):
    """Создать новый файл"""
    db_file = models.File(
        filename=file.filename,
        original_filename=file.original_filename,
        file_size=file.file_size,
        owner_id=user_id
    )
    db.add(db_file)
    await db.commit()
    await db.refresh(db_file)
    return db_file

# Функция для обновления статуса файла
async def update_file_status(db: AsyncSession, file_id: str, status: str):
    """Обновить статус файла"""
    result = await db.execute(select(models.File).where(models.File.id == file_id))
    db_file = result.scalar_one_or_none()
    if db_file:
        db_file.status = status
        await db.commit()
        await db.refresh(db_file)
    return db_file

# Функции для работы с сессиями пользователей
async def create_user_session(db: AsyncSession, user_id: str):
    """Создать новую сессию пользователя (запись входа)"""
    db_session = models.UserSession(user_id=user_id)
    db.add(db_session)
    await db.commit()
    await db.refresh(db_session)
    return db_session

async def get_user_session_count(db: AsyncSession, user_id: str) -> int:
    """Получить количество входов пользователя в систему"""
    result = await db.execute(
        select(func.count(models.UserSession.id)).where(models.UserSession.user_id == user_id)
    )
    return result.scalar()

async def is_first_time_user(db: AsyncSession, user_id: str) -> bool:
    """Проверить, первый ли раз пользователь заходит в приложение"""
    try:
        session_count = await get_user_session_count(db, user_id)
        print(f"🔢 User {user_id} has {session_count} sessions")
        return session_count == 0
    except Exception as e:
        print(f"❌ Error checking first time user: {e}")
        # Если ошибка (например, таблица не существует), считаем что это не первый раз
        return False