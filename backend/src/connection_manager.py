import asyncio
import json
import logging
from typing import Dict, List, Optional, Callable, Any
from fastapi import WebSocket
from pydantic import BaseModel
import threading

logger = logging.getLogger(__name__)

class WebSocketMessage(BaseModel):
    """Стандартизированный формат сообщений WebSocket"""
    event_type: str
    payload: Dict[str, Any]
    timestamp: Optional[str] = None
    
    def to_json(self) -> str:
        """Сериализация в JSON строку"""
        if not self.timestamp:
            from datetime import datetime
            self.timestamp = datetime.utcnow().isoformat()
        return self.model_dump_json()

class ConnectionManager:
    """
    Централизованный менеджер WebSocket соединений.
    Реализован как синглтон для обеспечения единой точки доступа.
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        # Словарь: user_id -> список активных WebSocket соединений
        self.active_connections: Dict[str, List[WebSocket]] = {}
        # Словарь: WebSocket -> user_id для быстрого поиска при отключении
        self.connection_to_user: Dict[WebSocket, str] = {}
        # Блокировка для потокобезопасности
        self._connections_lock = asyncio.Lock()
        self._initialized = True
        
        logger.info("ConnectionManager инициализирован")
    
    async def connect(self, websocket: WebSocket, user_id: str):
        """
        Регистрирует новое WebSocket соединение для пользователя
        
        Args:
            websocket: WebSocket соединение
            user_id: Идентификатор пользователя
        """
        await websocket.accept()
        
        async with self._connections_lock:
            if user_id not in self.active_connections:
                self.active_connections[user_id] = []
            
            self.active_connections[user_id].append(websocket)
            self.connection_to_user[websocket] = user_id
            
        logger.info(f"Новое WebSocket соединение для пользователя {user_id}. "
                   f"Всего соединений: {len(self.active_connections[user_id])}")
        
        # Отправляем приветственное сообщение
        await self.send_to_user(user_id, WebSocketMessage(
            event_type="connection_established",
            payload={
                "message": "Соединение установлено",
                "user_id": user_id,
                "total_connections": len(self.active_connections[user_id])
            }
        ))
    
    async def disconnect(self, websocket: WebSocket):
        """
        Удаляет WebSocket соединение из менеджера
        
        Args:
            websocket: WebSocket соединение для удаления
        """
        async with self._connections_lock:
            user_id = self.connection_to_user.get(websocket)
            if not user_id:
                logger.warning("Попытка отключить несуществующее WebSocket соединение")
                return
            
            # Удаляем соединение из списка пользователя
            if user_id in self.active_connections:
                try:
                    self.active_connections[user_id].remove(websocket)
                    # Если у пользователя больше нет соединений, удаляем его из словаря
                    if not self.active_connections[user_id]:
                        del self.active_connections[user_id]
                except ValueError:
                    logger.warning(f"WebSocket соединение не найдено в списке пользователя {user_id}")
            
            # Удаляем обратную ссылку
            del self.connection_to_user[websocket]
            
        logger.info(f"WebSocket соединение отключено для пользователя {user_id}")
    
    async def send_to_user(self, user_id: str, message: WebSocketMessage):
        """
        Отправляет сообщение всем активным соединениям пользователя
        
        Args:
            user_id: Идентификатор пользователя
            message: Сообщение для отправки
        """
        async with self._connections_lock:
            connections = self.active_connections.get(user_id, [])
            if not connections:
                logger.debug(f"Нет активных соединений для пользователя {user_id}")
                return
            
            message_json = message.to_json()
            disconnected_connections = []
            
            # Отправляем сообщение всем соединениям пользователя
            for connection in connections:
                try:
                    await connection.send_text(message_json)
                except Exception as e:
                    logger.warning(f"Ошибка отправки сообщения через WebSocket: {e}")
                    disconnected_connections.append(connection)
            
            # Удаляем неработающие соединения
            for connection in disconnected_connections:
                await self._remove_connection_unsafe(connection, user_id)
            
        logger.debug(f"Сообщение отправлено пользователю {user_id}: {message.event_type}")
    
    async def send_to_all(self, message: WebSocketMessage):
        """
        Отправляет сообщение всем активным соединениям
        
        Args:
            message: Сообщение для отправки
        """
        async with self._connections_lock:
            all_connections = []
            for connections in self.active_connections.values():
                all_connections.extend(connections)
            
            if not all_connections:
                logger.debug("Нет активных WebSocket соединений")
                return
            
            message_json = message.to_json()
            
            for connection in all_connections:
                try:
                    await connection.send_text(message_json)
                except Exception as e:
                    logger.warning(f"Ошибка отправки broadcast сообщения: {e}")
                    # Отключение проблемных соединений обработается при следующем обращении
            
        logger.debug(f"Broadcast сообщение отправлено {len(all_connections)} соединениям: {message.event_type}")
    
    async def _remove_connection_unsafe(self, websocket: WebSocket, user_id: str):
        """
        Внутренний метод для удаления соединения без блокировки
        ВНИМАНИЕ: Должен вызываться только внутри _connections_lock
        """
        try:
            self.active_connections[user_id].remove(websocket)
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
            del self.connection_to_user[websocket]
        except (ValueError, KeyError):
            pass  # Соединение уже удалено
    
    def get_user_connections_count(self, user_id: str) -> int:
        """
        Возвращает количество активных соединений пользователя
        
        Args:
            user_id: Идентификатор пользователя
            
        Returns:
            Количество активных соединений
        """
        return len(self.active_connections.get(user_id, []))
    
    def get_total_connections_count(self) -> int:
        """
        Возвращает общее количество активных соединений
        
        Returns:
            Общее количество соединений
        """
        return sum(len(connections) for connections in self.active_connections.values())
    
    def get_connected_users_count(self) -> int:
        """
        Возвращает количество пользователей с активными соединениями
        
        Returns:
            Количество подключенных пользователей
        """
        return len(self.active_connections)

# Создаем глобальный экземпляр менеджера соединений
connection_manager = ConnectionManager()

# Типы для progress callback системы
ProgressCallback = Callable[[str, str, Optional[Dict[str, Any]]], None]

def create_websocket_progress_callback(user_id: str):
    """
    Фабричная функция для создания progress callback, который отправляет обновления через WebSocket
    
    Args:
        user_id: Идентификатор пользователя
        
    Returns:
        Callback функция для отправки обновлений прогресса
    """
    async def progress_callback(file_id: str, status: str, details: Optional[Dict[str, Any]] = None):
        """
        Callback функция для отправки обновлений прогресса
        
        Args:
            file_id: Идентификатор файла
            status: Новый статус
            details: Дополнительные детали
        """
        message = WebSocketMessage(
            event_type="status_update",
            payload={
                "file_id": file_id,
                "new_status": status,
                "details": details or {}
            }
        )
        await connection_manager.send_to_user(user_id, message)
    
    return progress_callback