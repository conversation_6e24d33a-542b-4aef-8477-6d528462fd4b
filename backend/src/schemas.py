import uuid
from datetime import datetime
from pydantic import BaseModel, ConfigDict

# Схемы для Файлов
class FileBase(BaseModel):
    filename: str

class FileCreate(BaseModel):
    filename: str
    original_filename: str
    file_size: int

class FileSchema(FileBase):
    id: uuid.UUID
    original_filename: str
    file_size: int
    status: str
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

# Схемы для Пользователей
class UserBase(BaseModel):
    telegram_id: int
    username: str | None = None
    first_name: str | None = None

class UserCreate(UserBase):
    pass

class UserSchema(UserBase):
    id: uuid.UUID
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)