import asyncio
import time
import os
import random
import string
from typing import Optional, Callable, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
try:
    from . import crud
    from .database import AsyncSessionLocal
except ImportError:
    # Fallback для прямого запуска
    import crud
    from database import AsyncSessionLocal
import google.generativeai as genai
from faster_whisper import WhisperModel
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

# Константы
MAX_FILE_SIZE = 300 * 1024 * 1024  # 300 МБ в байтах
# USER_QUOTA = 1024 * 1024 * 1024   # 1 ГБ в байтах - ОТКЛЮЧЕНО ДЛЯ ТЕСТИРОВАНИЯ

# Тип для progress callback функций
ProgressCallback = Callable[[str, str, Optional[Dict[str, Any]]], None]

# Инициализация Gemini API
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)

# Пул Whisper моделей для многопользовательской работы
import threading
whisper_models = {}
whisper_lock = threading.Lock()

async def update_status_in_db(file_id: str, status: str):
    """Обновление статуса файла в БД для polling"""
    try:
        async with AsyncSessionLocal() as db:
            await crud.update_file_status(db, file_id, status)
    except Exception as e:
        print(f"⚠️ Ошибка обновления статуса в БД: {e}")

def get_whisper_model():
    """Потокобезопасная инициализация Whisper модели для каждого потока"""
    thread_id = threading.get_ident()
    
    with whisper_lock:
        if thread_id not in whisper_models:
            print(f"🤖 Загружаем модель Whisper для потока {thread_id}...")
            # Используем модель medium для высокого качества медицинской транскрипции
            whisper_models[thread_id] = WhisperModel("medium", device="cpu", compute_type="int8")
            print(f"✅ Модель Whisper загружена для потока {thread_id}")
    
    return whisper_models[thread_id]

def generate_filename() -> str:
    """Генерация случайного имени файла из 8 символов + .txt"""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=8)) + '.txt'

async def transcribe_audio(file_path: str, progress_callback: Optional[ProgressCallback] = None, file_id: str = "") -> str:
    """Транскрипция аудио через Whisper с отслеживанием прогресса"""
    try:
        status_text = "Загрузка модели Whisper... 20%"
        await update_status_in_db(file_id, status_text)
        if progress_callback:
            await progress_callback(file_id, status_text, {"stage": "whisper_loading", "progress": 20})
        
        model = get_whisper_model()
        
        status_text = "Анализ аудиофайла... 25%"
        await update_status_in_db(file_id, status_text)
        if progress_callback:
            await progress_callback(file_id, status_text, {"stage": "audio_analysis", "progress": 25})
        
        # Переменные для отслеживания прогресса
        transcription_parts = []
        total_duration = None
        processed_time = 0
        
        def _transcribe():
            nonlocal total_duration, processed_time
            
            segments, info = model.transcribe(
                file_path,
                language="ru",
                word_timestamps=True,
                vad_filter=True,
                vad_parameters=dict(min_silence_duration_ms=500),
                beam_size=5,
                best_of=5,
                temperature=0.0,
                condition_on_previous_text=False
            )
            
            # Получаем общую длительность аудио
            total_duration = info.duration
            print(f"🎵 Длительность аудио: {total_duration:.1f} секунд")
            
            # Обрабатываем сегменты с отслеживанием прогресса
            for segment in segments:
                transcription_parts.append(segment.text)
                processed_time = segment.end
                
                # Вычисляем прогресс (от 30% до 55%)
                if total_duration and total_duration > 0:
                    progress_percent = 30 + int((processed_time / total_duration) * 25)
                    progress_percent = min(progress_percent, 55)  # Максимум 55%
                    
                    # Создаем задачу для callback (неблокирующий вызов)
                    if progress_callback:
                        status_text = f"Расшифровка: {progress_percent}%"
                        asyncio.create_task(progress_callback(
                            file_id, 
                            status_text,
                            {
                                "stage": "transcription_progress", 
                                "progress": progress_percent
                            }
                        ))
                        # Обновляем статус в БД для polling
                        asyncio.create_task(update_status_in_db(file_id, status_text))
            
            return " ".join(transcription_parts)
        
        # Выполняем транскрипцию в отдельном потоке
        transcription = await asyncio.to_thread(_transcribe)
        
        status_text = "Транскрипция завершена 60%"
        await update_status_in_db(file_id, status_text)
        if progress_callback:
            await progress_callback(file_id, status_text, {
                "stage": "transcription_done", 
                "progress": 60
            })
        
        print(f"📝 Транскрипция завершена: {len(transcription_parts)} сегментов, {len(transcription)} символов")
        
        # Сохраняем транскрипцию в текстовый файл
        transcription_filename = generate_filename()
        transcription_path = f"storage/transcriptions/{transcription_filename}"
        
        with open(transcription_path, 'w', encoding='utf-8') as f:
            f.write(transcription)
        
        print(f"💾 Транскрипция сохранена в файл: {transcription_filename}")
        
        return transcription
        
    except Exception as e:
        print(f"❌ Ошибка транскрипции: {e}")
        raise Exception(f"Ошибка при транскрипции аудио: {str(e)}")

async def generate_summary(text: str, progress_callback: Optional[ProgressCallback] = None, file_id: str = "") -> str:
    """Создание конспекта через Gemini"""
    try:
        if not GEMINI_API_KEY:
            raise Exception("GEMINI_API_KEY не настроен")
        
        status_text = "Создание конспекта (Gemini)... 85%"
        await update_status_in_db(file_id, status_text)
        if progress_callback:
            await progress_callback(file_id, status_text, {"stage": "gemini_processing", "progress": 85})
        
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        
        prompt = f"""
Создай подробный конспект по следующему тексту на русском языке. 
Структурируй материал с использованием markdown разметки:

1. Используй заголовки разных уровней (# ## ###)
2. Выдели ключевые моменты с помощью списков
3. Добавь эмодзи для лучшего восприятия
4. Сделай текст структурированным и легко читаемым
5. Сохрани все важные детали из исходного текста

Текст для обработки:
{text}
"""
        
        def _generate():
            response = model.generate_content(prompt)
            return response.text
        
        summary = await asyncio.to_thread(_generate)
        
        status_text = "Конспект создан 95%"
        await update_status_in_db(file_id, status_text)
        if progress_callback:
            await progress_callback(file_id, status_text, {"stage": "gemini_done", "progress": 95})
        
        return summary
        
    except Exception as e:
        print(f"❌ Ошибка генерации конспекта: {e}")
        raise Exception(f"Ошибка при создании конспекта: {str(e)}")

async def process_audio_file(file_id: str, progress_callback: Optional[ProgressCallback] = None):
    """
    Фоновая обработка аудиофайла.
    Включает транскрипцию через Whisper и суммаризацию через Gemini.
    
    Args:
        file_id: Идентификатор файла для обработки
        progress_callback: Callback функция для отправки обновлений прогресса
    """
    async with AsyncSessionLocal() as db:
        try:
            print(f"🎵 Начинаем обработку файла {file_id}")
            
            # Отправляем начальный статус
            status = "Подготовка к обработке... 5%"
            await crud.update_file_status(db, file_id, status)
            if progress_callback:
                await progress_callback(file_id, status, {"stage": "initialization", "progress": 5})
            
            # Получаем информацию о файле из БД
            from sqlalchemy import select
            result = await db.execute(
                select(crud.models.File).where(crud.models.File.id == file_id)
            )
            db_file = result.scalar_one_or_none()
            
            if not db_file:
                raise Exception(f"Файл {file_id} не найден в базе данных")
            
            # Путь к аудиофайлу
            audio_file_path = f"storage/audio_uploads/{db_file.filename}"
            
            if not os.path.exists(audio_file_path):
                raise Exception(f"Аудиофайл не найден: {audio_file_path}")
            
            if progress_callback:
                await progress_callback(file_id, "Файл найден, начинаем обработку... 10%", {"stage": "file_found", "progress": 10})
            await crud.update_file_status(db, file_id, "Файл найден, начинаем обработку... 10%")
            
            # 1. Транскрипция через Whisper
            transcription = await transcribe_audio(audio_file_path, progress_callback, file_id)
            
            if not transcription.strip():
                raise Exception("Не удалось получить транскрипцию из аудиофайла")
            
            print(f"📝 Получена транскрипция длиной {len(transcription)} символов")
            
            # 2. Создание конспекта через Gemini
            summary = await generate_summary(transcription, progress_callback, file_id)
            
            if not summary.strip():
                raise Exception("Не удалось создать конспект")
            
            print(f"📋 Создан конспект длиной {len(summary)} символов")
            
            # Обновляем запись в БД с результатом
            db_file.status = "Готово"
            db_file.summary_text = summary
            await db.commit()
            await db.refresh(db_file)
            
            # Отправляем финальное уведомление
            if progress_callback:
                await progress_callback(file_id, "Готово", {
                    "stage": "completed", 
                    "progress": 100,
                    "summary_ready": True
                })
            
            print(f"✅ Файл {file_id} успешно обработан!")
            
            # Автоудаление аудиофайла после обработки
            try:
                audio_file_path = f"storage/audio_uploads/{db_file.filename}"
                if os.path.exists(audio_file_path):
                    os.remove(audio_file_path)
                    print(f"🗑️ Аудиофайл {db_file.filename} автоматически удален")
                else:
                    print(f"⚠️ Аудиофайл {audio_file_path} не найден для удаления")
            except Exception as delete_error:
                print(f"❌ Ошибка при удалении аудиофайла: {delete_error}")
                # Не прерываем процесс из-за ошибки удаления
                
        except Exception as e:
            error_msg = f"Ошибка: {str(e)}"
            print(f"❌ Ошибка при обработке файла {file_id}: {e}")
            await crud.update_file_status(db, file_id, error_msg)
            
            # Отправляем уведомление об ошибке
            if progress_callback:
                await progress_callback(file_id, error_msg, {
                    "stage": "error",
                    "error_type": "processing_error",
                    "error_details": str(e)
                })

def validate_file_size(file_size: int) -> bool:
    """Проверка размера файла"""
    return file_size <= MAX_FILE_SIZE

async def validate_user_quota(db: AsyncSession, user_id: str, new_file_size: int) -> bool:
    """Проверка квоты пользователя - ОТКЛЮЧЕНО ДЛЯ ТЕСТИРОВАНИЯ"""
    # current_usage = await crud.get_user_total_file_size(db, user_id)
    # return (current_usage + new_file_size) <= USER_QUOTA
    return True  # Всегда разрешаем загрузку

def get_max_file_size_mb() -> int:
    """Получить максимальный размер файла в МБ"""
    return MAX_FILE_SIZE // (1024 * 1024)

def get_user_quota_gb() -> int:
    """Получить квоту пользователя в ГБ - ОТКЛЮЧЕНО ДЛЯ ТЕСТИРОВАНИЯ"""
    # return USER_QUOTA // (1024 * 1024 * 1024)
    return 999  # Возвращаем большое значение для тестирования