import os
import uuid
import aiofiles
from pathlib import Path
from contextlib import asynccontextmanager
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException, status, APIRouter, UploadFile, File, BackgroundTasks
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict
import json

from .database import get_db, create_db_and_tables
from .security import validate_init_data, get_current_active_user
from . import crud, schemas, models
from .processing import process_audio_file, validate_file_size, validate_user_quota, get_max_file_size_mb, transcribe_audio
from .connection_manager import connection_manager, create_websocket_progress_callback

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Код, который выполнится при старте приложения
    print("Приложение запускается...")
    await create_db_and_tables()
    yield
    # Код, который выполнится при остановке приложения
    print("Приложение останавливается...")

# Инициализация FastAPI приложения
app = FastAPI(
    title="AI-Конспектор Backend",
    description="Backend для Telegram Web App по обработке аудио файлов",
    version="1.0.0",
    lifespan=lifespan
)

security = HTTPBearer()

# Создаем роутер для API-эндпоинтов
api_router = APIRouter(prefix="/api/v1")

@api_router.get("/users/me", response_model=schemas.UserSchema)
async def read_users_me(
    current_user: models.User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Возвращает информацию о текущем аутентифицированном пользователе.
    """
    print(f"✅ /users/me called for user: {current_user.telegram_id}")
    
    # Пытаемся создать сессию (без критичной ошибки)
    try:
        await crud.create_user_session(db, str(current_user.id))
        print(f"📊 Session created for user {current_user.telegram_id}")
    except Exception as e:
        print(f"⚠️ Could not create session: {e} (non-critical)")
    
    return current_user

@api_router.get("/user-status")
async def get_user_status(
    current_user: models.User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Получить статус пользователя - новый ли он и есть ли у него файлы"""
    print(f"🔍 Getting user status for user: {current_user.telegram_id}")
    
    try:
        # Получаем файлы пользователя
        files = await crud.get_user_files(db, str(current_user.id))
        print(f"📁 User {current_user.telegram_id} has {len(files)} files")
        
        # Проверяем сессии для определения "первый раз"
        try:
            session_count = await crud.get_user_session_count(db, str(current_user.id))
            is_first_time = session_count <= 1  # Считаем первым если 0 или 1 сессия
            print(f"🆕 User {current_user.telegram_id} has {session_count} sessions, first_time: {is_first_time}")
        except Exception as e:
            # Fallback: если проблемы с сессиями, используем количество файлов
            is_first_time = len(files) == 0
            print(f"⚠️ Session check failed, using file count: {is_first_time}")
        
        result = {
            "is_first_time": is_first_time,
            "has_files": len(files) > 0,
            "files_count": len(files),
            "user": {
                "id": str(current_user.id),
                "telegram_id": current_user.telegram_id,
                "username": current_user.username,
                "first_name": current_user.first_name or current_user.username
            }
        }
        
        print(f"📊 Returning user status: {result}")
        return result
        
    except Exception as e:
        print(f"❌ Error in get_user_status: {e}")
        # Emergency fallback
        return {
            "is_first_time": True,
            "has_files": False,
            "files_count": 0,
            "user": {
                "id": str(current_user.id),
                "telegram_id": current_user.telegram_id,
                "username": current_user.username or "unknown",
                "first_name": current_user.first_name or current_user.username or "unknown"
            }
        }

@api_router.get("/history", response_model=List[schemas.FileSchema])
async def get_history(
    current_user: models.User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Получить историю файлов пользователя"""
    print(f"📁 /history called for user: {current_user.telegram_id}")
    try:
        files = await crud.get_user_files(db, str(current_user.id))
        print(f"📁 Returning {len(files)} files for user {current_user.telegram_id}")
        return files
    except Exception as e:
        print(f"❌ Error getting files: {e}")
        return []

@api_router.post("/upload", status_code=status.HTTP_202_ACCEPTED, response_model=schemas.FileSchema)
async def upload_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    current_user: models.User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Загрузить новый файл для обработки"""
    
    # 1. Проверяем размер файла
    if not file.size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Файл пустой"
        )
    
    if not validate_file_size(file.size):
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"Размер файла превышает лимит в {get_max_file_size_mb()} МБ"
        )
    
    # 2. Проверяем квоту пользователя
    if not await validate_user_quota(db, str(current_user.id), file.size):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Превышена квота пользователя (1 ГБ)"
        )
    
    # 3. Проверяем тип файла (базовая проверка)
    if not file.content_type or not file.content_type.startswith(('audio/', 'video/')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Поддерживаются только аудио и видео файлы"
        )
    
    # 4. Генерируем уникальное имя файла
    file_extension = Path(file.filename or "audio.mp3").suffix
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = Path("storage/audio_uploads") / unique_filename
    
    # 5. Создаем директорию если её нет
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 6. Сохраняем файл
    try:
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка сохранения файла: {str(e)}"
        )
    
    # 7. Создаем запись в БД
    file_create = schemas.FileCreate(
        filename=unique_filename,
        original_filename=file.filename or "unknown",
        file_size=file.size
    )
    
    db_file = await crud.create_file(db, file_create, str(current_user.id))
    
    # 8. Создаем progress callback для WebSocket уведомлений
    progress_callback = create_websocket_progress_callback(str(current_user.id))
    
    # 9. Запускаем обработку в фоне с callback
    background_tasks.add_task(process_audio_file, str(db_file.id), progress_callback)
    
    # 10. Отправляем немедленное уведомление о начале обработки
    from .connection_manager import WebSocketMessage
    initial_message = WebSocketMessage(
        event_type="file_uploaded",
        payload={
            "file_id": str(db_file.id),
            "filename": db_file.original_filename,
            "status": "в очереди",
            "message": "Файл успешно загружен и добавлен в очередь обработки"
        }
    )
    await connection_manager.send_to_user(str(current_user.id), initial_message)
    
    return db_file

@api_router.get("/summary/{summary_id}")
async def get_summary(
    summary_id: str,
    current_user: models.User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Получить текст конспекта по ID в формате markdown"""
    try:
        # 1. Получаем файл и проверяем что он принадлежит пользователю
        db_file = await crud.get_file_by_id(db, summary_id, str(current_user.id))
        
        if not db_file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Файл не найден или не принадлежит пользователю"
            )
        
        # 2. Проверяем что файл обработан
        if db_file.status != "Готово":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Файл еще не обработан. Текущий статус: {db_file.status}"
            )
        
        # 3. Проверяем что конспект существует
        if not db_file.summary_text:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Конспект не найден для данного файла"
            )
        
        # 4. Возвращаем markdown текст
        from fastapi.responses import PlainTextResponse
        return PlainTextResponse(
            content=db_file.summary_text,
            media_type="text/markdown; charset=utf-8"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in get_summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка при получении конспекта"
        )

@api_router.post("/transcribe", status_code=status.HTTP_200_OK)
async def transcribe_only(
    file: UploadFile = File(...),
    current_user: models.User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Транскрибировать аудио файл и вернуть имя .txt файла"""
    
    # 1. Проверяем размер файла
    if not file.size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Файл пустой"
        )
    
    if not validate_file_size(file.size):
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"Размер файла превышает лимит в {get_max_file_size_mb()} МБ"
        )
    
    # 2. Проверяем тип файла
    if not file.content_type or not file.content_type.startswith(('audio/', 'video/')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Поддерживаются только аудио и видео файлы"
        )
    
    # 3. Генерируем уникальное имя файла
    file_extension = Path(file.filename or "audio.mp3").suffix
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = Path("storage/audio_uploads") / unique_filename
    
    # 4. Создаем директорию если её нет
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 5. Сохраняем файл
    try:
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка сохранения файла: {str(e)}"
        )
    
    # 6. Выполняем транскрипцию
    try:
        await transcribe_audio(str(file_path))
        
        # 7. Удаляем временный аудио файл
        try:
            os.remove(file_path)
        except Exception as e:
            print(f"⚠️ Не удалось удалить временный файл: {e}")
        
        # 8. Ищем созданный файл транскрипции (последний .txt в папке)
        transcription_dir = Path("storage/transcriptions")
        txt_files = list(transcription_dir.glob("*.txt"))
        if not txt_files:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Ошибка: файл транскрипции не был создан"
            )
        
        # Берем последний созданный файл
        latest_file = max(txt_files, key=lambda x: x.stat().st_mtime)
        
        return {
            "transcription_filename": latest_file.name,
            "message": "Транскрипция успешно выполнена"
        }
        
    except Exception as e:
        # Удаляем временный файл в случае ошибки
        try:
            os.remove(file_path)
        except:
            pass
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка транскрипции: {str(e)}"
        )

# Базовые эндпоинты
@app.get("/")
async def root():
    return {"message": "AI-Конспектор Backend is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@api_router.get("/debug/test")
async def debug_test():
    """Тестовый эндпоинт без авторизации для отладки"""
    return {
        "status": "working",
        "message": "Backend is accessible",
        "timestamp": "2025-06-22T11:30:00"
    }

# Подключаем роутер к основному приложению
app.include_router(api_router)

# WebSocket эндпоинт для real-time обновлений
@app.websocket("/ws/status")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint для получения real-time обновлений о статусе обработки файлов
    
    Query Parameters:
        initData: Строка initData от Telegram Web App для аутентификации
    """
    try:
        # 1. Аутентификация через initData
        init_data = websocket.query_params.get("initData")
        if not init_data:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Missing initData")
            return
        
        # 2. Валидация initData
        bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        if not bot_token:
            await websocket.close(code=status.WS_1011_INTERNAL_ERROR, reason="Bot token not configured")
            return
        
        user_data = validate_init_data(init_data, bot_token)
        if not user_data:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid initData")
            return
        
        user_id = str(user_data.get("id"))
        if not user_id:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="User ID not found")
            return
        
        # 3. Регистрация соединения в менеджере
        await connection_manager.connect(websocket, user_id)
        
        try:
            # 4. Обработка входящих сообщений (ping/pong, heartbeat)
            while True:
                try:
                    # Ожидаем сообщения от клиента
                    message = await websocket.receive_text()
                    
                    # Простой ping/pong механизм
                    if message == "ping":
                        await websocket.send_text("pong")
                    
                except Exception as e:
                    print(f"Ошибка при получении сообщения от WebSocket: {e}")
                    break
                    
        except WebSocketDisconnect:
            # Нормальное отключение клиента
            pass
            
    except Exception as e:
        print(f"Ошибка в WebSocket endpoint: {e}")
        
    finally:
        # 5. Обязательная очистка соединения при любом исходе
        await connection_manager.disconnect(websocket)