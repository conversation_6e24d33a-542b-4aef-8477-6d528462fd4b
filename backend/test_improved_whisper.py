#!/usr/bin/env python3
"""
Тестирование улучшенного Whisper API с качественной моделью
"""
import asyncio
import aiohttp
import aiofiles
import os
import json

async def test_transcribe_with_improved_model(audio_file_path: str):
    """Тестирование улучшенной транскрипции"""
    print(f"🎵 Тестируем улучшенную транскрипцию: {audio_file_path}")
    
    if not os.path.exists(audio_file_path):
        print(f"❌ Файл не найден: {audio_file_path}")
        return False
    
    file_size = os.path.getsize(audio_file_path)
    print(f"📊 Размер файла: {file_size / (1024*1024):.2f} МБ")
    
    async with aiohttp.ClientSession() as session:
        try:
            # Читаем файл
            async with aiofiles.open(audio_file_path, 'rb') as f:
                file_content = await f.read()
            
            # Подготавливаем данные для отправки
            data = aiohttp.FormData()
            data.add_field('file', 
                          file_content, 
                          filename=os.path.basename(audio_file_path),
                          content_type='audio/m4a')
            
            # Отправляем запрос с фейковой авторизацией для тестирования
            headers = {
                'Authorization': 'Bearer debug_mode_fake_init_data'
            }
            
            print("📤 Отправляем файл на сервер с улучшенной моделью...")
            print("🤖 Используется модель: MEDIUM")
            print("⚙️ Параметры: beam_size=5, best_of=5, temperature=0.0")
            
            async with session.post("http://localhost:8001/api/v1/transcribe", 
                                  data=data, 
                                  headers=headers,
                                  timeout=aiohttp.ClientTimeout(total=600)) as response:
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Транскрипция успешна!")
                    print(f"📄 Результат: {result}")
                    
                    # Проверяем созданный файл транскрипции
                    transcription_file = result.get('transcription_filename')
                    if transcription_file:
                        transcription_path = f"storage/transcriptions/{transcription_file}"
                        if os.path.exists(transcription_path):
                            with open(transcription_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            print(f"📝 ПОЛНАЯ ТРАНСКРИПЦИЯ ({len(content)} символов):")
                            print("=" * 80)
                            print(content)
                            print("=" * 80)
                            
                            # Анализ качества
                            print("\n🔍 АНАЛИЗ КАЧЕСТВА:")
                            words = content.split()
                            unique_words = set(words)
                            print(f"📊 Всего слов: {len(words)}")
                            print(f"📊 Уникальных слов: {len(unique_words)}")
                            print(f"📊 Коэффициент разнообразия: {len(unique_words)/len(words):.2f}")
                            
                            # Проверка на повторения
                            if len(unique_words) < len(words) * 0.3:
                                print("⚠️ ВНИМАНИЕ: Много повторений, возможно проблема с качеством")
                            else:
                                print("✅ Качество транскрипции выглядит хорошо")
                                
                        else:
                            print(f"⚠️ Файл транскрипции не найден: {transcription_path}")
                    
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Ошибка транскрипции. Статус: {response.status}")
                    print(f"📄 Детали ошибки: {error_text}")
                    return False
                    
        except asyncio.TimeoutError:
            print("❌ Таймаут запроса (превышено 10 минут)")
            return False
        except Exception as e:
            print(f"❌ Ошибка при транскрипции: {e}")
            return False

async def main():
    """Основная функция тестирования"""
    audio_file_path = "/home/<USER>/PROJ/diarizator/333.m4a"
    
    print("🚀 Тестирование УЛУЧШЕННОГО Whisper API")
    print("=" * 60)
    print("🔧 Улучшения:")
    print("   - Модель: MEDIUM (высокое качество для медицинского контента)")
    print("   - beam_size: 5")
    print("   - best_of: 5") 
    print("   - temperature: 0.0 (детерминированный результат)")
    print("   - condition_on_previous_text: False")
    print("=" * 60)
    
    # Проверка здоровья сервера
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:8001/health") as response:
                if response.status == 200:
                    print("✅ Сервер доступен")
                else:
                    print("❌ Сервер недоступен")
                    return
        except Exception:
            print("❌ Сервер не запущен")
            return
    
    print()
    
    # Тестирование улучшенной транскрипции
    await test_transcribe_with_improved_model(audio_file_path)
    
    print()
    print("🏁 Тестирование завершено!")

if __name__ == "__main__":
    asyncio.run(main())
