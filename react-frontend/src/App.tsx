import { useEffect, useState } from 'react';
import { AlertCircle } from 'lucide-react';
import { FileUpload } from './components/FileUpload';
import { useTelegramWebApp } from './hooks/useTelegramWebApp';
import { apiClient, type UserStatus } from './api/client';

function App() {
  const { isReady, initData, error: tgError } = useTelegramWebApp();
  const [userStatus, setUserStatus] = useState<UserStatus | null>(null);
  const [showWelcome, setShowWelcome] = useState(false);
  const [welcomeStep, setWelcomeStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  // const [debugInfo, setDebugInfo] = useState<string[]>([]);

  useEffect(() => {
    if (initData) {
      apiClient.setInitData(initData);
      loadUserStatus();
    } else if (isReady) {
      // Если нет initData (режим разработки), используем отладочный режим
      // addDebug('⚠️ No initData, using debug mode');
      apiClient.setInitData('debug_mode_fake_init_data');
      loadUserStatus();
    }
  }, [initData, isReady]);

  // const addDebug = (message: string) => {
  //   setDebugInfo(prev => [...prev.slice(-4), message]); // Показываем последние 5 сообщений
  // };

  const loadUserStatus = async () => {
    try {
      // addDebug('🔍 Loading user status...');
      
      // Сначала проверяем доступность API
      try {
        // addDebug('🧪 Testing API connectivity...');
        // const testResult = await apiClient.debugTest();
        // addDebug(`✅ API test: ${testResult.status}`);
      } catch (apiError: any) {
        // addDebug(`❌ API test failed: ${apiError.response?.status || 'network error'}`);
      }
      
      // Fallback: если /user-status не работает, используем /users/me и /history
      let status;
      try {
        status = await apiClient.getUserStatus();
      } catch (error: any) {
        if (error.response?.status === 404) {
          // addDebug('⚠️ /user-status not found, using fallback');
          // Fallback логика - по одному запросу для отладки
          try {
            // addDebug('📞 Calling /users/me...');
            const user = await apiClient.getUserInfo();
            // addDebug(`✅ Got user: ${user.username}`);
            
            // addDebug('📞 Calling /history...');
            const files = await apiClient.getHistory();
            // addDebug(`✅ Got ${files.length} files`);
            
            status = {
              is_first_time: files.length === 0, // Считаем первым разом если нет файлов
              has_files: files.length > 0,
              files_count: files.length,
              user: {
                id: user.id,
                telegram_id: user.telegram_id,
                username: user.username,
                first_name: user.username
              }
            };
          } catch (fallbackError: any) {
            // addDebug(`❌ Fallback error: ${fallbackError.response?.status || 'unknown'}`);
            // addDebug(`📝 Error details: ${fallbackError.message}`);
            throw fallbackError;
          }
        } else {
          // addDebug(`❌ getUserStatus error: ${error.response?.status || 'unknown'}`);
          throw error;
        }
      }
      
      // addDebug(`📊 User: ${status.user?.username || 'Unknown'}`);
      // addDebug(`📁 Files: ${status.files_count}, First: ${status.is_first_time}`);
      setUserStatus(status);
      
      // Показываем приветствие только для новых пользователей без файлов
      const shouldShow = status.is_first_time && !status.has_files;
      // addDebug(`🤔 Should show welcome: ${shouldShow}`);
      
      if (shouldShow) {
        // addDebug('👋 Showing welcome!');
        setShowWelcome(true);
        startWelcomeSequence();
      } else {
        // addDebug('⏭️ Skipping welcome');
      }
    } catch (error: any) {
      // addDebug(`❌ Error: ${error.message || 'Unknown'}`);
    } finally {
      setIsLoading(false);
    }
  };

  const startWelcomeSequence = () => {
    console.log('🎬 Starting welcome sequence...');
    // Первое сообщение
    setWelcomeStep(1);
    console.log('📝 Step 1: Welcome message');
    
    // Через 7 секунд показываем второе сообщение
    setTimeout(() => {
      console.log('📝 Step 2: Instructions message');
      setWelcomeStep(2);
      
      // Через еще 7 секунд убираем приветствие
      setTimeout(() => {
        console.log('✅ Welcome sequence complete');
        setShowWelcome(false);
      }, 7000);
    }, 7000);
  };

  const handleUploadSuccess = () => {
    // После загрузки убираем приветствие если оно показывалось
    setShowWelcome(false);
    console.log('File uploaded successfully');
  };

  if (tgError) {
    return (
      <div className="min-h-screen bg-tg-bg flex items-center justify-center p-4">
        <div className="tg-card max-w-md w-full text-center animate-bounce-in">
          <AlertCircle className="h-16 w-16 text-error-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">
            Ошибка инициализации
          </h2>
          <p className="text-hint mb-4">{tgError}</p>
          <p className="text-sm text-subtitle">
            Откройте приложение в Telegram
          </p>
        </div>
      </div>
    );
  }

  if (!isReady || !initData || isLoading) {
    return (
      <div className="min-h-screen bg-tg-bg flex items-center justify-center">
        <div className="flex flex-col items-center animate-fade-in">
          <div className="loading-spinner w-12 h-12 text-tg-link mb-4"></div>
          <p className="text-hint">Инициализация...</p>
        </div>
      </div>
    );
  }

  const renderWelcomeMessage = () => {
    console.log('🎨 Rendering welcome message:', { showWelcome, welcomeStep });
    
    if (!showWelcome) return null;

    const username = userStatus?.user?.first_name || userStatus?.user?.username || 'Друг';
    console.log('👤 Username for welcome:', username);
    
    return (
      <div className="absolute inset-0 bg-tg-bg flex items-center justify-center z-10">
        <div className="text-center px-8 animate-fade-in">
          {welcomeStep === 1 && (
            <div className="animate-fade-in">
              <h1 className="text-base font-bold text-tg-text mb-2">
                Привет, {username}! 👋
              </h1>
              <p className="text-base text-tg-text">
                Я - Конспектор.
              </p>
            </div>
          )}
          
          {welcomeStep === 2 && (
            <div className="animate-fade-in">
              <p className="text-base text-tg-text leading-relaxed">
                Пришли мне аудио, я его прослушаю и отправлю тебе конспект этой аудиозаписи! 🎧📝
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-tg-bg relative">
      {/* Debug info overlay - DISABLED
      {debugInfo.length > 0 && (
        <div className="fixed top-4 left-4 right-4 bg-black bg-opacity-80 text-white text-xs p-3 rounded-lg z-50 font-mono">
          <div className="font-bold mb-2">Debug Info:</div>
          {debugInfo.map((info, index) => (
            <div key={index} className="mb-1">{info}</div>
          ))}
          <button 
            onClick={() => setDebugInfo([])}
            className="mt-2 px-2 py-1 bg-red-600 text-white rounded text-xs"
          >
            Clear
          </button>
        </div>
      )}
      */}
      
      {renderWelcomeMessage()}
      <FileUpload onUploadSuccess={handleUploadSuccess} />
    </div>
  );
}

export default App;
