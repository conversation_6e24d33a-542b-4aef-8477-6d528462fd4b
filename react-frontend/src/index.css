@tailwind base;
@tailwind components;
@tailwind utilities;

/* Telegram Web App CSS Variables */
:root {
  /* Default theme colors (light theme) */
  --tg-theme-bg-color: #ffffff;
  --tg-theme-text-color: #000000;
  --tg-theme-hint-color: #999999;
  --tg-theme-link-color: #2481cc;
  --tg-theme-button-color: #2481cc;
  --tg-theme-button-text-color: #ffffff;
  --tg-theme-secondary-bg-color: #f1f1f1;
  --tg-theme-section-separator-color: #d7d8da;
  --tg-theme-section-bg-color: #ffffff;
  --tg-theme-section-header-text-color: #6d6d71;
  --tg-theme-subtitle-text-color: #999999;
  --tg-theme-destructive-text-color: #ff3b30;
  
  /* Safe area insets for mobile */
  --tg-safe-area-inset-top: 0px;
  --tg-safe-area-inset-bottom: 0px;
  --tg-safe-area-inset-left: 0px;
  --tg-safe-area-inset-right: 0px;
  
  /* Font settings */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Base body styles */
body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: var(--tg-theme-bg-color);
  color: var(--tg-theme-text-color);
  overflow-x: hidden;
  -webkit-tap-highlight-color: transparent;
}

/* Custom base styles */
@layer base {
  html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  body {
    background-color: var(--tg-theme-bg-color);
    color: var(--tg-theme-text-color);
  }
  
  /* Typography */
  h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--tg-theme-text-color);
    line-height: 1.25;
  }
  
  h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--tg-theme-text-color);
    line-height: 1.25;
  }
  
  h3 {
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--tg-theme-text-color);
    line-height: 1.25;
  }
  
  p {
    color: var(--tg-theme-text-color);
    line-height: 1.625;
  }
  
  /* Links */
  a {
    color: var(--tg-theme-link-color);
    transition: opacity 0.2s;
  }
  
  a:hover {
    opacity: 0.8;
  }
  
  /* Form elements */
  input, textarea, select {
    background-color: var(--tg-theme-bg-color);
    color: var(--tg-theme-text-color);
    border-color: var(--tg-theme-section-separator-color);
  }
  
  /* Buttons */
  button {
    font-weight: 500;
    transition: all 0.2s;
  }
  
  /* Scrollbar styling for webkit browsers */
  * {
    scrollbar-width: thin;
    scrollbar-color: var(--tg-theme-hint-color) transparent;
  }
  
  *::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  *::-webkit-scrollbar-track {
    background: transparent;
  }
  
  *::-webkit-scrollbar-thumb {
    background-color: var(--tg-theme-hint-color);
    border-radius: 2px;
  }
  
  *::-webkit-scrollbar-thumb:hover {
    background-color: var(--tg-theme-text-color);
  }
}

/* Custom component styles */
@layer components {
  /* Telegram-style card */
  .tg-card {
    background-color: var(--tg-theme-secondary-bg-color);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    border: 1px solid var(--tg-theme-section-separator-color);
  }
  
  /* Telegram-style button */
  .tg-button {
    background-color: var(--tg-theme-button-color);
    color: var(--tg-theme-button-text-color);
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-weight: 500;
    transition: opacity 0.2s;
    border: none;
    cursor: pointer;
  }
  
  .tg-button:hover {
    opacity: 0.9;
  }
  
  .tg-button:active {
    opacity: 0.8;
  }
  
  .tg-button-secondary {
    background-color: var(--tg-theme-secondary-bg-color);
    color: var(--tg-theme-text-color);
    border: 1px solid var(--tg-theme-section-separator-color);
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-weight: 500;
    transition: opacity 0.2s;
    cursor: pointer;
  }
  
  .tg-button-secondary:hover {
    opacity: 0.9;
  }
  
  .tg-button-secondary:active {
    opacity: 0.8;
  }
  
  /* Status indicators */
  .status-processing {
    background-color: #eff6ff;
    color: #1d4ed8;
    border: 1px solid #dbeafe;
  }
  
  .status-completed {
    background-color: #f0fdf4;
    color: #15803d;
    border: 1px solid #dcfce7;
  }
  
  .status-error {
    background-color: #fef2f2;
    color: #b91c1c;
    border: 1px solid #fee2e2;
  }
  
  .status-pending {
    background-color: #fffbeb;
    color: #b45309;
    border: 1px solid #fef3c7;
  }
  
  /* Loading animations */
  .loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid currentColor;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  /* File upload area */
  .upload-area {
    border: 2px dashed var(--tg-theme-section-separator-color);
    border-radius: 12px;
    transition: colors 0.2s;
  }
  
  .upload-area.drag-over {
    border-color: var(--tg-theme-link-color);
    background-color: #eff6ff;
  }
  
  /* Safe area adjustments */
  .safe-area-top {
    padding-top: var(--tg-safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: var(--tg-safe-area-inset-bottom);
  }
  
  .safe-area-left {
    padding-left: var(--tg-safe-area-inset-left);
  }
  
  .safe-area-right {
    padding-right: var(--tg-safe-area-inset-right);
  }
}

/* Utility classes */
@layer utilities {
  /* Text utilities */
  .text-hint {
    color: var(--tg-theme-hint-color);
  }
  
  .text-destructive {
    color: var(--tg-theme-destructive-text-color);
  }
  
  .text-subtitle {
    color: var(--tg-theme-subtitle-text-color);
  }
  
  .text-section-header {
    color: var(--tg-theme-section-header-text-color);
  }
  
  /* Background utilities */
  .bg-section {
    background-color: var(--tg-theme-section-bg-color);
  }
  
  /* Border utilities */
  .border-separator {
    border-color: var(--tg-theme-section-separator-color);
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-bounce-in {
    animation: bounceIn 0.4s ease-out;
  }
}

/* Keyframe animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes orbFill {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0%);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Instagram-style upload orb */
.upload-orb {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(240, 148, 51, 0.3);
}

.upload-orb::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888, #a53998, #833ab4, #5b51d8, #405de6);
  background-size: 200% 200%;
  border-radius: 50%;
  z-index: -1;
  animation: shimmer 3s linear infinite;
}

.upload-orb:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 40px rgba(240, 148, 51, 0.4);
}

.upload-orb-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 0%;
  border-radius: 50%;
  background: linear-gradient(180deg, #405de6 0%, #5b51d8 20%, #833ab4 40%, #a53998 60%, #bc1888 80%, #cc2366 100%);
  transition: height 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.upload-orb.uploading .upload-orb-fill {
  height: 100%;
  animation: orbFill 2s ease-in-out infinite alternate;
}

.upload-orb-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-orb.uploading {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
