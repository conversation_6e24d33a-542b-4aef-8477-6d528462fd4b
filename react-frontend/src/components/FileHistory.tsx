import React, { useState } from 'react';
import { FileTex<PERSON>, Co<PERSON>, Clock, CheckCircle, AlertCircle, Loader } from 'lucide-react';
import { type FileRecord, apiClient } from '../api/client';
import { useTelegramWebApp } from '../hooks/useTelegramWebApp';

interface FileHistoryProps {
  files: FileRecord[];
  isLoading: boolean;
}

export const FileHistory: React.FC<FileHistoryProps> = ({ files, isLoading }) => {
  const [loadingSummary, setLoadingSummary] = useState<string | null>(null);
  const [selectedSummary, setSelectedSummary] = useState<{
    file: FileRecord;
    content: string;
  } | null>(null);
  const { showAlert, hapticFeedback } = useTelegramWebApp();

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Б';
    const k = 1024;
    const sizes = ['Б', 'КБ', 'МБ', 'ГБ'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) {
      return 'Сегодня в ' + date.toLocaleTimeString('ru-RU', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffDays === 2) {
      return 'Вчера в ' + date.toLocaleTimeString('ru-RU', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else {
      return date.toLocaleDateString('ru-RU', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const getStatusInfo = (status: string) => {
    if (status === 'Готово') {
      return {
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        progress: 100
      };
    } else if (status.includes('Ошибка') || status.includes('ошибка')) {
      return {
        icon: AlertCircle,
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        progress: 0
      };
    } else {
      const match = status.match(/(\d+)%/);
      const progress = match ? parseInt(match[1]) : 0;
      return {
        icon: Loader,
        color: 'text-blue-600',
        bgColor: 'bg-blue-100',
        progress
      };
    }
  };

  const handleFileClick = async (file: FileRecord) => {
    if (file.status !== 'Готово') return;

    setLoadingSummary(file.id);
    hapticFeedback.impact('light');

    try {
      const summary = await apiClient.getSummary(file.id);
      setSelectedSummary({ file, content: summary });
    } catch (error) {
      // console.error('Error loading summary:', error);
      showAlert('Не удалось загрузить конспект');
      hapticFeedback.notification('error');
    } finally {
      setLoadingSummary(null);
    }
  };

  const handleCopySummary = async () => {
    if (!selectedSummary) return;

    try {
      await navigator.clipboard.writeText(selectedSummary.content);
      showAlert('Конспект скопирован в буфер обмена!');
      hapticFeedback.notification('success');
    } catch (error) {
      // console.error('Error copying to clipboard:', error);
      showAlert('Не удалось скопировать текст');
      hapticFeedback.notification('error');
    }
  };

  const closeSummaryModal = () => {
    setSelectedSummary(null);
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"></div>
        <p className="text-gray-600">Загрузка файлов...</p>
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <FileText className="h-8 w-8 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Нет загруженных файлов
        </h3>
        <p className="text-gray-500 max-w-sm">
          Загрузите свой первый аудио файл, чтобы получить конспект
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-3">
        {files.map((file) => {
          const statusInfo = getStatusInfo(file.status);
          const isProcessing = file.status !== 'Готово' && !file.status.includes('Ошибка');
          const isCompleted = file.status === 'Готово';
          const isError = file.status.includes('Ошибка') || file.status.includes('ошибка');
          
          return (
            <div
              key={file.id}
              className={`
                bg-white rounded-lg border border-gray-200 p-4 transition-all
                ${isCompleted ? 'hover:shadow-md cursor-pointer hover:border-blue-300' : ''}
                ${loadingSummary === file.id ? 'opacity-50' : ''}
              `}
              onClick={() => isCompleted && handleFileClick(file)}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 truncate">
                    {file.original_filename || file.filename}
                  </h4>
                  <p className="text-xs text-gray-500 mt-1">
                    {formatFileSize(file.file_size)}
                  </p>
                </div>
                
                <div className={`flex items-center space-x-1 ${statusInfo.bgColor} px-2 py-1 rounded-full`}>
                  <statusInfo.icon className={`h-3 w-3 ${statusInfo.color} ${isProcessing ? 'animate-spin' : ''}`} />
                  <span className={`text-xs font-medium ${statusInfo.color}`}>
                    {isCompleted ? 'Готово' : isError ? 'Ошибка' : file.status}
                  </span>
                </div>
              </div>

              {isProcessing && (
                <div className="mb-3">
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div 
                      className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                      style={{ width: `${statusInfo.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center text-xs text-gray-500">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDate(file.created_at)}
                </div>
                
                {isCompleted && (
                  <div className="text-xs text-blue-600 font-medium">
                    Нажмите для просмотра →
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary Modal */}
      {selectedSummary && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] flex flex-col">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 truncate mr-4">
                {selectedSummary.file.original_filename || selectedSummary.file.filename}
              </h3>
              <button
                onClick={closeSummaryModal}
                className="text-gray-400 hover:text-gray-600 text-2xl leading-none"
              >
                ×
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4">
              <pre className="whitespace-pre-wrap text-sm text-gray-800 font-sans leading-relaxed">
                {selectedSummary.content}
              </pre>
            </div>
            
            <div className="p-4 border-t border-gray-200 flex space-x-3">
              <button
                onClick={handleCopySummary}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
              >
                <Copy className="h-4 w-4" />
                <span>Копировать</span>
              </button>
              <button
                onClick={closeSummaryModal}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors"
              >
                Закрыть
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};