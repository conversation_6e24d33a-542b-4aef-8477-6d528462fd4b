import React, { useRef, useState } from 'react';
import { Upload } from 'lucide-react';
import { apiClient } from '../api/client';
import { useTelegramWebApp } from '../hooks/useTelegramWebApp';

interface FileUploadProps {
  onUploadSuccess: () => void;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onUploadSuccess }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { showAlert, hapticFeedback } = useTelegramWebApp();

  const validateFile = (file: File): string | null => {
    const maxSize = 300 * 1024 * 1024; // 300MB
    const allowedTypes = ['audio/', 'video/'];
    
    if (file.size > maxSize) {
      return 'Размер файла не должен превышать 300 МБ';
    }
    
    if (!allowedTypes.some(type => file.type.startsWith(type))) {
      return 'Поддерживаются только аудио и видео файлы';
    }
    
    return null;
  };

  const handleFileUpload = async (file: File) => {
    const error = validateFile(file);
    if (error) {
      showAlert(error);
      hapticFeedback.notification('error');
      return;
    }

    setIsUploading(true);
    hapticFeedback.impact('light');

    try {
      await apiClient.uploadFile(file);
      showAlert('Файл успешно загружен и поставлен в обработку!');
      hapticFeedback.notification('success');
      onUploadSuccess();
    } catch (error: any) {
      // console.error('Upload error:', error);
      
      let errorMessage = 'Не удалось загрузить файл';
      
      if (error.response?.status === 413) {
        errorMessage = 'Файл слишком большой (максимум 300 МБ)';
      } else if (error.response?.status === 429) {
        errorMessage = 'Превышена квота хранилища (1 ГБ на пользователя)';
      } else if (error.response?.status === 400) {
        errorMessage = 'Неподдерживаемый формат файла';
      } else if (error.response?.status === 401) {
        errorMessage = 'Ошибка авторизации. Перезапустите приложение.';
      }
      
      showAlert(errorMessage);
      hapticFeedback.notification('error');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
    e.target.value = '';
  };

  const openFilePicker = () => {
    fileInputRef.current?.click();
  };


  return (
    <div className="flex items-center justify-center min-h-screen">
      <input
        ref={fileInputRef}
        type="file"
        accept="audio/*,video/*"
        onChange={handleFileSelect}
        className="hidden"
      />
      
      <div
        className={`
          ${isDragging ? 'scale-110' : ''} 
          ${isUploading ? 'pointer-events-none' : 'cursor-pointer'}
          transition-transform duration-200
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFilePicker}
      >
        <div className={`upload-orb ${isUploading ? 'uploading' : ''}`}>
          <div className="upload-orb-fill"></div>
          <div className="upload-orb-content">
            {!isUploading && <Upload className="h-8 w-8 text-white" />}
          </div>
        </div>
      </div>
    </div>
  );
};