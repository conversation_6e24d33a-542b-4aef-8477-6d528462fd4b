import axios, { type AxiosInstance } from 'axios';

export interface User {
  id: string;
  telegram_id: number;
  username?: string;
  created_at: string;
  first_name?: string;
}

export interface UserStatus {
  is_first_time: boolean;
  has_files: boolean;
  files_count: number;
  user: {
    id: string;
    telegram_id: number;
    username?: string;
    first_name?: string;
  };
}

export interface FileRecord {
  id: string;
  filename: string;
  original_filename?: string;
  file_size: number;
  status: string;
  created_at: string;
}

export interface UploadResponse {
  id: string;
  filename: string;
  original_filename: string;
  file_size: number;
  status: string;
  created_at: string;
}

class APIClient {
  private client: AxiosInstance;
  private initData: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: '/api/v1',
      timeout: 30000,
    });

    this.client.interceptors.request.use((config) => {
      if (this.initData) {
        config.headers.Authorization = `Bearer ${this.initData}`;
      }
      return config;
    });

    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        // console.error('API Error:', error.response?.data || error.message);
        throw error;
      }
    );
  }

  setInitData(initData: string) {
    this.initData = initData;
    // console.log('🔑 API Client: Set initData:', initData ? 'present' : 'missing');
  }

  async getUserInfo(): Promise<User> {
    const response = await this.client.get<User>('/users/me');
    return response.data;
  }

  async getUserStatus(): Promise<UserStatus> {
    const response = await this.client.get<UserStatus>('/user-status');
    return response.data;
  }

  async getHistory(): Promise<FileRecord[]> {
    const response = await this.client.get<FileRecord[]>('/history');
    return response.data;
  }

  async uploadFile(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.client.post<UploadResponse>('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async getSummary(summaryId: string): Promise<string> {
    const response = await this.client.get<string>(`/summary/${summaryId}`);
    return response.data;
  }

  async debugTest(): Promise<any> {
    const response = await this.client.get('/debug/test');
    return response.data;
  }
}

export const apiClient = new APIClient();