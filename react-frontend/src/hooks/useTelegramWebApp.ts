import { useEffect, useState } from 'react';

declare global {
  interface Window {
    Telegram?: {
      WebApp: {
        initData: string;
        initDataUnsafe: any;
        ready: () => void;
        expand: () => void;
        close: () => void;
        showAlert: (message: string) => void;
        showConfirm: (message: string, callback: (confirmed: boolean) => void) => void;
        HapticFeedback?: {
          impactOccurred: (style: 'light' | 'medium' | 'heavy') => void;
          notificationOccurred: (type: 'error' | 'success' | 'warning') => void;
        };
        MainButton?: {
          text: string;
          color: string;
          textColor: string;
          isVisible: boolean;
          isActive: boolean;
          show: () => void;
          hide: () => void;
          enable: () => void;
          disable: () => void;
          onClick: (callback: () => void) => void;
          offClick: (callback: () => void) => void;
        };
        platform: string;
        version: string;
        setHeaderColor?: (color: string) => void;
        themeParams: {
          bg_color?: string;
          text_color?: string;
          hint_color?: string;
          link_color?: string;
          button_color?: string;
          button_text_color?: string;
          secondary_bg_color?: string;
          section_separator_color?: string;
        };
      };
    };
  }
}

interface TelegramWebAppState {
  isReady: boolean;
  initData: string | null;
  user: any;
  error: string | null;
}

export function useTelegramWebApp(): TelegramWebAppState & {
  showAlert: (message: string) => void;
  showConfirm: (message: string) => Promise<boolean>;
  hapticFeedback: {
    impact: (style?: 'light' | 'medium' | 'heavy') => void;
    notification: (type: 'error' | 'success' | 'warning') => void;
  };
} {
  const [state, setState] = useState<TelegramWebAppState>({
    isReady: false,
    initData: null,
    user: null,
    error: null,
  });

  useEffect(() => {
    const webApp = window.Telegram?.WebApp;

    // DEBUG: Временная заглушка для разработки
    if (!webApp && process.env.NODE_ENV === 'development') {
      // console.warn('Debug mode: No Telegram WebApp detected, using fake data');
      setState({
        isReady: true,
        initData: 'debug_mode_fake_init_data',
        user: { first_name: 'Debug User', id: 12345 },
        error: null,
      });
      return;
    }

    if (!webApp) {
      setState(prev => ({
        ...prev,
        error: 'Приложение должно быть запущено в Telegram'
      }));
      return;
    }

    try {
      webApp.ready();
      webApp.expand();

      if (!webApp.initData) {
        setState(prev => ({
          ...prev,
          error: 'Отсутствуют данные авторизации Telegram'
        }));
        return;
      }

      setState({
        isReady: true,
        initData: webApp.initData,
        user: webApp.initDataUnsafe?.user || null,
        error: null,
      });

      // Применяем тему Telegram и настраиваем базовые стили
      if (webApp.themeParams) {
        const root = document.documentElement;
        const body = document.body;
        
        // Применяем все доступные цвета темы
        Object.entries(webApp.themeParams).forEach(([key, value]) => {
          if (value) {
            root.style.setProperty(`--tg-theme-${key.replace(/_/g, '-')}`, value);
          }
        });

        // Устанавливаем основные стили для body
        if (webApp.themeParams.bg_color) {
          body.style.backgroundColor = webApp.themeParams.bg_color;
        }
        
        if (webApp.themeParams.text_color) {
          body.style.color = webApp.themeParams.text_color;
        }

        // Добавляем класс для темной темы (если фон темный)
        if (webApp.themeParams.bg_color) {
          const bgColor = webApp.themeParams.bg_color;
          const rgb = parseInt(bgColor.slice(1), 16);
          const r = (rgb >> 16) & 255;
          const g = (rgb >> 8) & 255;
          const b = rgb & 255;
          const brightness = (r * 299 + g * 587 + b * 114) / 1000;
          
          if (brightness < 128) {
            body.classList.add('dark-theme');
          } else {
            body.classList.remove('dark-theme');
          }
        }

        // Настраиваем viewport для мобильных устройств
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport) {
          viewport.setAttribute('content', 
            'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
          );
        }
      }

      // Дополнительные настройки для Telegram Web App
      webApp.expand();
      
      // Скрываем главную кнопку, если она есть
      if (webApp.MainButton) {
        webApp.MainButton.hide();
      }

      // Настраиваем цвет заголовка
      if (webApp.setHeaderColor && webApp.themeParams.bg_color) {
        webApp.setHeaderColor(webApp.themeParams.bg_color);
      }

    } catch (error) {
      setState(prev => ({
        ...prev,
        error: `Ошибка инициализации: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`
      }));
    }
  }, []);

  const showAlert = (message: string) => {
    if (window.Telegram?.WebApp?.showAlert) {
      window.Telegram.WebApp.showAlert(message);
    } else {
      alert(message);
    }
  };

  const showConfirm = (message: string): Promise<boolean> => {
    return new Promise((resolve) => {
      if (window.Telegram?.WebApp?.showConfirm) {
        window.Telegram.WebApp.showConfirm(message, resolve);
      } else {
        resolve(confirm(message));
      }
    });
  };

  const hapticFeedback = {
    impact: (style: 'light' | 'medium' | 'heavy' = 'medium') => {
      window.Telegram?.WebApp?.HapticFeedback?.impactOccurred(style);
    },
    notification: (type: 'error' | 'success' | 'warning') => {
      window.Telegram?.WebApp?.HapticFeedback?.notificationOccurred(type);
    },
  };

  return {
    ...state,
    showAlert,
    showConfirm,
    hapticFeedback,
  };
}