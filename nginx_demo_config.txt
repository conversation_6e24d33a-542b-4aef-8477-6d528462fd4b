server {
    listen 3000 ssl http2;
    listen [::]:3000 ssl http2;
    
    server_name med-dev-systems.ru www.med-dev-systems.ru api.med-dev-systems.ru;
    
    # SSL Configuration
    ssl_certificate /etc/ssl/certs/med-dev-systems.ru.crt;
    ssl_certificate_key /etc/ssl/private/med-dev-systems.ru.key;
    
    # SSL Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Serve static demo page
    location / {
        root /var/www/med-dev-systems.ru;
        index index.html;
        try_files $uri $uri/ =404;
    }
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    server_name med-dev-systems.ru www.med-dev-systems.ru api.med-dev-systems.ru;
    return 301 https://$server_name:3000$request_uri;
}